import type { BasicFetchResult, BasicPageParams } from '#/api/types/common';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

import { preserveBigInt } from '../../../utils/json-bigint';
import { ensureMeterIdString, preserveIdPrecision } from '../meter/index';

// 机械水表接口API
enum Api {
  advancedSearch = '/waterfee/meter/mechanical/advanced-search',
  detail = '/waterfee/meter/mechanical',
  export = '/waterfee/meter/mechanical/export',
  list = '/waterfee/meter/mechanical/list',
  statistics = '/waterfee/meter/mechanical/statistics',
}

// 机械水表模型
export interface MechanicalMeterModel {
  meterId: string;
  meterNo: string;
  reading: number;
  lastReadDate?: string;
  readCycle?: string;
  readerName?: string;
  installAddress?: string;
  businessAreaName?: string;
  meterBookName?: string;
  [key: string]: any;
}

// 机械水表详情模型
export interface MechanicalMeterDetailModel {
  meterInfo: {
    [key: string]: any;
    accuracy?: string;
    businessAreaName?: string;
    caliber?: string;
    createTime?: string;
    installLocation?: string;
    manufacturer?: string;
    meterBookName?: string;
    meterId: string;
    meterNo: string;
    updateTime?: string;
    userName?: string;
    userNo?: string;
  };
  replacementRecord?: Record<string, any>;
  readingRecord?: Record<string, any>;
  historyReadings?: Record<string, any>[];
}

// 机械水表查询参数
export interface MechanicalMeterParams extends BasicPageParams {
  meterNo?: string;
  caliber?: string;
  manufacturer?: string;
  installDateRange?: [string, string];
  userNo?: string;
  userName?: string;
  businessAreaId?: string;
  [key: string]: any;
}

// 获取机械水表列表
export function getMechanicalMeterList(params?: MechanicalMeterParams) {
  // 处理查询参数
  const queryParams = params ? { ...params } : {};

  // 处理日期范围
  if (
    queryParams.installDateRange &&
    Array.isArray(queryParams.installDateRange)
  ) {
    queryParams.installDateStart = queryParams.installDateRange[0];
    queryParams.installDateEnd = queryParams.installDateRange[1];
    delete queryParams.installDateRange;
  }

  // 处理ID
  if (queryParams.businessAreaId) {
    queryParams.businessAreaId = ensureMeterIdString(
      queryParams.businessAreaId || '', // Ensure a string is passed
    );
  }

  // 保留大整数精度
  const safeParams = preserveIdPrecision(queryParams);

  return requestClient.get<BasicFetchResult<MechanicalMeterModel[]>>(Api.list, {
    params: safeParams,
  });
}

// 获取机械水表详情
export function getMechanicalMeterInfo(meterId: string) {
  // 移除非数字字符，确保 meterId 是有效的数字字符串
  const cleanMeterId = String(meterId || '').replaceAll(/\D/g, '');
  const safeMeterId = ensureMeterIdString(cleanMeterId);
  console.log('获取机械水表详情, 原始ID:', meterId, '处理后ID:', safeMeterId);
  return requestClient.get<MechanicalMeterDetailModel>(
    `${Api.detail}/${safeMeterId}`,
  );
}

// 高级查询机械水表
export function advancedSearchMechanicalMeter(params: MechanicalMeterParams) {
  // 保留大整数精度
  const safeParams = preserveIdPrecision(params);

  return requestClient.post<BasicFetchResult<MechanicalMeterModel[]>>(
    Api.advancedSearch,
    safeParams,
  );
}

// 获取统计数据
export function getMechanicalMeterStatistics(params?: MechanicalMeterParams) {
  // 处理查询参数
  const queryParams = params ? { ...params } : {};

  // 保留大整数精度
  const safeParams = preserveIdPrecision(queryParams);

  return requestClient.get<Record<string, any>>(Api.statistics, {
    params: safeParams,
  });
}

// 导出机械水表数据
export function exportMechanicalMeter(params?: MechanicalMeterParams) {
  // 确保params是一个对象，防止undefined导致错误
  const safeParams = params ? preserveBigInt(params) : {};
  return commonExport(Api.export, safeParams);
}
