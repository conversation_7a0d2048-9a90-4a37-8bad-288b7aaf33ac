import type { BasicFetchResult, BasicPageParams } from '#/api/types/common';
import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';
import { preserveBigInt } from '#/utils/json-bigint';
import { ensureMeterIdString } from './index';

// 水表换表API
enum Api {
  list = '/waterfee/meter/change/list',
  detail = '/waterfee/meter/change',
  add = '/waterfee/meter/change',
  update = '/waterfee/meter/change',
  delete = '/waterfee/meter/change',
  export = '/waterfee/meter/change/export',
  byMeter = '/waterfee/meter/change/meter',
  byMeterNo = '/waterfee/meter/change/meter',
}

// 换表记录查询参数
export interface MeterChangeParams extends BasicPageParams {
  meterNo?: string;
  oldMeterNo?: string;
  newMeterNo?: string;
  startTime?: string;
  endTime?: string;
  [key: string]: any;
}

// 换表记录模型
export interface MeterChangeModel {
  changeId?: string;
  userId?: string;
  userNo?: string;
  userName?: string;
  oldMeterId?: string;
  newMeterId?: string;
  oldMeterNo?: string;
  newMeterNo?: string;
  changeType?: string;
  changeReason?: string;
  oldMeterReading?: number;
  newMeterReading?: number;
  changeTime?: string;
  operator?: string;
  auditStatus?: string;
  auditTime?: string;
  auditor?: string;
  remark?: string;
  meterCategory?: string;
  valveSupport?: string;
  [key: string]: any;
}

// 换表表单模型
export interface MeterChangeFormModel {
  oldMeterId?: string;
  oldMeterNo?: string;
  oldMeterReading?: number;
  newMeterId?: string;
  newMeterNo?: string;
  newMeterReading?: number;
  changeTime?: string;
  changeType?: string;
  changeReason?: string;
  operator?: string;
  remark?: string;
  [key: string]: any;
}

// 获取换表记录列表
export function getMeterChangeList(params?: MeterChangeParams) {
  // 处理查询参数
  const queryParams = params ? { ...params } : {};

  // 保留大整数精度
  const safeParams = preserveBigInt(queryParams);

  return requestClient.get<BasicFetchResult<MeterChangeModel[]>>(Api.list, {
    params: safeParams,
  });
}

// 获取换表记录详情
export function getMeterChangeDetail(changeId: string) {
  const safeId = ensureMeterIdString(changeId);
  return requestClient.get<MeterChangeModel>(`${Api.detail}/${safeId}`);
}

// 添加换表记录
export function addMeterChange(data: MeterChangeFormModel) {
  return requestClient.post(Api.add, preserveBigInt(data));
}

// 更新换表记录
export function updateMeterChange(data: MeterChangeFormModel) {
  return requestClient.put(Api.update, preserveBigInt(data));
}

// 删除换表记录
export function deleteMeterChange(changeId: string) {
  const safeId = ensureMeterIdString(changeId);
  return requestClient.delete(`${Api.delete}/${safeId}`);
}

// 导出换表记录
export function exportMeterChange(params?: MeterChangeParams) {
  const safeParams = params ? preserveBigInt(params) : {};
  return commonExport(Api.export, safeParams);
}

// 根据水表ID获取换表记录
export function getMeterChangeByMeterId(meterId: string) {
  const safeId = ensureMeterIdString(meterId);
  return requestClient.get<MeterChangeModel[]>(`${Api.byMeter}/${safeId}`);
}

// 根据水表编号获取换表记录
export function getMeterChangeByMeterNo(meterNo: string) {
  return requestClient.get<MeterChangeModel[]>(`${Api.byMeterNo}/${meterNo}`);
}
