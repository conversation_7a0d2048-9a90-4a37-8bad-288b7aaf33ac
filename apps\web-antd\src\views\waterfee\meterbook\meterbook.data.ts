import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { getPopupContainer } from '@vben/utils';
import { h } from 'vue';
import { Tag } from 'ant-design-vue';
import { getDictOptions } from '#/utils/dict';
import { renderDict } from '#/utils/render';

// Query form schema
export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'bookNo',
    label: '手册编号',
  },
  {
    component: 'Select',
    componentProps: {
      getPopupContainer,
      options: getDictOptions('meter_reading_type'),
    },
    fieldName: 'readType',
    label: '抄表方式',
  },
  {
    component: 'Select',
    componentProps: {
      getPopupContainer,
      options: getDictOptions('meter_reading_cycle'),
    },
    fieldName: 'readCycle',
    label: '抄表周期',
  },
];

// Table columns definition
export const columns: VxeGridProps['columns'] = [
  {
    title: '序号',
    field: 'id',
    width: 220,
  },
  {
    title: '管辖区域',
    field: 'areaName',  
    // width: 120,
    align: 'left',
  },
  {
    title: '手册编号',
    field: 'bookNo',
    // width: 120,
    align: 'left',
  },
  {
    title: '手册名称',
    field: 'bookName',
    // width: 150,
    align: 'left',
  },
  {
    title: '抄表方式',
    field: 'readType',
    // width: 120,
    align: 'left',
    slots: {
      default: ({ row }) => {
        return renderDict(row.readType, 'meter_reading_type');
      },
    },
  },
  {
    title: '抄表周期',
    field: 'readCycle',
    // width: 120,
    align: 'left',
    slots: {
      default: ({ row }) => {
        return renderDict(row.readCycle, 'meter_reading_cycle');
      },
    },
  },
  {
    title: '抄表例日',
    field: 'readDay',
    // width: 100,
    align: 'center',
  },
  {
    title: '抄表基准日',
    field: 'readBaseDay',
    // width: 100,
    align: 'center',
  },
  {
    title: '抄表员',
    field: 'readerName',
    // width: 100,
    align: 'left',
  },
  {
    title: '抄表组长',
    field: 'readerLeaderName',
    // width: 100,
    align: 'left',
  },
  {
    title: '创建人',
    field: 'createBy',
    // width: 100,
    align: 'left',
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 200,
  },
];


// Form schema for the drawer
export const formSchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
    fieldName: 'id',
    label: 'ID',
  },
  {
    component: 'TreeSelect',
    componentProps: {
      fieldNames: { label: 'areaName', value: 'areaId' },
      treeData: [],
      placeholder: '请选择管辖区域',
    },
    fieldName: 'areaId',
    label: '管辖区域',
    rules: 'required',
  },
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入手册编号',
    },
    fieldName: 'bookNo',
    label: '手册编号',
    rules: 'required',
  },
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入手册名称',
    },
    fieldName: 'bookName',
    label: '手册名称',
    rules: 'required',
  },
  {
    component: 'Select',
    componentProps: {
      getPopupContainer,
      options: getDictOptions('meter_reading_type'),
    },
    fieldName: 'readType',
    label: '抄表方式',
    rules: 'required',
  },
  {
    component: 'Select',
    componentProps: {
      getPopupContainer,
      options: getDictOptions('meter_reading_cycle'),
    },
    fieldName: 'readCycle',
    label: '抄表周期',
    rules: 'required',
  },
  {
    component: 'InputNumber',
    componentProps: {
      min: 1,
      max: 31,
    },
    fieldName: 'readDay',
    label: '抄表例日',
    rules: 'required',
  },
  {
    component: 'InputNumber',
    componentProps: {
      min: 1,
      max: 31,
    },
    fieldName: 'readBaseDay',
    label: '抄表基准日',
    rules: 'required',
  },
  {
    component: 'Select',
    componentProps: {
      options: [],
      placeholder: '请选择抄表员',
    },
    fieldName: 'reader',
    label: '抄表员',
    rules: 'required',
  },
  {
    component: 'Select',
    componentProps: {
      options: [],
      placeholder: '请选择抄表组长',
    },
    fieldName: 'readerLeader',
    label: '抄表组长',
  },
];
