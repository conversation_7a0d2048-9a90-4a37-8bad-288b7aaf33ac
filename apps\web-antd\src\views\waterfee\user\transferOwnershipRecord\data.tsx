import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { getPopupContainer } from '@vben/utils';

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'userNoOrUserName',
    label: '关键字',
    componentProps: {
      placeholder: '请输入用户编号或名称',
      allowClear: true,
    },
  },
  {
    component: 'RangePicker',
    fieldName: 'timeRange',
    label: '创建时间',
    componentProps: {
      getPopupContainer,
      placeholder: ['开始日期', '结束日期'],
      valueFormat: 'YYYY-MM-DD',
      allowClear: true,
    },
  },
];

export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
  {
    title: '用户编号',
    field: 'userNo',
  },
  {
    title: '原用户名',
    field: 'beforeUserName',
  },
  {
    title: '原证件类型',
    field: 'beforeCertificateType',
  },
  {
    title: '原证件号码',
    field: 'beforeCertificateNumber',
  },
  {
    title: '原手机号码',
    field: 'beforePhoneNumber',
  },
  {
    title: '原用水人数',
    field: 'beforeUseWaterNumber',
  },
  // {
  //   title: '原电子邮箱',
  //   field: 'beforeEmail',
  // },
  {
    title: '新用户名',
    field: 'afterUserName',
  },
  {
    title: '新证件类型',
    field: 'afterCertificateType',
  },
  {
    title: '新证件号码',
    field: 'afterCertificateNumber',
  },
  {
    title: '新手机号码',
    field: 'afterPhoneNumber',
  },
  {
    title: '新用水人数',
    field: 'afterUseWaterNumber',
  },
  // {
  //   title: '新电子邮箱',
  //   field: 'afterEmail',
  // },
  {
    title: '处理人',
    field: 'createByUserName',
  },
  {
    title: '处理日期',
    field: 'createTime',
  },
];

export const drawerSchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
    fieldName: 'transferId',
    label: '主键',
  },
  {
    component: 'Input',
    fieldName: 'userId',
    label: '用户ID',
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'beforeUseWaterNumber',
    label: '原用水人数',
  },
  {
    component: 'Input',
    fieldName: 'beforeUserName',
    label: '原用水户名称',
  },
  {
    component: 'Input',
    fieldName: 'beforePhoneNumber',
    label: '原手机号码',
  },
  {
    component: 'Input',
    fieldName: 'beforeCertificateNumber',
    label: '原证件号码',
  },
  {
    component: 'Input',
    fieldName: 'beforeEmail',
    label: '原电子邮箱',
  },
  {
    component: 'Input',
    fieldName: 'afterUseWaterNumber',
    label: '新用水人数',
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'afterUserName',
    label: '新用水户名称',
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'afterPhoneNumber',
    label: '新手机号码',
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'afterCertificateNumber',
    label: '新证件号码',
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'afterEmail',
    label: '新电子邮箱',
  },
];
