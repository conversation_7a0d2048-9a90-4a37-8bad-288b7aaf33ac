<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';

import { Page, useVbenDrawer } from '@vben/common-ui';
import { getVxePopupContainer } from '@vben/utils';

import { Button, message, Popconfirm, Space } from 'ant-design-vue';
import { DownloadOutlined } from '@ant-design/icons-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getMeterChangeList, exportMeterChange, deleteMeterChange } from '#/api/waterfee/meter/change';
import { preserveBigInt } from '#/utils/json-bigint';
import { commonDownloadExcel } from '#/utils/file/download';

import { columns, querySchema } from './change.data';
import MeterChangeDrawer from './components/MeterChangeDrawer.vue';

// 表单配置
const formOptions = {
  commonConfig: {
    componentProps: {
      allowClear: true,
    },
  },
  schema: querySchema(),
  fieldMappingTime: [['dateRange', ['startTime', 'endTime'], 'YYYY-MM-DD HH:mm:ss']],
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
};

// 表格配置
const gridOptions = {
  columns,
  height: 'auto',
  width: '100%',
  fit: true,
  autoResize: true,
  tableLayout: 'auto',
  scrollX: {
    enabled: true,
    gt: 10,
  },
  resizable: true,
  resizeConfig: {
    minWidth: 80,
  },
  tooltipConfig: {
    showAll: true,
  },
  proxyConfig: {
    ajax: {
      query: async ({ page }: { page?: { currentPage: number; pageSize: number } }, formValues: Record<string, any> = {}) => {
        const resp = await getMeterChangeList({
          pageNum: page?.currentPage,
          pageSize: page?.pageSize,
          ...formValues,
        });
        return resp;
      },
    },
  },
  id: 'waterfee-meter-change-index',
};

// 初始化表格
const [BasicTable, tableApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

// 路由实例
const router = useRouter();

// 抽屉状态
const drawerOpen = ref(false);
const drawerProps = ref({
  id: '',
  readonly: false,
});

// 添加换表记录
function handleAdd() {
  // 跳转到添加页面
  router.push('/meterManagement/addChange');
}

// 处理成功回调
function handleSuccess() {
  tableApi.query();
}
</script>

<template>
  <Page :auto-content-height="true">
    <BasicTable @reload="tableApi.query()" table-title="水表换表管理">
      <template #toolbar-tools>
        <Space>
          <Button type="link" @click="handleAdd">
            添加换表记录
          </Button>
        </Space>
      </template>
    </BasicTable>

    <!-- 换表抽屉 -->
    <MeterChangeDrawer
      v-if="drawerOpen"
      v-model:open="drawerOpen"
      :id="drawerProps.id"
      :readonly="drawerProps.readonly"
      @reload="handleSuccess"
    />
  </Page>
</template>

<style scoped>
/* 可以添加自定义样式 */
:deep(.vben-vxe-grid-toolbar) {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

:deep(.vben-vxe-grid-toolbar-left) {
  flex: 1;
}

/* 确保表格容器能够撑满可用宽度 */
:deep(.vxe-table--main-wrapper),
:deep(.vxe-grid--main-wrapper) {
  width: 100% !important;
}

/* 表格内容区自适应 */
:deep(.vxe-table--body-wrapper) {
  width: 100% !important;
}

/* 表格头部自适应 */
:deep(.vxe-table--header-wrapper) {
  width: 100% !important;
}

/* 确保表格行内容能够正确显示 */
:deep(.vxe-body--row) {
  width: 100%;
}
</style>
