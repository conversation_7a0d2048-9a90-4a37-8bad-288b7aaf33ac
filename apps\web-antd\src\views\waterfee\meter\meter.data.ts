import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { getPopupContainer } from '@vben/utils';

import { getDictOptions } from '#/utils/dict';
import { renderDict } from '#/utils/render';

// 字典值常量 - 从数据库获取而不是硬编码
const meterTypeDict = {
  MECHANICAL: 1, // 机械表
  INTELLIGENT: 2, // 智能表
};

const valveControlDict = {
  NO: 0, // 无阀控
  YES: 1, // 有阀控
};

const prepaidDict = {
  NO: 0, // 非预付费
  YES: 1, // 预付费
};

// 查询表单架构
export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'meterNo',
    label: '水表编号',
  },
  {
    component: 'Select',
    componentProps: {
      getPopupContainer,
      options: getDictOptions('waterfee_meter_type'),
    },
    fieldName: 'meterType',
    label: '水表类型',
  },
  {
    component: 'Select',
    componentProps: {
      getPopupContainer,
      options: getDictOptions('dnmm'),
    },
    fieldName: 'caliber',
    label: '水表口径',
  },
  {
    component: 'Select',
    componentProps: {
      getPopupContainer,
      options: getDictOptions('meter_factory'),
    },
    fieldName: 'manufacturer',
    label: '水表厂家',
  },
  {
    component: 'RangePicker',
    componentProps: {
      getPopupContainer,
    },
    fieldName: 'installDateRange',
    label: '安装日期',
  },
  {
    component: 'Input',
    fieldName: 'installAddressLike',
    label: '安装地址',
  },
];

// 表格列定义
export const columns: VxeGridProps['columns'] = [
  {
    title: '序号',
    width: 60,
    cellRender: { name: 'SafeIndex' },
  },
  {
    title: '水表编号',
    field: 'meterNo',
    minWidth: 120,
    align: 'left',
    sortable: true,
  },
  {
    title: '水表类型',
    field: 'meterType',
    minWidth: 100,
    align: 'center',
    slots: {
      default: ({ row }) => {
        return renderDict(row.meterType, 'waterfee_meter_type');
      },
    },
  },
  {
    title: '水表口径',
    field: 'caliber',
    minWidth: 100,
    align: 'center',
    slots: {
      default: ({ row }) => {
        return renderDict(row.caliber, 'dnmm');
      },
    },
  },
  {
    title: '水表厂家',
    field: 'manufacturer',
    minWidth: 120,
    align: 'left',
    slots: {
      default: ({ row }) => {
        return renderDict(row.manufacturer, 'meter_factory');
      },
    },
  },
  {
    title: '业务区域',
    field: 'businessAreaName',
    minWidth: 130,
    align: 'left',
    slots: {
      default: ({ row }) => {
        return row.businessAreaName || '未分配';
      },
    },
  },
  {
    title: '抄表手册',
    field: 'meterBookName',
    minWidth: 130,
    align: 'left',
    slots: {
      default: ({ row }) => {
        return row.meterBookName || '未分配';
      },
    },
  },
  {
    title: '安装日期',
    field: 'installDate',
    minWidth: 120,
    align: 'center',
    sortable: true,
  },
  {
    title: '安装地址',
    field: 'installAddress',
    minWidth: 180,
    align: 'left',
  },
  {
    title: '初始读数',
    field: 'initialReading',
    minWidth: 100,
    align: 'right',
  },
  {
    title: '关联用户',
    field: 'userNo',
    minWidth: 130,
    align: 'left',
    slots: {
      default: ({ row }) => {
        if (!row.userNo) {
          return '未关联';
        }
        return row.userName ? `${row.userName}(${row.userNo})` : row.userNo;
      },
    },
  },
  {
    title: '创建时间',
    field: 'createTime',
    minWidth: 140,
    align: 'center',
    sortable: true,
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    minWidth: 300,
  },
];

// 机械表表单架构
export const mechanicalMeterFormSchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
    fieldName: 'meterId',
    label: 'ID',
  },
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入水表编号',
    },
    fieldName: 'meterNo',
    label: '水表编号',
    rules: 'required',
  },
  {
    component: 'Input',
    componentProps: {
      hidden: true,
      value: meterTypeDict.MECHANICAL,
    },
    fieldName: 'meterType',
    label: '水表类型',
  },
  {
    component: 'Select',
    componentProps: {
      getPopupContainer,
      options: getDictOptions('meter_factory'),
      placeholder: '请选择水表厂家',
    },
    fieldName: 'manufacturer',
    label: '水表厂家',
    rules: 'required',
  },
  {
    component: 'Select',
    componentProps: {
      getPopupContainer,
      options: getDictOptions('waterfee_meter_category'),
      placeholder: '请选择水表类别',
    },
    fieldName: 'meterCategory',
    label: '水表类别',
    rules: 'required',
  },
  {
    component: 'Select',
    componentProps: {
      getPopupContainer,
      options: getDictOptions('waterfee_meter_classification'),
      placeholder: '请选择水表分类',
    },
    fieldName: 'meterClassification',
    label: '水表分类',
  },
  {
    component: 'Select',
    componentProps: {
      getPopupContainer,
      options: getDictOptions('dnmm'),
      placeholder: '请选择口径',
    },
    fieldName: 'caliber',
    label: '口径',
    rules: 'required',
  },
  {
    component: 'Select',
    componentProps: {
      getPopupContainer,
      options: getDictOptions('meter_accuracy'),
      placeholder: '请选择精度',
    },
    fieldName: 'accuracy',
    label: '精度',
  },
  {
    component: 'InputNumber',
    componentProps: {
      min: 0,
      precision: 3,
      placeholder: '请输入初始读数',
    },
    fieldName: 'initialReading',
    label: '初始读数',
    rules: 'required',
  },
  {
    component: 'DatePicker',
    componentProps: {
      getPopupContainer,
      placeholder: '请选择安装日期',
    },
    fieldName: 'installDate',
    label: '安装日期',
    rules: 'required',
  },
  {
    component: 'TreeSelect',
    componentProps: {
      fieldNames: { label: 'areaName', value: 'areaId' },
      treeData: [],
      placeholder: '请选择业务区域',
    },
    fieldName: 'businessAreaId',
    label: '业务区域',
    rules: 'required',
  },
  {
    component: 'Select',
    componentProps: {
      options: [],
      placeholder: '请选择抄表手册',
    },
    fieldName: 'meterBookId',
    label: '抄表手册',
    rules: 'required',
  },
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入安装地址',
    },
    fieldName: 'installAddress',
    label: '安装地址',
    formItemClass: 'col-span-4',
    rules: 'required',
  },
  {
    component: 'InputNumber',
    componentProps: {
      min: 1,
      placeholder: '请输入表倍率',
    },
    fieldName: 'meterRatio',
    label: '表倍率',
    defaultValue: 1,
  },
  {
    component: 'Select',
    componentProps: {
      getPopupContainer,
      options: getDictOptions('measure_purposes'),
      placeholder: '请选择计量用途',
    },
    fieldName: 'measurementPurpose',
    label: '计量用途',
  },
];

// 智能表表单架构
export const intelligentMeterFormSchema: FormSchemaGetter = () => [
  ...mechanicalMeterFormSchema(),
  {
    component: 'Select',
    fieldName: 'communicationMode',
    componentProps: {
      getPopupContainer,
      options: getDictOptions('waterfee_communication_mode'),
      placeholder: '请选择通讯方式',
    },
    label: '通讯方式',
    rules: 'required',
  },
  {
    component: 'Select',
    componentProps: {
      getPopupContainer,
      options: getDictOptions('waterfee_valve_control'),
      placeholder: '请选择阀控功能',
    },
    fieldName: 'valveControl',
    label: '阀控功能',
    defaultValue: valveControlDict.NO,
  },
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入IMEI号',
    },
    fieldName: 'imei',
    label: 'IMEI号',
    dependencies: {
      show: (values) => values.meterType === meterTypeDict.INTELLIGENT,
      triggerFields: ['meterType'],
    },
  },
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入IMSI号',
    },
    fieldName: 'imsi',
    label: 'IMSI号',
    dependencies: {
      show: (values) => values.meterType === meterTypeDict.INTELLIGENT,
      triggerFields: ['meterType'],
    },
  },
  {
    component: 'Select',
    componentProps: {
      getPopupContainer,
      options: getDictOptions('waterfee_iot_platform'),
      placeholder: '请选择物联网平台',
    },
    fieldName: 'iotPlatform',
    label: '物联网平台',
    dependencies: {
      show: (values) => values.meterType === meterTypeDict.INTELLIGENT,
      triggerFields: ['meterType'],
    },
  },
  {
    component: 'Select',
    componentProps: {
      getPopupContainer,
      options: getDictOptions('waterfee_prepaid'),
      placeholder: '请选择是否预付费',
    },
    fieldName: 'prepaid',
    label: '是否预付费',
    defaultValue: prepaidDict.NO,
    dependencies: {
      show: (values) => values.meterType === meterTypeDict.INTELLIGENT,
      triggerFields: ['meterType'],
    },
  },
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入物联网协议',
    },
    fieldName: 'protocol',
    label: '物联网协议',
    dependencies: {
      show: (values) => values.meterType === meterTypeDict.INTELLIGENT,
      triggerFields: ['meterType'],
    },
  },
];


