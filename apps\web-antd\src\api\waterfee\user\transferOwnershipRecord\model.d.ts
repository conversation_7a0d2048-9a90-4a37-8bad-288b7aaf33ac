export interface UserTransferOwnershipRecordVO {
  /**
   * 主键
   */
  transferId: number | string;

  /**
   * 用户ID
   */
  userId: number | string;

  /**
   * 原用水人数
   */
  beforeUseWaterNumber: number;

  /**
   * 原用水户名称
   */
  beforeUserName: string;

  /**
   * 原手机号码
   */
  beforePhoneNumber: string;

  /**
   * 原证件类型
   */
  beforeCertificateType: string;

  /**
   * 原证件号码
   */
  beforeCertificateNumber: string;

  /**
   * 原电子邮箱
   */
  beforeEmail: string;

  /**
   * 新用水人数
   */
  afterUseWaterNumber: number;

  /**
   * 新用水户名称
   */
  afterUserName: string;

  /**
   * 新手机号码
   */
  afterPhoneNumber: string;

  /**
   * 新证件类型
   */
  afterCertificateType: string;

  /**
   * 新证件号码
   */
  afterCertificateNumber: string;

  /**
   * 新电子邮箱
   */
  afterEmail: string;
}

export interface UserTransferOwnershipRecordForm extends BaseEntity {
  /**
   * 主键
   */
  transferId?: number | string;

  /**
   * 用户ID
   */
  userId?: number | string;

  /**
   * 原用水人数
   */
  beforeUseWaterNumber?: number;

  /**
   * 原用水户名称
   */
  beforeUserName?: string;

  /**
   * 原手机号码
   */
  beforePhoneNumber?: string;

  /**
   * 原证件类型
   */
  beforeCertificateType?: string;

  /**
   * 原证件号码
   */
  beforeCertificateNumber?: string;

  /**
   * 原电子邮箱
   */
  beforeEmail?: string;

  /**
   * 新用水人数
   */
  afterUseWaterNumber?: number;

  /**
   * 新用水户名称
   */
  afterUserName?: string;

  /**
   * 新手机号码
   */
  afterPhoneNumber?: string;

  /**
   * 新证件类型
   */
  afterCertificateType?: string;

  /**
   * 新证件号码
   */
  afterCertificateNumber?: string;

  /**
   * 新电子邮箱
   */
  afterEmail?: string;
}

export interface UserTransferOwnershipRecordQuery extends PageQuery {
  /**
   * 用户编号或用户名
   */
  userNoOrUserName?: string;

  /**
   * 查询开始时间
   */
  startTime?: string;

  /**
   * 查询结束时间
   */
  endTime?: string;
}
