import { ref } from 'vue';
import { userList } from '#/api/system/user';
import { areaList } from '#/api/waterfee/area';
import { meterBookList } from '#/api/waterfee/meterbook';
import { listToTree } from '@vben/utils';

// 定义API响应类型
type ApiResponse<T> = T[] | { rows?: T[] } | Record<string, any>;

// 定义选项类型
interface OptionItem {
  label: string;
  value: string | number;
  children?: OptionItem[];
  [key: string]: any;
}

// 营业区域选项（树形结构）
export const businessAreaOptions = ref<OptionItem[]>([]);

// 抄表手册选项
export const meterBookOptions = ref<OptionItem[]>([]);

// 抄表员选项
export const readerOptions = ref<OptionItem[]>([]);

// 定义区域项类型
interface AreaItem {
  areaId?: string | number;
  areaName?: string;
  id?: string | number;
  name?: string;
  [key: string]: any;
}

// 加载营业区域列表（树形结构）
export async function loadBusinessAreaOptions() {
  try {
    console.log('开始加载营业区域列表...');

    // 获取区域数据
    const response: any = await areaList();

    // 记录原始响应
    console.log('区域API原始响应:', response);

    // 确保响应是数组或包含有效数据
    let areaArray: AreaItem[] = [];

    if (response) {
      if (Array.isArray(response)) {
        // 如果响应本身就是数组
        areaArray = response;
      } else if (response && typeof response === 'object' && 'rows' in response && Array.isArray(response.rows)) {
        // 如果响应有rows字段且是数组
        areaArray = response.rows;
      } else if (typeof response === 'object') {
        // 如果响应是对象，尝试转换为数组
        console.warn('区域API返回对象而非数组，尝试转换');
        areaArray = Object.values(response).filter((item: any): item is AreaItem =>
          item && typeof item === 'object' && ('areaId' in item || 'id' in item)
        );
      }
    }

    // 检查数组是否为空
    if (!areaArray.length) {
      console.warn('区域数据为空或格式不正确，尝试使用模拟数据');
      // 使用模拟数据作为备选
      areaArray = [
        { areaId: '1', areaName: '默认区域', parentId: '0' }
      ];
    }

    console.log('处理后的区域数组:', areaArray);

    // 标准化数据，确保所有必要的字段都存在
    const normalizedData = areaArray.map((item: AreaItem) => {
      return {
        ...item,
        areaId: item.areaId || item.id || '',
        areaName: item.areaName || item.name || '未命名区域',
        parentId: item.parentId || '0', // 确保有父ID，默认为根节点
      };
    }).filter(item => !!item.areaId); // 过滤掉没有ID的项

    // 使用 listToTree 将平面数组转换为树形结构
    const treeData = listToTree(normalizedData, {
      id: 'areaId',
      pid: 'parentId',
      children: 'children'
    });

    console.log('生成的树形结构:', treeData);

    // 转换为选项格式（递归处理）
    function convertToOptions(treeNodes: any[]): OptionItem[] {
      return treeNodes.map(node => {
        const option: OptionItem = {
          label: node.areaName,
          value: node.areaId,
          key: node.areaId, // 添加key属性，在TreeSelect中很有用
          title: node.areaName, // 添加title属性，在TreeSelect中很有用
          raw: node // 保存原始数据，方便调试
        };

        // 如果有子节点，递归处理
        if (node.children && node.children.length > 0) {
          option.children = convertToOptions(node.children);
        }

        return option;
      });
    }

    const options = convertToOptions(treeData);
    console.log('生成的树形选项:', options);

    // 更新选项
    businessAreaOptions.value = options;
    console.log('businessAreaOptions已更新:', businessAreaOptions.value);

    return businessAreaOptions.value;
  } catch (error) {
    console.error('加载营业区域列表失败:', error);

    // 出错时使用空数组
    businessAreaOptions.value = [];
    return [];
  }
}

// 定义抄表手册项类型
interface MeterBookItem {
  id?: string | number;
  bookName?: string;
  [key: string]: any;
}

// 定义用户项类型
interface UserItem {
  userId?: string | number;
  userName?: string;
  [key: string]: any;
}

// 加载抄表手册列表
export async function loadMeterBookOptions() {
  try {
    console.log('开始加载抄表手册列表...');

    const res = await meterBookList();
    console.log('抄表手册API原始响应:', res);

    if (res && typeof res === 'object' && 'rows' in res && Array.isArray(res.rows)) {
      const bookItems: MeterBookItem[] = res.rows;

      meterBookOptions.value = bookItems.map((item: MeterBookItem) => ({
        label: item.bookName || '未命名手册',
        value: item.id || '',
        raw: item
      })).filter(item => !!item.value);

      console.log('抄表手册选项已更新:', meterBookOptions.value);
    } else {
      console.warn('抄表手册数据格式不正确');
      meterBookOptions.value = [];
    }

    return meterBookOptions.value;
  } catch (error) {
    console.error('加载抄表手册列表失败:', error);
    meterBookOptions.value = [];
    return [];
  }
}

// 加载抄表员列表
export async function loadReaderOptions() {
  try {
    console.log('开始加载抄表员列表...');

    const res = await userList({
      pageSize: 100,
      pageNum: 1,
    });

    console.log('抄表员API原始响应:', res);

    if (res && typeof res === 'object' && 'rows' in res && Array.isArray(res.rows)) {
      const userItems: UserItem[] = res.rows;

      readerOptions.value = userItems.map((item: UserItem) => ({
        label: item.userName || '未命名用户',
        value: item.userId || '',
        raw: item
      })).filter(item => !!item.value);

      console.log('抄表员选项已更新:', readerOptions.value);
    } else {
      console.warn('抄表员数据格式不正确');
      readerOptions.value = [];
    }

    return readerOptions.value;
  } catch (error) {
    console.error('加载抄表员列表失败:', error);
    readerOptions.value = [];
    return [];
  }
}

// 初始化所有选项
export async function initAllOptions() {
  try {
    await Promise.all([
      loadBusinessAreaOptions(),
      loadMeterBookOptions(),
      loadReaderOptions(),
    ]);
    console.log('所有选项加载完成');
  } catch (error) {
    console.error('初始化选项失败:', error);
  }
}
