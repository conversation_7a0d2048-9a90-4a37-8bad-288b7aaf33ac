<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { Form, Button, DatePicker, Select, Table, Card, Descriptions, Tag, message, Spin } from 'ant-design-vue';
import { SearchOutlined } from '@ant-design/icons-vue';
import { analyzeBalance } from '#/api/waterfee/meterRelation';
import type { MeterBalanceAnalysisVO } from '#/api/waterfee/meterRelation/index';
import { getRecordByMonth } from '#/api/waterfee/meterRelation/index';
import { meterList, meterInfoByNo, meterInfoById, meterInfoByIds} from '#/api/waterfee/meter';
import { getHistoryReadings } from '#/api/waterfee/meterReadingRecord';
import type { MeterModel } from '#/api/waterfee/model/meter/meterModel';
import dayjs from 'dayjs';

// 表单实例
const formRef = ref();

// 表单数据
const formState = reactive({
  parentMeterId: '',
  readingMonth: null as dayjs.Dayjs | null, // 改为年月选择
});

// 水表选项
const meterOptions = ref<{ label: string; value: string }[]>([]);
const loading = ref(false);
const analyzing = ref(false);

// 分析结果
const analysisResult = ref<MeterBalanceAnalysisVO | null>(null);

// 处理后的分表数据
const childMeters = ref<{ meterId: string; meterNo: string; waterUsage: number }[]>([]);

// 子表列定义
const childColumns = [
  {
    title: '分表编号',
    dataIndex: 'meterNo',
    key: 'meterNo',
    width: 150,
  },
  {
    title: '用水量(m³)',
    dataIndex: 'waterUsage',
    key: 'waterUsage',
    width: 120,
  },
];

// 加载水表列表
async function loadMeterList() {
  loading.value = true;
  try {
    const res = await meterList({
      pageNum: 1,
      pageSize: 1000,
    });

    if (res && res.rows) {
      meterOptions.value = res.rows.map((item: MeterModel) => ({
        label: `${item.meterNo} (ID: ${item.meterId})`,
        value: String(item.meterId),
      }));
    }
  } catch (error) {
    console.error('获取水表列表失败:', error);
    message.error('获取水表列表失败');
  } finally {
    loading.value = false;
  }
}

// 执行分析
async function handleAnalyze() {
  try {
    await formRef.value.validate();

    if (!formState.readingMonth) {
      message.error('请选择抄表月份');
      return;
    }

    analyzing.value = true;

    // 将选择的年月转换为完整的日期时间格式
    // 使用所选月份的最后一天，时间设为23:59:59
    const year = formState.readingMonth.year();
    const month = formState.readingMonth.month(); // 0-11

    // 获取所选月份的最后一天
    const lastDayOfMonth = new Date(year, month + 1, 0).getDate();

    // 构建完整的日期时间字符串：YYYY-MM-DD 23:59:59
    const readingTimeStr = `${year}-${String(month + 1).padStart(2, '0')}-${String(lastDayOfMonth).padStart(2, '0')} 23:59:59`;

    console.log('构建的完整日期时间:', readingTimeStr);

    // 获取总分表平衡分析结果
    const res = await analyzeBalance(formState.parentMeterId, readingTimeStr);
    console.log('总分表平衡分析结果:', res);

    // 保存分析结果
    analysisResult.value = res;

    // 清空子表数据
    childMeters.value = [];

    // 处理子表数据
    if (res && res.childMeterNos && Array.isArray(res.childMeterNos)) {
      console.log('分表编号列表:', res.childMeterNos);

      // 提取所有分表ID
      let childMeterIds = '';

      // 处理每个分表ID，去除方括号和引号
      for (const childMeterNoStr of res.childMeterNos) {
        try {
          // 提取分表ID，去除方括号和多余的引号
          // 示例: "[1909075298933329921]" -> "1909075298933329921"
          let childMeterId = childMeterNoStr;

          // 移除方括号
          childMeterId = childMeterId.replace(/[\[\]]/g, '');

          // 移除可能存在的引号
          childMeterId = childMeterId.replace(/['"]/g, '');

          console.log('处理分表ID:', childMeterId);

          // 拼接，注意加上分隔符
          childMeterIds += childMeterId + ',';  // 如果需要分隔符，可以用逗号

        } catch (err) {
          console.error(`处理分表ID ${childMeterNoStr} 失败:`, err);
        }
      }

      // 去掉最后的逗号
      childMeterIds = childMeterIds.slice(0, -1);

      console.log('所有分表ID:', childMeterIds);


      // 批量获取分表信息
      try {
        console.log('批量获取分表信息，ID列表:', childMeterIds);

        const ss = childMeterIds.split(',').map(id => id.trim());
        const meterInfoList = await meterInfoByIds(ss);
        console.log('批量获取的分表信息:', meterInfoList);

        // 构建年月格式：YYYY-MM-DD HH:mm:ss
        const yearMonth = readingTimeStr; // 使用完整的日期时间格式
        console.log('查询用水量的日期时间:', yearMonth);

        // 收集所有分表编号，用于批量获取用水量
        const allMeterNos = [];

        if (Array.isArray(meterInfoList)) {
          meterInfoList.forEach(meterInfo => {
            if (meterInfo && meterInfo.meterNo) {
              allMeterNos.push(meterInfo.meterNo);
            }
          });
        } else if (meterInfoList && meterInfoList.meterNo) {
          allMeterNos.push(meterInfoList.meterNo);
        }

        console.log('需要获取用水量的分表编号:', allMeterNos);

        if (allMeterNos.length === 0) {
          console.warn('没有找到有效的分表编号');
          return;
        }

        // 批量获取用水量，分批处理
        try {
          // 检查是否有批量获取用水量的API
          const { getRecordsByMonth } = await import('#/api/waterfee/meterRelation');

          // 分批处理的批次大小
          const BATCH_SIZE = 50;

          // 将分表编号分成多个批次
          const batches = [];
          for (let i = 0; i < allMeterNos.length; i += BATCH_SIZE) {
            batches.push(allMeterNos.slice(i, i + BATCH_SIZE));
          }

          console.log(`将 ${allMeterNos.length} 个分表编号分成 ${batches.length} 个批次处理`);

          // 创建分表编号到用水量的映射
          const waterUsageMap = new Map();

          // 逐批处理
          for (let i = 0; i < batches.length; i++) {
            const batch = batches[i];
            console.log(`处理第 ${i + 1}/${batches.length} 批分表编号，数量: ${batch.length}`);

            try {
              // 批量获取当前批次的用水量
              const recordsData = await getRecordsByMonth(batch, yearMonth);
              console.log(`第 ${i + 1} 批用水量数据:`, recordsData);

              // 处理当前批次的用水量数据
              if (Array.isArray(recordsData)) {
                recordsData.forEach(record => {
                  if (record && record.meterNo) {
                    waterUsageMap.set(record.meterNo, record.waterUsage || 0);
                  }
                });
              } else if (recordsData && recordsData.meterNo) {
                // 如果只返回了一个对象
                waterUsageMap.set(recordsData.meterNo, recordsData.waterUsage || 0);
              }
            } catch (err) {
              console.error(`批量获取第 ${i + 1} 批用水量数据失败:`, err);

              // 如果批量获取失败，使用单个API获取当前批次
              const { getRecordByMonth } = await import('#/api/waterfee/meterRelation');

              // 限制并发请求数量
              const MAX_CONCURRENT = 5;
              for (let j = 0; j < batch.length; j += MAX_CONCURRENT) {
                const concurrentBatch = batch.slice(j, j + MAX_CONCURRENT);
                const recordPromises = concurrentBatch.map(async (meterNo) => {
                  try {
                    const record = await getRecordByMonth(meterNo, yearMonth);
                    if (record) {
                      waterUsageMap.set(meterNo, record.waterUsage || 0);
                    }
                    return record;
                  } catch (detailErr) {
                    console.error(`获取分表 ${meterNo} 用水量失败:`, detailErr);
                    waterUsageMap.set(meterNo, 0); // 默认为0
                    return null;
                  }
                });

                await Promise.all(recordPromises);
              }
            }
          }

          console.log('所有分表用水量映射:', waterUsageMap);

          // 处理分表信息
          if (Array.isArray(meterInfoList)) {
            meterInfoList.forEach(meterInfo => {
              if (meterInfo && meterInfo.meterNo) {
                // 从映射中获取用水量
                const waterUsage = waterUsageMap.get(meterInfo.meterNo) || 0;

                // 添加到分表列表
                childMeters.value.push({
                  meterId: meterInfo.meterId,
                  meterNo: meterInfo.meterNo,
                  waterUsage: waterUsage
                });
              }
            });
          } else if (meterInfoList && meterInfoList.meterNo) {
            // 处理单个分表信息的情况
            const waterUsage = waterUsageMap.get(meterInfoList.meterNo) || 0;

            // 添加到分表列表
            childMeters.value.push({
              meterId: meterInfoList.meterId,
              meterNo: meterInfoList.meterNo,
              waterUsage: waterUsage
            });
          }
        } catch (error) {
          console.error('批量获取用水量过程中发生错误:', error);

          // 如果整个批量处理失败，使用单个API
          try {
            const { getRecordByMonth } = await import('#/api/waterfee/meterRelation');
            const waterUsageMap = new Map();

            // 限制并发请求数量
            const MAX_CONCURRENT = 5;
            for (let i = 0; i < allMeterNos.length; i += MAX_CONCURRENT) {
              const batchNos = allMeterNos.slice(i, i + MAX_CONCURRENT);
              const recordPromises = batchNos.map(async (meterNo) => {
                try {
                  const record = await getRecordByMonth(meterNo, yearMonth);
                  return {
                    meterNo,
                    waterUsage: record?.waterUsage || 0
                  };
                } catch (err) {
                  console.error(`获取分表 ${meterNo} 用水量失败:`, err);
                  return {
                    meterNo,
                    waterUsage: 0
                  };
                }
              });

              const results = await Promise.all(recordPromises);
              results.forEach(result => {
                if (result) {
                  waterUsageMap.set(result.meterNo, result.waterUsage);
                }
              });
            }

            // 处理分表信息
            if (Array.isArray(meterInfoList)) {
              meterInfoList.forEach(meterInfo => {
                if (meterInfo && meterInfo.meterNo) {
                  // 从映射中获取用水量
                  const waterUsage = waterUsageMap.get(meterInfo.meterNo) || 0;

                  // 添加到分表列表
                  childMeters.value.push({
                    meterId: meterInfo.meterId,
                    meterNo: meterInfo.meterNo,
                    waterUsage: waterUsage
                  });
                }
              });
            } else if (meterInfoList && meterInfoList.meterNo) {
              // 处理单个分表信息的情况
              const waterUsage = waterUsageMap.get(meterInfoList.meterNo) || 0;

              // 添加到分表列表
              childMeters.value.push({
                meterId: meterInfoList.meterId,
                meterNo: meterInfoList.meterNo,
                waterUsage: waterUsage
              });
            }
          } catch (fallbackError) {
            console.error('单个API获取用水量也失败:', fallbackError);

            // 如果单个API也失败，添加默认记录
            if (Array.isArray(meterInfoList)) {
              meterInfoList.forEach(meterInfo => {
                if (meterInfo && meterInfo.meterNo) {
                  childMeters.value.push({
                    meterId: meterInfo.meterId,
                    meterNo: meterInfo.meterNo,
                    waterUsage: 0
                  });
                }
              });
            } else if (meterInfoList && meterInfoList.meterNo) {
              childMeters.value.push({
                meterId: meterInfoList.meterId,
                meterNo: meterInfoList.meterNo,
                waterUsage: 0
              });
            }
          }
        }
      } catch (err) {
        console.error('批量获取分表信息失败:', err);

        // 如果批量获取失败，为每个ID添加一个默认记录
        for (const childMeterId of childMeterIds) {
          childMeters.value.push({
            meterId: childMeterId,
            meterNo: childMeterId, // 使用ID作为编号
            waterUsage: 0
          });
        }
      }
    }

    console.log('处理后的分表数据:', childMeters.value);

  } catch (error) {
    console.error('分析总分表水量差异失败:', error);
    message.error('分析总分表水量差异失败');
    analysisResult.value = null;
    childMeters.value = [];
  } finally {
    analyzing.value = false;
  }
}

  /**
   * 使用原生 JS 格式化日期为 "YYYY年MM月"
   * @param {string} dateStr - 形如 2025-05-13 16:08:24 的字符串
   * @returns {string} - 返回 "2025年05月"
   */
function formatYearMonth(dateStr) {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  return `${year}年${month}月`
}

// 重置表单
function handleReset() {
  formRef.value.resetFields();
  analysisResult.value = null;
  childMeters.value = [];
}

// 初始化
onMounted(() => {
  loadMeterList();
});
</script>

<template>
  <div class="balance-analysis">
    <Card title="总分表平衡分析" :bordered="false">
      <Form
        ref="formRef"
        :model="formState"
        layout="inline"
      >
        <Form.Item
          label="总表"
          name="parentMeterId"
          :rules="[{ required: true, message: '请选择总表' }]"
        >
          <Select
            v-model:value="formState.parentMeterId"
            placeholder="请选择总表"
            :options="meterOptions"
            show-search
            :filter-option="(input, option) =>
              option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0"
            style="width: 250px"
            :loading="loading"
          />
        </Form.Item>

        <Form.Item
          label="抄表月份"
          name="readingMonth"
          :rules="[{ required: true, message: '请选择抄表月份' }]"
        >
          <DatePicker
            v-model:value="formState.readingMonth"
            picker="month"
            format="YYYY-MM"
            placeholder="请选择抄表月份"
            style="width: 250px"
          />
        </Form.Item>

        <Form.Item>
          <Button type="primary" @click="handleAnalyze" :loading="analyzing">
            <template #icon><SearchOutlined /></template>
            分析
          </Button>
          <Button style="margin-left: 8px" @click="handleReset">
            重置
          </Button>
        </Form.Item>
      </Form>

      <Spin :spinning="analyzing">
        <div v-if="analysisResult" class="analysis-result">
          <Descriptions title="分析结果" bordered>
            <Descriptions.Item label="总表编号">{{ analysisResult.parentMeterNo }}</Descriptions.Item>
            <Descriptions.Item label="抄表时间">{{ formatYearMonth(analysisResult.readingTime) }}</Descriptions.Item>
            <Descriptions.Item label="总表用水量">{{ analysisResult.parentUsage }} m³</Descriptions.Item>
            <Descriptions.Item label="分表总用水量">{{ analysisResult.childUsage }} m³</Descriptions.Item>
            <Descriptions.Item label="差值">{{ analysisResult.diffUsage }} m³</Descriptions.Item>
            <Descriptions.Item label="漏损率">{{ analysisResult.leakRate }}%</Descriptions.Item>
            <Descriptions.Item label="是否异常">
              <Tag :color="analysisResult.isAbnormal === '1' ? 'red' : 'green'">
                {{ analysisResult.isAbnormal === '1' ? '异常' : '正常' }}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="异常原因" :span="3">
              {{ analysisResult.isAbnormal === '1' && analysisResult.abnormalReason ? analysisResult.abnormalReason : '无' }}
            </Descriptions.Item>
          </Descriptions>

          <div class="child-table">
            <h3>分表明细</h3>
            <Table
              :columns="childColumns"
              :data-source="childMeters"
              :pagination="false"
              :scroll="{ y: 300 }"
              row-key="meterId"
            />
          </div>
        </div>
        <div v-else-if="!analyzing" class="empty-result">
          请选择总表和抄表时间进行分析
        </div>
      </Spin>
    </Card>
  </div>
</template>


<style scoped>
.balance-analysis {
  padding: 16px;
}

.analysis-result {
  margin-top: 24px;
}

.child-table {
  margin-top: 24px;
}

.empty-result {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #999;
  font-size: 14px;
  margin-top: 24px;
}
</style>
