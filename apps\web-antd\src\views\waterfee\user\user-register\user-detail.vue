<script setup lang="ts">
import type { UserForm } from '#/api/waterfee/user/archivesManage/model';
import type { DescItem } from '#/components/description';

import { computed, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { Button as AButton, Card as ACard } from 'ant-design-vue';

import { getUser } from '#/api/waterfee/user/archivesManage';
import { Description, useDescription } from '#/components/description';
import { renderDict } from '#/utils/render';

import { flowSchema } from './data';

const route = useRoute();
const router = useRouter();
const userInfo = ref<UserForm>({} as UserForm);
const loading = ref(false);
const title = computed(() => '用水用户详情');

// 获取字典名称
const getDictName = (fieldName: string) => {
  const dictMap: Record<string, string> = {
    customerNature: 'waterfee_user_customer_nature',
    useWaterNature: 'waterfee_user_use_water_nature',
    userStatus: 'waterfee_user_user_status',
    auditStatus: 'audit_status',
    certificateType: 'waterfee_user_certificate_type',
    invoiceType: 'waterfee_user_invoice_type',
    billingMethod: 'waterfee_user_billing_method',
    ifPenalty: 'yes_no',
    ifExtraCharge: 'yes_no',
    penaltyType: 'waterfee_user_penalty_type',
    priceUseWaterNature: 'waterfee_user_use_water_nature',
  };
  return dictMap[fieldName] || '';
};

// 构建描述项
const descSchema = computed<DescItem[]>(() => {
  return flowSchema()
    .filter((item) => item.fieldName !== 'userId') // 过滤掉不需要显示的字段
    .map((item) => {
      return {
        field: item.fieldName,
        label: item.label as string,
        span: 1,
        render: (val: any) => {
          const dictName = getDictName(item.fieldName);
          if (dictName) {
            return renderDict(val, dictName);
          }
          if (item.fieldName === 'supplyDate' && val) {
            return val;
          }
          return val || '-';
        },
      } satisfies DescItem;
    });
});

// 使用Description组件
const [registerDescription, { setDescProps }] = useDescription({
  column: 2,
  bordered: true,
  labelStyle: {
    fontWeight: 'bold',
    minWidth: '120px',
  },
  schema: descSchema.value,
});

// 初始化数据
const initData = async () => {
  const { id } = route.query;
  if (id) {
    loading.value = true;
    try {
      const record = await getUser(id as string);
      userInfo.value = record;
      setDescProps({ data: record }, true);
    } catch (error) {
      console.error(error);
    } finally {
      loading.value = false;
    }
  }
};

// 返回列表页
const handleBack = () => {
  router.back();
};

initData();
</script>

<template>
  <div class="p-4">
    <ACard :title="title" :loading="loading">
      <template #extra>
        <AButton @click="handleBack">返回</AButton>
      </template>
      <Description @register="registerDescription" />
    </ACard>
  </div>
</template>
