import type { BasicPageParams, BasicFetchResult } from '#/api/types/common';
import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

import { ensureMeterIdString, preserveIdPrecision } from '../meter/index';
import { preserveBigInt } from '../../../utils/json-bigint';

// 智能水表接口API
enum Api {
  detail = '/waterfee/meter/intelligent',
  export = '/waterfee/meter/intelligent/export',
  list = '/waterfee/meter/intelligent/list',
  params = '/waterfee/meter/intelligent/params',
  readings = '/waterfee/meter/intelligent/readings',
  realtime = '/waterfee/meter/intelligent/realtime',
  valve = '/waterfee/meter/intelligent/valve',
}

// 智能水表模型
export interface IntelligentMeterModel {
  meterId: string;
  meterNo: string;
  meterType: number;
  userNo?: string;
  userName?: string;
  manufacturer?: string;
  caliber?: string;
  imei?: string;
  signalStrength?: number;
  voltage?: number;
  reading?: number;
  installDate?: string;
  lastReadingTime?: string;
  [key: string]: any;
}

// 智能水表查询参数
export interface IntelligentMeterParams extends BasicPageParams {
  meterNo?: string;
  meterType?: number;
  caliber?: string;
  manufacturer?: string;
  installDateRange?: [string, string];
  userNo?: string;
  userName?: string;
  businessAreaId?: string;
  [key: string]: any;
}

// 抄表记录模型
export interface MeterReadingModel {
  id?: string;
  meterId: string;
  meterNo?: string;
  readingTime: string;
  reading: number;
  waterUsage: number;
  signalStrength: number;
  batteryVoltage: number;
  [key: string]: any;
}

// 实时数据模型
export interface RealtimeDataModel {
  reading: number;
  signalStrength: number;
  batteryVoltage: number;
  valveStatus?: string;
  [key: string]: any;
}

// 获取智能水表列表
export function getIntelligentMeterList(params?: IntelligentMeterParams) {
  // 处理查询参数
  const queryParams = params ? { ...params } : {};

  // 处理日期范围
  if (
    queryParams.installDateRange &&
    Array.isArray(queryParams.installDateRange)
  ) {
    queryParams.installDateStart = queryParams.installDateRange[0];
    queryParams.installDateEnd = queryParams.installDateRange[1];
    delete queryParams.installDateRange;
  }

  // 处理ID
  if (queryParams.businessAreaId) {
    queryParams.businessAreaId = ensureMeterIdString(
      queryParams.businessAreaId || '', // Ensure a string is passed
    );
  }

  // 保留大整数精度
  const safeParams = preserveIdPrecision(queryParams);

  return requestClient.get<BasicFetchResult<IntelligentMeterModel[]>>(
    Api.list,
    {
      params: safeParams,
    },
  );
}

// 获取智能水表详情
export function getIntelligentMeterInfo(meterId: string) {
  const safeMeterId = ensureMeterIdString(meterId || ''); // Ensure a string is passed
  return requestClient.get<IntelligentMeterModel>(
    `${Api.detail}/${safeMeterId}`,
  );
}

// 获取智能水表抄表记录
export function getIntelligentMeterReadings(params: {
  endTime?: string;
  meterId: string;
  pageNum: number;
  pageSize: number;
  startTime?: string;
}) {
  const { meterId, ...queryParams } = params;
  const safeMeterId = ensureMeterIdString(meterId || ''); // Ensure a string is passed

  return requestClient.get<BasicFetchResult<MeterReadingModel[]>>(
    Api.readings,
    {
      params: {
        ...queryParams,
        meterId: safeMeterId,
      },
    },
  );
}

// 获取智能水表实时数据
export function getIntelligentMeterRealtime(meterId: string) {
  const safeMeterId = ensureMeterIdString(meterId || ''); // Ensure a string is passed
  return requestClient.get<RealtimeDataModel>(`${Api.realtime}/${safeMeterId}`);
}

// 远程阀门控制
export function controlValve(meterId: string, action: 'close' | 'open') {
  const safeMeterId = ensureMeterIdString(meterId || ''); // Ensure a string is passed
  return requestClient.post(`${Api.valve}/${safeMeterId}`, {
    action,
  });
}

// 设置智能水表参数
export function setMeterParams(
  meterId: string,
  params: {
    alarmThreshold?: number; // 报警阈值
    flowThreshold?: number; // 流量阈值
    reportInterval?: number; // 上报间隔(分钟)
  },
) {
  const safeMeterId = ensureMeterIdString(meterId || ''); // Ensure a string is passed
  return requestClient.put(`${Api.params}/${safeMeterId}`, params);
}

// 导出智能水表数据
export function exportIntelligentMeter(params?: IntelligentMeterParams) {
  // 确保params是一个对象，防止undefined导致错误
  const safeParams = params ? preserveBigInt(params) : {};
  return commonExport(Api.export, safeParams);
}


