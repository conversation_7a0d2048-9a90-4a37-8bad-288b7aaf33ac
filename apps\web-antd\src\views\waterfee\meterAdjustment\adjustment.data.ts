import { h } from 'vue';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { Button } from 'ant-design-vue';
import { meterBookOptions } from './utils/options';
import { renderDict } from '#/utils/render';
import eventBus, { EventType } from './utils/eventBus';

// 查询表单架构
export function querySchema() {
  return [
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择表册',
        options: meterBookOptions,
        allowClear: true,
        onChange: (value: string | undefined) => {
          // 选择表册后自动触发查询
          console.log('表册选择变更:', value);
        }
      },
      fieldName: 'bookId',
      label: '表册',
      required: true,
    },
  ];
}

// 表格列配置
export const columns: VxeGridProps['columns'] = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center',
  },
  {
    type: 'seq',
    title: '序号',
    width: 60,
    fixed: 'left',
    align: 'center',
    // 使用自定义渲染函数来显示序号
    slots: {
      default: ({ rowIndex }) => {
        return h('span', {}, rowIndex + 1);
      },
    },
  },
  {
    field: 'businessAreaId',
    title: '营业区域',
    minWidth: 120,
    align: 'center',
    slots: {
      default: ({ row }) => {
        // 根据 businessAreaId 从缓存中获取对应的名称
        const areaName = row.businessAreaName || '加载中...';
        return h('div', {}, areaName);
      },
    },
  },
  {
    field: 'meterBookId',
    title: '抄表手册',
    minWidth: 120,
    align: 'center',
    slots: {
      default: ({ row }) => {
        // 根据 meterBookId 从缓存中获取对应的名称
        const bookName = row.meterBookName || '加载中...';
        return h('div', {}, bookName);
      },
    },
  },
  {
    field: 'meterNo',
    title: '水表编号',
    minWidth: 120,
    align: 'center',
  },
  {
    field: 'userNo',
    title: '用户编号',
    minWidth: 120,
    align: 'center',
  },
  {
    field: 'userId',
    title: '用户名',
    minWidth: 100,
    align: 'center',
    slots: {
      default: ({ row }) => {
        // 根据 userId 从缓存中获取对应的名称
        const userName = row.userName || '加载中...';
        return h('div', {}, userName);
      },
    },
  },
  {
    field: 'waterNature',
    title: '用水性质',
    minWidth: 100,
    align: 'center',
    slots: {
      default: ({ row }) => {
        // 使用字典翻译
        return renderDict(row.waterNature, 'water_use_type');
      },
    },
  },
  {
    title: '操作',
    width: 120,
    fixed: 'right',
    align: 'center',
    slots: {
      default: ({ row }) => {
        return h('div', [
          h(Button, {
            type: 'link',
            size: 'small',
            onClick: () => {
              console.log('调整水表:', row);
              // 通过事件总线触发调整事件
              eventBus.emit(EventType.ADJUST_METER, row);
            }
          }, { default: () => '调整' })
        ]);
      },
    },
  },
];

