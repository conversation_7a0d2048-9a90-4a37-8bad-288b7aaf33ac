import { h } from 'vue';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { formatDate } from '@vben/utils';
import { getDictOptions } from '#/utils/dict';
import { businessAreaOptions, meterBookOptions, readerOptions } from './utils/options';

// 查询表单配置
export function querySchema() {
  return [
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入水表编号',
        allowClear: true,
      },
      fieldName: 'meterNo',
      label: '水表编号',
    },
    // {
    //   component: 'Select',
    //   componentProps: {
    //     placeholder: '请选择营业区域',
    //     options: businessAreaOptions,
    //     allowClear: true,
    //   },
    //   fieldName: 'businessAreaId',
    //   label: '营业区域',
    // },
    // {
    //   component: 'Select',
    //   componentProps: {
    //     placeholder: '请选择抄表手册',
    //     options: meterBookOptions,
    //     allowClear: true,
    //   },
    //   fieldName: 'meterBookId',
    //   label: '抄表手册',
    // },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择抄表员',
        options: readerOptions,
        allowClear: true,
      },
      fieldName: 'readerId',
      label: '抄表员',
    },
    {
      component: 'RangePicker',
      componentProps: {
        placeholder: ['开始日期', '结束日期'],
        allowClear: true,
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
        showTime: false,
      },
      fieldName: 'rangeTime',
      label: '抄表时间',
    },
  ];
}

// 表单配置
export function formSchema() {
  return [
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入水表编号',
        allowClear: true,
      },
      fieldName: 'meterNo',
      label: '水表编号',
      required: true,
      colProps: { span: 24 },
    },
    {
      component: 'Input',
      componentProps: {
        disabled: true,
        placeholder: '自动获取',
      },
      fieldName: 'userNo',
      label: '用户编号',
      colProps: { span: 12 },
    },
    {
      component: 'Input',
      componentProps: {
        disabled: true,
        placeholder: '自动获取',
      },
      fieldName: 'userName',
      label: '用户姓名',
      colProps: { span: 12 },
    },
    {
      component: 'Input',
      componentProps: {
        disabled: true,
        placeholder: '自动获取',
      },
      fieldName: 'businessAreaName',
      label: '营业区域',
      colProps: { span: 12 },
    },
    {
      component: 'Input',
      componentProps: {
        disabled: true,
        placeholder: '自动获取',
      },
      fieldName: 'meterBookName',
      label: '抄表手册',
      colProps: { span: 12 },
    },
    {
      component: 'InputNumber',
      componentProps: {
        disabled: true,
        placeholder: '自动获取',
        min: 0,
        precision: 2,
      },
      fieldName: 'lastReading',
      label: '上期读数',
      colProps: { span: 12 },
    },
    {
      component: 'InputNumber',
      componentProps: {
        disabled: true,
        placeholder: '自动获取',
        min: 0,
        precision: 2,
      },
      fieldName: 'oldMeterStopReading',
      label: '旧表止数',
      colProps: { span: 12 },
    },
    {
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入本期读数',
        min: 0,
        precision: 2,
      },
      fieldName: 'currentReading',
      label: '本期读数',
      required: true,
      colProps: { span: 12 },
    },
    {
      component: 'InputNumber',
      componentProps: {
        placeholder: '自动计算',
        min: 0,
        precision: 2,
      },
      fieldName: 'waterUsage',
      label: '用水量',
      required: true,
      colProps: { span: 12 },
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择抄表员',
        options: readerOptions,
        allowClear: true,
      },
      fieldName: 'readerId',
      label: '抄表员',
      required: true,
      colProps: { span: 12 },
    },
    {
      component: 'DatePicker',
      componentProps: {
        placeholder: '请选择抄表时间',
        allowClear: true,
        showTime: true,
      },
      fieldName: 'readingTime',
      label: '抄表时间',
      required: true,
      colProps: { span: 12 },
    },
    {
      component: 'Input.TextArea',
      componentProps: {
        placeholder: '请输入备注',
        allowClear: true,
        rows: 4,
      },
      fieldName: 'remark',
      label: '备注',
      colProps: { span: 24 },
    },
    // 隐藏字段，用于提交表单
    {
      component: 'Input',
      componentProps: {
        type: 'hidden',
      },
      fieldName: 'businessAreaId',
      label: '',
      show: false,
    },
    {
      component: 'Input',
      componentProps: {
        type: 'hidden',
      },
      fieldName: 'meterBookId',
      label: '',
      show: false,
    },
    {
      component: 'Input',
      componentProps: {
        type: 'hidden',
      },
      fieldName: 'userId',
      label: '',
      show: false,
    },
    {
      component: 'Input',
      componentProps: {
        type: 'hidden',
      },
      fieldName: 'meterId',
      label: '',
      show: false,
    },
  ];
}

// 表格列配置
export const columns: VxeGridProps['columns'] = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center',
  },
  {
    type: 'seq',
    title: '序号',
    width: 60,
    fixed: 'left',
    align: 'center',
    // 使用自定义渲染函数来显示序号
    slots: {
      default: ({ rowIndex }) => {
        return h('span', {}, rowIndex + 1);
      },
    },
  },
  {
    field: 'businessAreaName',
    title: '营业区域',
    minWidth: 120,
    align: 'center',
  },
  {
    field: 'meterBookName',
    title: '抄表手册',
    minWidth: 120,
    align: 'center',
  },
  {
    field: 'meterNo',
    title: '水表编号',
    minWidth: 120,
    align: 'center',
  },
  {
    field: 'userNo',
    title: '用户编号',
    minWidth: 120,
    align: 'center',
  },
  {
    field: 'userName',
    title: '用户名',
    minWidth: 100,
    align: 'center',
  },
  {
    field: 'lastReading',
    title: '上期读数',
    minWidth: 100,
    align: 'center',
  },
  {
    field: 'currentReading',
    title: '本期读数',
    minWidth: 100,
    align: 'center',
  },
  {
    field: 'waterUsage',
    title: '用水量',
    minWidth: 100,
    align: 'center',
  },
  {
    field: 'operator',
    title: '抄表员',
    minWidth: 100,
    align: 'center',
  },
  {
    field: 'readingTime',
    title: '抄表时间',
    minWidth: 150,
    align: 'center',
    formatter: ({ cellValue }) => {
      if (!cellValue) return '';
      return formatDate(cellValue, 'YYYY-MM-DD HH:mm:ss');
    },
  },
];
