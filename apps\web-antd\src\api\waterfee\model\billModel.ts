import type { BaseEntity } from '#/api/common';

/**
 * 账单模型
 */
export interface BillModel extends BaseEntity {
  /** 账单唯一ID */
  billId?: string;
  /** 账单编号 */
  billNumber?: string;
  /** 关联客户ID */
  customerId?: number;
  /** 关联水表ID */
  meterId?: number;
  /** 表册ID */
  meterBookId?: number;
  /** 表册名称 */
  bookName?: string;
  /** 价格方案ID */
  pricePlanId?: number;
  /** 价格方案名称 */
  pricePlanName?: string;
  /** 计费周期开始日期 */
  billingPeriodStart?: string;
  /** 计费周期结束日期 */
  billingPeriodEnd?: string;
  /** 账单发行日期 */
  billingIssueDate?: string;
  /** 账单到期日期 */
  billingDueDate?: string;
  /** 上期结算读数ID */
  previousReadingId?: number;
  /** 本期结算读数ID */
  currentReadingId?: number;
  /** 上期读数值 */
  previousReadingValue?: number;
  /** 本期读数值 */
  currentReadingValue?: number;
  /** 本期消费量 */
  consumptionVolume?: number;
  /** 消费量单位 */
  consumptionUnit?: string;
  /** 标准水费 */
  baseChargeAmount?: number;
  /** 阶梯1消费量 */
  tier1?: number;
  /** 阶梯1费用 */
  tier1Amount?: number;
  /** 阶梯2消费量 */
  tier2?: number;
  /** 阶梯2费用 */
  tier2Amount?: number;
  /** 阶梯3消费量 */
  tier3?: number;
  /** 阶梯3费用 */
  tier3Amount?: number;
  /** 违约金 */
  surchargeAmount?: number;
  /** 附加费用 */
  additionalChargeAmount?: number;
  /** 税额 */
  taxAmount?: number;
  /** 调整金额 */
  adjustmentsAmount?: number;
  /** 账单总金额 */
  totalAmount?: number;
  /** 已支付金额 */
  amountPaid?: number;
  /** 应付余额 */
  balanceDue?: number;
  /** 账单状态 */
  billStatus?: string;
  /** 账单月份 */
  billMonth?: string;
  /** 账单备注 */
  notes?: string;
  /** 备注 */
  remark?: string;
  /** 用于用量调整的字段 */
  adjustmentVolume?: number;
  /** 用量调整原因 */
  adjustmentReason?: string;
  /** 上期结余 */
  previousBalance?: number;

  // 关联信息
  /** 客户名称 */
  customerName?: string;
  /** 水表编号 */
  meterNo?: string;
  /** 表册名称 - 兼容旧版字段 */
  meterBookName?: string;
  /** 发行人名称 */
  createByName?: string;

  // 用户相关信息
  /** 用户编号 */
  userNo?: string;
  /** 用户名称 */
  userName?: string;
  /** 小区ID */
  communityId?: number;
  /** 小区名称 */
  communityName?: string;
  /** 客户性质 */
  customerNature?: string;
  /** 用水性质 */
  useWaterNature?: string;
  /** 手机号码 */
  phoneNumber?: string;
  /** 证件类型 */
  certificateType?: string;
  /** 证件号码 */
  certificateNumber?: string;
  /** 用户状态 */
  userStatus?: string;
  /** 用水地址 */
  address?: string;
  /** 电子邮箱 */
  email?: string;
  /** 用水人数 */
  useWaterNumber?: number;
  /** 供水日期 */
  supplyDate?: string;
  /** 单元房号 */
  unitRoomNumber?: string;

  // 账单表格数据
  /** 正常用量 */
  normalUsage?: number;
  /** 价格名称 */
  priceName?: string;
  /** 单价 */
  unitPrice?: number;
}

/**
 * 账单查询参数
 */
export interface BillParams {
  /** 账单编号 */
  billNumber?: string;
  /** 客户ID */
  customerId?: number;
  /** 水表ID */
  meterId?: number;
  /** 表册ID */
  meterBookId?: number;
  /** 账单状态 */
  billStatus?: string;
  /** 账单月份 */
  billMonth?: string;
  /** 分页参数 */
  pageNum?: number;
  /** 分页参数 */
  pageSize?: number;
}
