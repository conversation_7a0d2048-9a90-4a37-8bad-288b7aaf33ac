<template>
  <BasicTable :table-title="`${meterBook.meterBookName} - 账单列表`">
    <template #toolbar-tools>
      <Space>
        <Button @click="handleBack">
          <template #icon><ArrowLeftOutlined /></template>
          返回
        </Button>
        <Button @click="handleRefresh">
          <template #icon><ReloadOutlined /></template>
          刷新
        </Button>
        <Button
          type="primary"
          :disabled="!selectedRows.length"
          @click="handleBatchIssue"
        >
          <template #icon><SendOutlined /></template>
          批量发行
        </Button>
      </Space>
    </template>
    <template #action="{ row }">
      <Space>
        <Button type="link" @click="handleView(row)">
          查看
        </Button>
        <Button type="link" @click="handleIssue(row)" v-if="row.billStatus === 'DRAFT'">
          发行
        </Button>
      </Space>
    </template>
  </BasicTable>
</template>

<script lang="ts" setup>
  import { Button, Space, message, Modal } from 'ant-design-vue';
  import { ReloadOutlined, SendOutlined, ArrowLeftOutlined } from '@ant-design/icons-vue';
  import { useVbenVxeGrid } from '#/adapter/vxe-table';
  import { billList, billIssue } from '#/api/waterfee/bill';
  import { detailColumns, querySchema } from '../data';
  import type { BillModel } from '#/api/waterfee/model/billModel';
  import { ref, onMounted } from 'vue';
  import { useRouter } from 'vue-router';

  // 定义属性
  const props = defineProps<{
    meterBook: {
      meterBookId: number;
      meterBookName: string;
    }
  }>();

  // 定义事件
  const emit = defineEmits(['back']);

  const router = useRouter();
  const selectedRows = ref<BillModel[]>([]);

  // 账单详情表单配置
  const formOptions = {
    commonConfig: {
      labelWidth: 80,
      componentProps: {
        allowClear: true,
      },
    },
    schema: querySchema(),
    wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
  };

  // 账单详情表格配置
  const gridOptions = {
    columns: detailColumns,
    // height: 'calc(100vh - 280px)', // 减去头部、标题等元素的高度
    checkboxConfig: {
      highlight: true,
      reserve: true,
    },
    proxyConfig: {
      ajax: {
        query: async ({ page }: { page?: { currentPage: number; pageSize: number } }, formValues: Record<string, any> = {}) => {
          // 构建查询参数
          const params: any = {
            pageNum: page?.currentPage,
            pageSize: page?.pageSize,
            meterBookId: props.meterBook.meterBookId,
            ...formValues,
          };

          try {
            const resp = await billList(params);

            // 处理数据，根据需要进行转换
            const rows = resp.rows || [];
            rows.forEach((row: any) => {
              // 如果没有发行人，设置为 N/A
              if (!row.executor) {
                row.executor = 'N/A';
              }

              // 如果没有发行时间，设置为 N/A
              if (!row.executeTime) {
                row.executeTime = 'N/A';
              }
            });

            return {
              rows: rows,
              total: resp.total || 0
            };
          } catch (error) {
            console.error('获取账单列表失败:', error);
            message.error('获取账单列表失败，请稍后重试');
            return { rows: [], total: 0 };
          }
        },
      },
    },
    id: 'waterfee-bill-issues-detail',
    rowConfig: {
      keyField: 'billId',
    },
  };

  const [BasicTable, tableApi] = useVbenVxeGrid({
    formOptions,
    gridOptions,
    gridEvents: {
      checkboxChange: () => {
        selectedRows.value = tableApi.grid.getCheckboxRecords();
      },
      checkboxAll: () => {
        selectedRows.value = tableApi.grid.getCheckboxRecords();
      },
    },
  });

  // 组件挂载时初始化
  onMounted(() => {
    // 初始化表格数据
    tableApi.query();
  });

  // 返回表册列表
  function handleBack() {
    emit('back');
  }

  // 查看账单详情
  function handleView(record: BillModel) {
    if (record.billId) {
      router.push(`/charges/detail?id=${record.billId}`);
    }
  }

  // 发行单个账单
  function handleIssue(record: BillModel) {
    if (!record.billId) {
      message.warning('无效的账单ID');
      return;
    }

    Modal.confirm({
      title: '发行账单',
      content: `确定要发行账单 ${record.billNumber} 吗？发行后将无法修改账单信息。`,
      onOk: async () => {
        try {
          await billIssue(record.billId as number);
          message.success('账单发行成功');
          tableApi.query();
        } catch (error) {
          console.error('发行失败:', error);
          message.error('发行失败，请稍后重试');
        }
      },
    });
  }

  // 批量发行账单
  function handleBatchIssue() {
    if (!selectedRows.value.length) {
      message.warning('请选择要发行的账单');
      return;
    }

    const billIds = selectedRows.value.filter(row => row.billId !== undefined).map(row => row.billId as number);

    Modal.confirm({
      title: '批量发行账单',
      content: `确定要发行选中的 ${billIds.length} 个账单吗？发行后将无法修改账单信息。`,
      onOk: async () => {
        try {
          await billIssue(billIds);
          message.success('账单批量发行成功');
          tableApi.query();
          // 清空选中
          selectedRows.value = [];
        } catch (error) {
          console.error('批量发行失败:', error);
          message.error('批量发行失败，请稍后重试');
        }
      },
    });
  }

  // 刷新表格数据
  function handleRefresh() {
    tableApi.query();
  }
</script>

<style scoped>
/* 自定义样式 */
:deep(.vxe-table--render-default .vxe-body--row:hover) {
  background-color: #f5f5f5;
}
</style>


