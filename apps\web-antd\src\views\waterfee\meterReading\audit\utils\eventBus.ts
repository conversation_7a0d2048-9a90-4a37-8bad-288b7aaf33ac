// 事件类型枚举
export enum EventType {
  VIEW_DETAILS = 'view_details',
  AUDIT_RECORDS = 'audit_records',
  SAVE_READING = 'save_reading',
  FORM_SUBMIT = 'form_submit',
}

// 创建简单的事件总线
class EventBus {
  private events: Record<string, Array<(data: any) => void>> = {};

  // 添加事件监听器
  on(event: string, callback: (data: any) => void) {
    if (!this.events[event]) {
      this.events[event] = [];
    }
    this.events[event].push(callback);
  }

  // 移除事件监听器
  off(event: string, callback: (data: any) => void) {
    if (!this.events[event]) return;
    this.events[event] = this.events[event].filter(cb => cb !== callback);
  }

  // 触发事件
  emit(event: string, data?: any) {
    if (!this.events[event]) return;
    this.events[event].forEach(callback => callback(data));
  }
}

// 导出事件总线实例
export default new EventBus();
