import type { BasicFetchResult, BasicPageParams } from '#/api/types/common';
import { requestClient } from '#/api/request';
import { preserveBigInt } from '#/utils/json-bigint';

// 抄表记录审核API
enum Api {
  list = '/waterfee/meterReadingRecord/list',
  detail = '/waterfee/meterReadingRecord',
  update = '/waterfee/meterReadingRecord/update',
  intelligent = '/waterfee/meterReadingRecord/intelligent',
  listByMonth = '/waterfee/meterReadingRecord/listByMonth',
}

// 抄表记录审核模型
export interface MeterReadingAuditModel {
  taskId: string;
  meterBookId: string;
  meterBookName?: string;
  businessAreaId: string;
  businessAreaName?: string;
  taskName: string;
  meterType: string;
  isAudited: string;
  readingCount: number;
  createTime?: string;
  // 新增字段
  readerId?: string;
  readerName?: string;
  readingMethod?: string;
  readingDay?: number;
  baseDay?: number;
  bookUserNum?: number;
  planReadingNum?: number;
  actualReadingNum?: number;
  auditorId?: string;
  auditorName?: string;
  [key: string]: any;
}

// 抄表记录详情模型
export interface MeterReadingDetailModel {
  recordId: string;
  meterNo: string;
  userName?: string;
  lastReading: number;
  lastReadingTime: string;
  currentReading: number;
  oldMeterStopReading: number;
  waterUsage: number;
  readingTime: string;
  isAudited: string;
  meterType: string;
  [key: string]: any;
}

// 抄表记录审核查询参数
export interface MeterReadingAuditParams extends BasicPageParams {
  meterBookId?: string;
  isAudited?: string;
  meterType?: string;
  [key: string]: any;
}

// 获取抄表记录审核列表
export function getMeterReadingAuditList(params?: MeterReadingAuditParams) {
  // 处理查询参数
  const queryParams = params ? { ...params } : {};

  // 保留大整数精度
  const safeParams = preserveBigInt(queryParams);

  return requestClient.get<BasicFetchResult<MeterReadingAuditModel[]>>(
    Api.list,
    {
      params: safeParams,
    },
  );
}

// 获取抄表记录详情列表
export function getMeterReadingDetailList(params: {
  meterBookId: string;
  taskId: string;
  pageNum?: number;
  pageSize?: number;
}) {
  // 保留大整数精度
  const safeParams = preserveBigInt(params);

  return requestClient.get<BasicFetchResult<MeterReadingDetailModel[]>>(
    Api.listByMonth,   
    {
      params: safeParams,
    },
  );
}

// 获取抄表记录详细信息
export function getMeterReadingDetail(recordId: string | number) {
  return requestClient.get<MeterReadingDetailModel>(`${Api.detail}/${recordId}`);
}

// 更新抄表记录
export function updateMeterReading(data: {
  recordId: string;
  currentReading: number;
}) {
  return requestClient.put(Api.update, preserveBigInt(data));
}

// 获取智能表抄表记录列表
export function getIntelligentMeterReadingList(meterBookId: string | number, params?: {
  pageNum?: number;
  pageSize?: number;
  isAudited?: string;
  [key: string]: any;
}) {
  // 处理查询参数
  const queryParams = params ? { ...params } : {};

  // 保留大整数精度
  const safeParams = preserveBigInt(queryParams);

  return requestClient.get<BasicFetchResult<MeterReadingDetailModel[]>>(
    `${Api.intelligent}/${meterBookId}`,
    {
      params: safeParams,
    },
  );
}

