import type {
  CommunityListGetResultModel,
  CommunityModel,
  CommunityParams,
} from './model/communityModel';

import type { ID } from '#/api/common';

import { requestClient } from '#/api/request';

enum Api {
  communityList = '/waterfee/community/list',
  communityOptions = '/waterfee/community/getSelectList',
  root = '/waterfee/community',
}

/**
 * 社区列表
 * @param params 查询参数
 * @returns 社区列表
 */
export function communityList(params?: CommunityParams) {
  return requestClient.get<CommunityListGetResultModel>(Api.communityList, {
    params,
  });
}

export function communityOptions(params?: CommunityParams) {
  return requestClient.get<CommunityListGetResultModel>(Api.communityOptions, {
    params,
  });
}

/**
 * 社区详情
 * @param id 社区ID
 * @returns 社区详情
 */
export function communityInfo(id: ID) {
  return requestClient.get<CommunityModel>(`${Api.root}/${id}`);
}

/**
 * 社区新增
 * @param data 参数
 */
export function communityAdd(data: Partial<CommunityModel>) {
  return requestClient.postWithMsg<void>(Api.root, data);
}

/**
 * 社区更新
 * @param data 参数
 */
export function communityUpdate(data: Partial<CommunityModel>) {
  return requestClient.putWithMsg<void>(Api.root, data);
}

/**
 * 社区删除
 * @param id 社区ID
 * @returns void
 */
export function communityRemove(id: ID) {
  return requestClient.deleteWithMsg<void>(`${Api.root}/${id}`);
}
