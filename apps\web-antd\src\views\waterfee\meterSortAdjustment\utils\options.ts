import { ref } from 'vue';
import { areaList } from '#/api/waterfee/area';
import { meterBookList } from '#/api/waterfee/meterbook';
import { listUser } from '#/api/waterfee/user/archivesManage';
import { getDictOptions } from '#/utils/dict';

// 营业区域选项
export const businessAreaOptions = ref<any[]>([]);

// 抄表手册选项
export const meterBookOptions = ref<any[]>([]);

// 用户选项
export const userOptions = ref<any[]>([]);

// 用水性质字典选项
export const waterNatureOptions = ref<any[]>([]);

// 加载营业区域列表
export async function loadBusinessAreaOptions() {
  try {
    const res = await areaList();
    if (res && res.length > 0) {
      businessAreaOptions.value = res.map(item => ({
        label: item.areaName,
        value: item.areaId,
      }));
    }
    return businessAreaOptions.value;
  } catch (error) {
    console.error('加载营业区域列表失败:', error);
    return [];
  }
}

// 加载抄表手册列表
export async function loadMeterBookOptions() {
  try {
    const res = await meterBookList();
    if (res && res.rows) {
      meterBookOptions.value = res.rows.map(item => ({
        label: item.bookName,
        value: item.id,
      }));
    }
    return meterBookOptions.value;
  } catch (error) {
    console.error('加载抄表手册列表失败:', error);
    return [];
  }
}

// 将平面列表转换为树形结构
export function buildAreaTree(list) {
  const map = {};
  const roots = [];

  // 首先将所有项目按照 ID 存入 map
  list.forEach(item => {
    map[item.areaId] = {
      ...item,
      key: item.areaId,
      value: item.areaId,
      title: item.areaName,
      label: item.areaName,
      children: [],
    };
  });

  // 然后将每个项目添加到其父项目的 children 中
  list.forEach(item => {
    const node = map[item.areaId];
    if (item.parentId && map[item.parentId]) {
      map[item.parentId].children.push(node);
    } else {
      roots.push(node);
    }
  });

  return roots;
}

// 加载用户选项
export async function loadUserOptions() {
  try {
    // 假设 userList 函数返回用户列表
    const res = await listUser();
    if (res && res.rows) {
      userOptions.value = res.rows.map((item: any) => ({
        label: item.userName,
        value: item.userId,
      }));
    }
    return userOptions.value;
  } catch (error) {
    console.error('加载用户列表失败:', error);
    return [];
  }
}

// 加载用水性质字典选项
export async function loadWaterNatureOptions() {
  try {
    // 使用 getDictOptions 函数获取字典选项
    const options = await getDictOptions('water_nature');
    waterNatureOptions.value = options;
    return waterNatureOptions.value;
  } catch (error) {
    console.error('加载用水性质字典选项失败:', error);
    return [];
  }
}

// 初始化所有选项
export async function initAllOptions() {
  try {
    await Promise.all([
      loadBusinessAreaOptions(),
      loadMeterBookOptions(),
      loadWaterNatureOptions(),
      loadUserOptions(),
    ]);
    console.log('所有选项加载完成');
  } catch (error) {
    console.error('初始化选项失败:', error);
  }
}
