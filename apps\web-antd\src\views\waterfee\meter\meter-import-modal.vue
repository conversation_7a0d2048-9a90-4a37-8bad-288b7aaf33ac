<script setup lang="ts">
import type { UploadFile } from 'ant-design-vue/es/upload/interface';

import { computed, h, ref, unref, watch } from 'vue';

import { ExcelIcon, InBoxIcon } from '@vben/icons';

import { Modal, Radio, Upload } from 'ant-design-vue';

import { meterImportData, meterImportTemplate } from '#/api/waterfee/meter';
import { commonDownloadExcel } from '#/utils/file/download';

// 定义props接收父组件传递的参数
const props = defineProps({
  meterType: {
    type: Number,
    default: 1,
  },
  open: {
    type: Boolean,
    default: false,
  },
});

// 定义自定义事件
const emit = defineEmits(['update:open', 'reload', 'register']);

// 将props中的meterType同步到本地状态
const meterType = ref(props.meterType);
const fileList = ref<UploadFile[]>([]);
const isLoading = ref(false);

// 监听props的变化，同步到本地状态
watch(
  () => props.meterType,
  (val) => {
    meterType.value = val;
  },
);

// 使用计算属性包装open属性，避免直接修改prop
const modalOpen = computed({
  get: () => props.open,
  set: (val) => emit('update:open', val),
});

// 导入处理
async function handleImport() {
  if (fileList.value.length !== 1) {
    Modal.warning({
      title: '请选择文件',
      content: '请先选择一个Excel文件进行导入',
    });
    return;
  }

  try {
    isLoading.value = true;
    // const formData = new FormData();
    // formData.append('file', fileList.value[0].originFileObj);
    // formData.append('meterType', String(unref(meterType)));

    // console.log('导入参数:', {
    //   meterType: unref(meterType),
    //   fileName: fileList.value[0]?.name,
    //   fileSize: fileList.value[0]?.originFileObj?.size,
    // });

    const data = {
      file: fileList.value[0]!.originFileObj as Blob,
      meterType: String(unref(meterType)),
    };

    const response = await meterImportData(data);

    let modal = Modal.success;
    let content = '';

    if (response.code === 200) {
      const { successCount, failureCount, failureMsg } = response.data || {};

      content = `<div>
        <p>导入结果：共${successCount + failureCount}条</p>
        <p class="text-green-600">成功：${successCount}条</p>
        ${failureCount > 0 ? `<p class="text-red-500">失败：${failureCount}条</p>` : ''}
      </div>`;

      if (failureMsg && failureMsg.length > 0) {
        content += '<div class="mt-2"><p class="font-bold">失败详情：</p><ul>';
        failureMsg.forEach((msg, index) => {
          content += `<li>${index + 1}. ${msg}</li>`;
        });
        content += '</ul></div>';
      }

      emit('reload');
    } else {
      modal = Modal.error;
      content = response.msg || '导入失败';
    }

    closeModal();
    modal({
      content: h('div', {
        class: 'max-h-[260px] overflow-y-auto',
        innerHTML: content,
      }),
      title: '导入结果',
    });
  } catch (error) {
    console.error('导入错误:', error);
    Modal.error({
      title: '导入失败',
      content: error.message || '请求处理失败',
    });
  } finally {
    isLoading.value = false;
  }
}

// 关闭模态框
function closeModal() {
  fileList.value = [];
  emit('update:open', false);
}

// 下载对应类型的模板
function downloadTemplate() {
  return commonDownloadExcel(
    () => meterImportTemplate(unref(meterType)),
    `水表导入模板-${unref(meterType) === 1 ? '机械表' : '智能表'}`,
  );
}

// 文件上传前验证
function beforeUpload(file: File) {
  const isExcel =
    file.type ===
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
    file.type === 'application/vnd.ms-excel';
  if (!isExcel) {
    Modal.error({
      title: '文件类型错误',
      content: '只能上传Excel文件(.xlsx或.xls格式)',
    });
    return false;
  }
  const isLt10M = file.size / 1024 / 1024 < 10;
  if (!isLt10M) {
    Modal.error({
      title: '文件过大',
      content: '文件大小不能超过10MB',
    });
    return false;
  }
  // 返回 false 来阻止自动上传
  return false;
}
</script>

<template>
  <Modal
    v-model:open="modalOpen"
    title="水表导入"
    :mask-closable="false"
    :destroy-on-close="true"
    :width="700"
    @cancel="closeModal"
  >
    <div class="mb-4">
      <Radio.Group v-model:value="meterType" class="mb-4">
        <Radio :value="1">机械表导入</Radio>
        <Radio :value="2">智能表导入</Radio>
      </Radio.Group>
    </div>

    <!-- 拖拽上传区域 -->
    <Upload.Dragger
      v-model:file-list="fileList"
      :before-upload="beforeUpload"
      :max-count="1"
      :show-upload-list="true"
      :multiple="false"
      accept=".xlsx,.xls"
    >
      <p class="ant-upload-drag-icon flex items-center justify-center">
        <InBoxIcon class="text-primary" style="font-size: 48px" />
      </p>
      <p class="ant-upload-text">点击或者拖拽文件到此处上传</p>
      <p class="ant-upload-hint">
        支持单个文件上传，仅支持 .xlsx, .xls 格式文件
      </p>
    </Upload.Dragger>

    <div class="mt-2 flex flex-col gap-2">
      <div class="flex items-center gap-2">
        <a-button type="link" @click="downloadTemplate">
          <div class="flex items-center gap-[4px]">
            <ExcelIcon />
            <span>下载模板</span>
          </div>
        </a-button>
      </div>
      <div class="text-sm text-gray-500">
        <p>提示：导入前请先下载对应类型的模板，按格式填写数据。</p>
      </div>
    </div>

    <template #footer>
      <a-button @click="closeModal">取消</a-button>
      <a-button type="primary" :loading="isLoading" @click="handleImport">
        导入
      </a-button>
    </template>
  </Modal>
</template>
