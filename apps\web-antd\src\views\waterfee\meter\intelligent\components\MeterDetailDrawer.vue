<script setup lang="ts">
import { ref, computed } from 'vue';
import { useVbenDrawer } from '@vben/common-ui';
import { Button, Space, message } from 'ant-design-vue';
import { getIntelligentMeterInfo } from '#/api/waterfee/meter/intelligent';
import { preserveBigInt } from '#/utils/json-bigint';
import { Description, useDescription } from '#/components/description';
import { getDescSchema } from '../detail/intelligent.data';
import { getDictOptions } from '#/utils/dict';
import { areaInfo } from '#/api/waterfee/area';
import { meterBookInfo } from '#/api/waterfee/meterbook';

const emit = defineEmits<{
  reload: [];
}>();

// 状态定义
const detailInfo = ref({});
const loading = ref(false);
const title = computed(() => '智能水表详情');

// 字典数据
const dictData = ref({
  manufacturer: [],
  caliber: [],
  accuracy: [],
  valveStatus: [],
});

// 使用Description组件
const [registerDescription, { setDescProps }] = useDescription({
  column: 2,
  bordered: true,
  labelStyle: {
    fontWeight: 'bold',
    minWidth: '120px',
  },
  schema: [],
});

// 获取字典标签
function getDictLabel(dictType: keyof typeof dictData.value, value: string | number) {
  const dict = dictData.value[dictType];
  const item = dict.find((d) => d.value === value);
  return item?.label || value;
}

// 格式化阀门状态
function formatValveStatus(status: number) {
  return getDictLabel('valveStatus', status);
}

const [BasicDrawer, drawerInstance] = useVbenDrawer({
  onCancel: handleCancel,
  async onOpenChange(isOpen) {
    if (!isOpen) {
      return null;
    }

    try {
      drawerInstance.setState({ loading: true });

      // 加载字典数据
      try {
        const [manufacturerDict, caliberDict, accuracyDict, valveStatusDict, valveControlDict] = await Promise.all([
          getDictOptions('meter_factory'),
          getDictOptions('dnmm'),
          getDictOptions('water_meter_accuracy'),
          getDictOptions('valve_status'),
          getDictOptions('waterfee_valve_control'),
        ]);

        dictData.value = {
          manufacturer: manufacturerDict,
          caliber: caliberDict,
          accuracy: accuracyDict,
          valveStatus: valveStatusDict || valveControlDict,
        };
      } catch (error) {
        console.error('加载字典数据失败:', error);
      }

      // 从 drawerApi 获取数据
      const data = drawerInstance.getData() || {};
      const { id, readonly } = data;

      if (id) {
        try {
          // 直接获取数据，因为 requestClient 已经处理了响应结果
          const data = await getIntelligentMeterInfo(id);
          console.log('获取水表详情成功:', data);
          detailInfo.value = preserveBigInt(data);

          // 获取区域和表册信息
          await fetchAreaAndMeterBookInfo(detailInfo.value);

          // 设置描述项数据
          setDescProps({
            data: detailInfo.value,
            schema: getDescSchema(detailInfo.value)
          });
        } catch (error) {
          console.error('获取水表详情失败:', error);
          message.error(error.message || '获取水表详情失败');
        }
      }
    } catch (error) {
      console.error('Error opening drawer:', error);
      message.error('获取水表详情失败');
    } finally {
      drawerInstance.setState({ loading: false });
    }
  }
});

// 获取区域和表册信息
async function fetchAreaAndMeterBookInfo(meterData) {
  try {
    // 获取区域信息
    if (meterData.businessAreaId) {
      const areaData = await areaInfo(meterData.businessAreaId);
      if (areaData) {
        meterData.businessAreaName = areaData.areaName;
      }
    }

    // 获取表册信息
    if (meterData.meterBookId) {
      const bookData = await meterBookInfo(meterData.meterBookId);
      if (bookData) {
        meterData.meterBookName = bookData.bookName;
      }
    }
  } catch (error) {
    console.error('获取区域和表册信息失败:', error);
  }
}

// 处理关闭抽屉
function handleCancel() {
  drawerInstance.close();
}
</script>

<template>
  <BasicDrawer
    :title="title"
    width="800"
    :closable="true"
    @close="handleCancel"
    v-bind="$attrs"
    :getContainer="false"
  >
    <Description @register="registerDescription" />

    <template #footer>
      <Space>
        <Button @click="handleCancel">关闭</Button>
      </Space>
    </template>
  </BasicDrawer>
</template>

<style scoped>
:deep(.ant-descriptions-item-label) {
  width: 120px;
  text-align: right;
  color: rgba(0, 0, 0, 0.65);
}

:deep(.ant-descriptions-item-content) {
  color: rgba(0, 0, 0, 0.85);
}
</style>



