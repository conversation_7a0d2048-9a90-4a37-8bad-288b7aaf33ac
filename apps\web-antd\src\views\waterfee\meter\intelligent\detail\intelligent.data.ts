import type { DescItem } from '#/components/description';
import { getDictOptions } from '#/utils/dict';
import { renderDict } from '#/utils/render';

// 获取字典名称
export const getDictName = (fieldName: string) => {
  const dictMap: Record<string, string> = {
    manufacturer: 'meter_factory',
    caliber: 'dnmm',
    accuracy: 'water_meter_accuracy',
    valveControl: 'waterfee_valve_control',
    meterType: 'waterfee_meter_type',
    communicationMode: 'waterfee_communication_mode',
    prepaid: 'yes_no',
  };
  return dictMap[fieldName] || '';
};

// 构建描述项
export const getDescSchema = (detailInfo: Record<string, any>): DescItem[] => {
  return [
    {
      field: 'meterId',
      label: '水表ID',
      span: 1,
    },
    {
      field: 'meterNo',
      label: '水表编号',
      span: 1,
    },
    {
      field: 'meterType',
      label: '水表类型',
      span: 1,
      render: (val) => renderDict(val, 'waterfee_meter_type'),
    },
    {
      field: 'manufacturer',
      label: '厂家',
      span: 1,
      render: (val) => renderDict(val, 'meter_factory'),
    },
    {
      field: 'meterCategory',
      label: '水表类别',
      span: 1,
      render: (val) => renderDict(val, 'waterfee_meter_category'),
    },
    {
      field: 'meterClassification',
      label: '水表分类',
      span: 1,
      render: (val) => renderDict(val, 'waterfee_meter_classification'),
    },
    {
      field: 'measurementPurpose',
      label: '计量用途',
      span: 1,
      render: (val) => renderDict(val, 'measure_purposes'),
    },
    {
      field: 'caliber',
      label: '口径',
      span: 1,
      render: (val) => renderDict(val, 'dnmm'),
    },
    {
      field: 'accuracy',
      label: '精度',
      span: 1,
      render: (val) => renderDict(val, 'water_meter_accuracy'),
    },
    {
      field: 'initialReading',
      label: '初始读数',
      span: 1,
    },
    {
      field: 'installDate',
      label: '安装日期',
      span: 1,
    },
    {
      field: 'businessAreaName',
      label: '所属区域',
      span: 1,
    },
    {
      field: 'meterBookName',
      label: '所属表册',
      span: 1,
    },
    {
      field: 'installAddress',
      label: '安装地址',
      span: 1,
    },
    {
      field: 'meterRatio',
      label: '倍率',
      span: 1,
    },
    {
      field: 'communicationMode',
      label: '通信方式',
      span: 1,
      render: (val) => renderDict(val, 'waterfee_communication_mode'),
    },
    {
      field: 'valveControl',
      label: '阀门控制',
      span: 1,
      render: (val) => renderDict(val, 'waterfee_valve_control'),
    },
    {
      field: 'imei',
      label: 'IMEI号',
      span: 1,
    },
    {
      field: 'imsi',
      label: 'IMSI号',
      span: 1,
    },
    {
      field: 'iotPlatform',
      label: '物联网平台',
      span: 1,
    },
    {
      field: 'prepaid',
      label: '是否预付费',
      span: 1,
      render: (val) => renderDict(val, 'yes_no'),
    },
    {
      field: 'userNo',
      label: '用户编号',
      span: 1,
    },
    {
      field: 'userName',
      label: '用户名称',
      span: 1,
    },
    {
      field: 'currentReading',
      label: '当前读数',
      span: 1,
    },
    {
      field: 'createTime',
      label: '创建时间',
      span: 1,
    },
    {
      field: 'updateTime',
      label: '更新时间',
      span: 1,
    },
  ];
};
