import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { getPopupContainer } from '@vben/utils';
import { getDictOptions } from '#/utils/dict';
import { renderDict } from '#/utils/render';
import { h } from 'vue';

// 查询表单架构
export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入任务名称',
    },
    fieldName: 'taskName',
    label: '任务名称',
  },
  {
    component: 'Select',
    componentProps: {
      getPopupContainer,
      options: getDictOptions('waterfee_reading_method'),
      placeholder: '请选择抄表方式',
    },
    fieldName: 'readingMethod',
    label: '抄表方式',
  },
  {
    component: 'Select',
    componentProps: {
      getPopupContainer,
      options: getDictOptions('waterfee_reading_cycle'),
      placeholder: '请选择抄表周期',
    },
    fieldName: 'readingCycle',
    label: '抄表周期',
  },
  {
    component: 'Select',
    componentProps: {
      getPopupContainer,
      options: getDictOptions('waterfee_task_status'),
      placeholder: '请选择任务状态',
    },
    fieldName: 'taskStatus',
    label: '任务状态',
  },
  {
    component: 'RangePicker',
    componentProps: {
      getPopupContainer,
      placeholder: ['开始日期', '结束日期'],
    },
    fieldName: 'startDateRange',
    label: '开始日期',
  },
];

// 表格列配置
export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 40 },
  {
    title: '序号',
    width: 60,
    cellRender: { name: 'SafeIndex' },
  },
  {
    field: 'taskId',
    title: '任务ID',
    visible: false,
  },
  {
    field: 'businessAreaName',
    title: '营业区域',
    minWidth: 130,
    align: 'left',
    slots: {
      default: ({ row }) => {
        return row.businessAreaName || '未分配';
      },
    },
  },
  {
    field: 'meterBookName',
    title: '抄表手册',
    minWidth: 130,
    align: 'left',
    slots: {
      default: ({ row }) => {
        return row.meterBookName || '未分配';
      },
    },
  },
  {
    field: 'readingMethod',
    title: '抄表方式',
    minWidth: 100,
    align: 'center',
    slots: {
      default: ({ row }) => {
        return renderDict(row.readingMethod, 'waterfee_reading_method');
      },
    },
  },
  {
    field: 'readingCycle',
    title: '抄表周期',
    minWidth: 100,
    align: 'center',
    slots: {
      default: ({ row }) => {
        return renderDict(row.readingCycle, 'waterfee_reading_cycle');
      },
    },
  },
  {
    field: 'readingDay',
    title: '抄表例日',
    minWidth: 100,
    align: 'center',
    slots: {
      default: ({ row }) => {
        return h('span', {}, row.readingDay);
      },
    },
  },
  {
    field: 'baseDay',
    title: '抄表基准日',
    minWidth: 100,
    align: 'center',
    slots: {
      default: ({ row }) => {
        return h('span', {}, row.baseDay);
      },
    },
  },
  {
    field: 'lastReadDate',
    title: '上次抄表日期',
    minWidth: 120,
    align: 'center',
  },
  {
    field: 'readerName',
    title: '抄表员',
    minWidth: 100,
    align: 'center',
  },
  {
    field: 'taskStatus',
    title: '任务状态',
    minWidth: 100,
    align: 'center',
    slots: {
      default: ({ row }) => {
        const status = row.taskStatus;
        let color = '';

        switch (status) {
          case '0':
            color = '#ff4d4f';
            break;
          case '1':
            color = '#52c41a';
            break;
          default:
            color = '#d9d9d9';
        }

        return h('div', {
          style: {
            color: color,
            fontWeight: 'bold',
            width: '100%',
            textAlign: 'center',
            display: 'inline-block',
            whiteSpace: 'nowrap',
            overflow: 'hidden',
            textOverflow: 'ellipsis'
          }
        }, renderDict(status, 'waterfee_task_status'));
      },
    },
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    minWidth: 150,
    align: 'center',
  },
];

// 抄表任务表单架构
export const readingTaskFormSchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
    fieldName: 'taskId',
    label: 'ID',
  },
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入任务名称',
    },
    fieldName: 'taskName',
    label: '任务名称',
    rules: 'required',
  },
  {
    component: 'TreeSelect',
    componentProps: {
      fieldNames: { label: 'areaName', value: 'areaId' },
      treeData: [],
      placeholder: '请选择业务区域',
    },
    fieldName: 'businessAreaId',
    label: '业务区域',
    rules: 'required',
  },
  {
    component: 'Select',
    componentProps: {
      options: [],
      placeholder: '请选择抄表手册',
    },
    fieldName: 'meterBookId',
    label: '抄表手册',
    rules: 'required',
  },
  {
    component: 'Select',
    componentProps: {
      options: [],
      placeholder: '请选择抄表员',
    },
    fieldName: 'readerId',
    label: '抄表员',
    rules: 'required',
  },
  {
    component: 'Select',
    componentProps: {
      getPopupContainer,
      options: getDictOptions('waterfee_reading_method'),
      placeholder: '请选择抄表方式',
    },
    fieldName: 'readingMethod',
    label: '抄表方式',
    rules: 'required',
  },
  {
    component: 'Select',
    componentProps: {
      getPopupContainer,
      options: getDictOptions('waterfee_reading_cycle'),
      placeholder: '请选择抄表周期',
    },
    fieldName: 'readingCycle',
    label: '抄表周期',
    rules: 'required',
  },
  {
    component: 'InputNumber',
    componentProps: {
      min: 1,
      max: 31,
      placeholder: '请输入抄表例日',
    },
    fieldName: 'readingDay',
    label: '抄表例日',
    rules: 'required',
  },
  {
    component: 'InputNumber',
    componentProps: {
      min: 1,
      max: 31,
      placeholder: '请输入抄表基准日',
    },
    fieldName: 'baseDay',
    label: '抄表基准日',
    rules: 'required',
  },
  {
    component: 'RadioGroup',
    componentProps: {
      options: [
        { label: '是', value: '1' },
        { label: '否', value: '0' },
      ],
    },
    fieldName: 'isCycle',
    label: '是否循环',
    defaultValue: '1',
  },
  {
    component: 'DatePicker',
    componentProps: {
      getPopupContainer,
      placeholder: '请选择开始日期',
    },
    fieldName: 'startDate',
    label: '开始日期',
    rules: 'required',
  },
  {
    component: 'DatePicker',
    componentProps: {
      getPopupContainer,
      placeholder: '请选择结束日期',
    },
    fieldName: 'endDate',
    label: '结束日期',
    dependencies: {
      show: (values) => values.isCycle === '0',
      triggerFields: ['isCycle'],
    },
  },
  {
    component: 'Input.TextArea',
    componentProps: {
      placeholder: '请输入备注',
      rows: 4,
    },
    fieldName: 'remark',
    label: '备注',
    formItemClass: 'col-span-2',
  },
];
