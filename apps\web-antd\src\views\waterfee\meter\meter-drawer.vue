<script setup lang="ts">
import type { Area } from '#/api/waterfee/area/model';

import { computed, ref } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';
import { addFullName, listToTree } from '@vben/utils';

import { Button, Space } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { areaList, areaNodeList } from '#/api/waterfee/area';
import { meterAdd, meterInfoById, meterUpdate } from '#/api/waterfee/meter';
import { meterBookList } from '#/api/waterfee/meterbook';
import { listUser } from '#/api/waterfee/user/archivesManage';
import { getDictOptions } from '#/utils/dict';

import { preserveBigInt } from '../../../utils/json-bigint';

const emit = defineEmits<{ reload: [] }>();

const isUpdate = ref(false);
const isReadonly = ref(false); // 添加只读模式标记
const meterType = ref(1); // 默认为机械表类型

// 初始化区域和表册选项
const areaOptions = ref<Area[]>([]);
const meterBookOptions = ref<any[]>([]);
const userOptions = ref<any[]>([]);

// 存储原始表册数据
const originalMeterBooks = ref<any[]>([]);

// 生成水表编号 M+时间戳
function generateMeterNo() {
  return `M${Date.now()}`;
}

// 水表类型常量
const meterTypeDict = {
  MECHANICAL: 1, // 机械表
  INTELLIGENT: 2, // 智能表
};

const title = computed(() => {
  // 根据模式显示不同标题
  if (isReadonly.value) {
    return '水表详情';
  }
  return isUpdate.value ? '编辑水表' : '新增水表';
});

// 根据meterType区分表单模式
const formSchema = computed(() => {
  const isIntelligent = meterType.value === meterTypeDict.INTELLIGENT;
  console.log(
    '当前水表类型:',
    meterType.value,
    '是否智能表:',
    isIntelligent,
    '是否只读:',
    isReadonly.value,
  );

  // 基础字段（机械表和智能表共有）
  const baseFields = [
    {
      fieldName: 'meterNo',
      component: 'Input',
      label: '水表编号',
      required: !isReadonly.value, // 只读模式下不校验必填
      componentProps: {
        placeholder: '水表编号自动生成',
        disabled: true,
      },
      formItemClass: 'col-span-2',
    },
    {
      fieldName: 'manufacturer',
      component: 'Select',
      label: '水表厂家',
      required: true,
      componentProps: {
        options: getDictOptions('meter_factory'),
        placeholder: '请选择水表厂家',
      },
      formItemClass: 'col-span-2',
    },
    {
      fieldName: 'meterCategory',
      component: 'Select',
      label: '水表类别',
      required: true,
      componentProps: {
        options: getDictOptions('waterfee_meter_category'),
        placeholder: '请选择水表类别',
      },
    },
    {
      fieldName: 'meterClassification',
      component: 'Select',
      label: '水表分类',
      componentProps: {
        options: getDictOptions('waterfee_meter_classification'),
        placeholder: '请选择水表分类',
      },
    },
    {
      fieldName: 'caliber',
      component: 'Select',
      label: '口径',
      required: true,
      componentProps: {
        options: getDictOptions('dnmm'),
        placeholder: '请选择口径',
      },
    },
    {
      fieldName: 'accuracy',
      component: 'Select',
      label: '精度',
      componentProps: {
        options: getDictOptions('water_meter_accuracy'),
        placeholder: '请选择精度',
      },
    },
    {
      fieldName: 'initialReading',
      component: 'InputNumber',
      label: '初始读数',
      required: true,
      componentProps: {
        min: 0,
        precision: 2,
        placeholder: '请输入初始读数',
      },
    },
    {
      fieldName: 'installDate',
      component: 'DatePicker',
      label: '安装日期',
      required: true,
      componentProps: {
        placeholder: '请选择安装日期',
        valueFormat: 'YYYY-MM-DD HH:mm:ss',
      },
    },
    {
      fieldName: 'businessAreaId',
      component: 'TreeSelect',
      label: '业务区域',
      required: true,
      componentProps: {
        treeData: areaOptions.value,
        fieldNames: { label: 'areaName', value: 'areaId' },
        placeholder: '请选择业务区域',
      },
    },
    {
      fieldName: 'meterBookId',
      component: 'Select',
      label: '抄表手册',
      required: true,
      componentProps: {
        options: meterBookOptions.value,
        placeholder: '请选择抄表手册',
      },
    },
    {
      fieldName: 'installAddress',
      component: 'Input',
      label: '安装地址',
      required: true,
      componentProps: {
        placeholder: '请输入安装地址',
      },
      formItemClass: 'col-span-4',
    },
    {
      fieldName: 'meterRatio',
      component: 'InputNumber',
      label: '表倍率',
      defaultValue: 1,
      componentProps: {
        min: 1,
        placeholder: '请输入表倍率',
      },
    },
    {
      fieldName: 'measurementPurpose',
      component: 'Select',
      label: '计量用途',
      componentProps: {
        options: getDictOptions('measure_purposes'),
        placeholder: '请选择计量用途',
      },
    },
    {
      fieldName: 'waterNature',
      component: 'Select',
      label: '用水性质',
      componentProps: {
        options: getDictOptions('waterfee_user_use_water_nature'),
        placeholder: '请选择用水性质',
      },
    },
    {
      fieldName: 'userId',
      component: 'Select',
      label: '关联用户',
      componentProps: {
        options: userOptions.value,
        placeholder: '请选择关联用户',
        showSearch: true,
        filterOption: (input: string, option: any) => {
          return (
            option.label.toLowerCase().includes(input.toLowerCase()) ||
            option.userNo?.toLowerCase().indexOf(input.toLowerCase()) >= 0
          );
        },
        onChange: (value: any, option: any) => {
          if (option && option.userNo) {
            formApi.setFieldValue('userNo', option.userNo);
          }
        },
      },
    },
    {
      fieldName: 'userNo',
      component: 'Input',
      label: '用户编号',
      componentProps: {
        disabled: true,
        placeholder: '自动填充',
      },
    },
    // 隐藏的字段
    {
      fieldName: 'meterType',
      component: null,
    },
  ];

  // 智能表特有字段
  const intelligentFields = [
    {
      fieldName: 'communicationMode',
      component: 'Select',
      label: '通讯方式',
      required: true,
      componentProps: {
        options: getDictOptions('waterfee_communication_mode'),
        placeholder: '请选择通讯方式',
      },
    },
    {
      fieldName: 'valveControl',
      component: 'Select',
      label: '阀控功能',
      defaultValue: 0,
      componentProps: {
        options: getDictOptions('waterfee_valve_control'),
        placeholder: '请选择阀控功能',
      },
    },
    {
      fieldName: 'imei',
      component: 'Input',
      label: 'IMEI号',
      componentProps: {
        placeholder: '请输入IMEI号',
      },
    },
    {
      fieldName: 'imsi',
      component: 'Input',
      label: 'IMSI号',
      componentProps: {
        placeholder: '请输入IMSI号',
      },
    },
    {
      fieldName: 'iotPlatform',
      component: 'Select',
      label: '物联网平台',
      componentProps: {
        options: getDictOptions('waterfee_iot_platform'),
        placeholder: '请选择物联网平台',
      },
    },
    {
      fieldName: 'prepaid',
      component: 'Select',
      label: '是否预付费',
      defaultValue: 0,
      componentProps: {
        options: getDictOptions('waterfee_prepaid'),
        placeholder: '请选择是否预付费',
      },
    },
  ];

  // 根据表类型返回不同的表单字段
  const allFields = isIntelligent
    ? [...baseFields, ...intelligentFields]
    : baseFields;

  // 只读模式下禁用所有输入控件
  if (isReadonly.value) {
    allFields.forEach((field) => {
      if (field.componentProps) {
        field.componentProps.disabled = true;
      } else {
        field.componentProps = { disabled: true };
      }
    });
  }

  return allFields;
});

const [Form, formApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'col-span-2',
    labelWidth: 100,
  },
  schema: computed(() => {
    // 确保schema正确返回且每个字段都有fieldName
    return formSchema.value;
  }),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-4 !gap-6 !p-4',
});

/**
 * 获取区域树状数据
 * @param areaId 区域ID
 * @param exclude 是否排除当前区域
 */
async function getAreaTree(areaId?: string, exclude = false) {
  try {
    // 获取区域数据
    const response = await (!areaId || exclude ? areaList({}) : areaNodeList(areaId));

    // 记录原始响应
    console.log('区域API原始响应:', response);

    // 确保响应是数组
    let areaArray: Area[] = [];

    if (response) {
      if (Array.isArray(response)) {
        // 如果响应本身就是数组
        areaArray = response;
      } else if (response.rows && Array.isArray(response.rows)) {
        // 如果响应有rows字段且是数组
        areaArray = response.rows;
      } else if (typeof response === 'object') {
        // 如果响应是对象，尝试转换为数组
        console.warn('区域API返回对象而非数组，尝试转换');
        areaArray = Object.values(response).filter(item =>
          item && typeof item === 'object' && 'areaId' in item
        );
      }
    }

    // 检查数组是否为空
    if (!areaArray.length) {
      console.warn('区域数据为空或格式不正确');
      return [];
    }

    // 转换为树形结构
    const treeData = listToTree(areaArray, { id: 'areaId', pid: 'parentId' });

    // 添加全名
    addFullName(treeData, 'areaName', ' / ');

    return treeData;
  } catch (error) {
    console.error('获取区域树数据失败:', error);
    return []; // 返回空数组避免后续错误
  }
}

// 加载区域选项
async function loadAreaOptions() {
  try {
    const areaData = await getAreaTree();

    // 确保areaData是数组且不为空
    if (!areaData || !Array.isArray(areaData)) {
      console.warn('Area data is not valid:', areaData);
      return;
    }

    // 更新ref对象
    areaOptions.value = areaData;
  } catch (error) {
    console.error('Error loading area:', error);
  }
}

/**
 * 确保值是字符串类型
 * @param value 任意值
 * @returns 字符串值
 */
function ensureString(value: any): string {
  if (value === null || value === undefined) {
    return '';
  }
  // 对于特别大的数字，需要先通过JSON序列化保留精度
  if (typeof value === 'number' && value.toString().length > 15) {
    const jsonStr = JSON.stringify({ temp: value });
    const parsed = JSON.parse(jsonStr);
    return String(parsed.temp);
  }
  return String(value);
}

// 加载抄表手册选项
async function loadMeterBookOptions() {
  try {
    // 获取表册数据，response中包含rows字段
    const response = await meterBookList({ pageNum: 1, pageSize: 1000 });

    // 记录并打印原始响应数据
    console.log('表册API原始响应:', response);

    // 安全地处理数据
    let options: any[] = [];
    if (
      response &&
      typeof response === 'object' &&
      Array.isArray(response.rows)
    ) {
      // 保存原始数据便于调试
      originalMeterBooks.value = response.rows;

      // 确保所有ID字段都是字符串
      options = response.rows.map((book: any) => {
        // 确保ID是字符串
        const idStr = String(book.id || '');
        return {
          label: book.bookName || '未命名',
          value: idStr,
          rawId: book.id, // 保存原始值
        };
      });
    }

    // 更新ref对象
    meterBookOptions.value = options;
  } catch (error) {
    console.error('Error loading meter books:', error);
  }
}

// 加载用户选项
async function loadUserOptions() {
  try {
    const users = await listUser({ pageNum: 1, pageSize: 1000 });

    let options: any[] = [];
    if (users && typeof users === 'object' && Array.isArray(users.rows)) {
      // 使用preserveBigInt处理大整数
      const safeData = preserveBigInt(users.rows);

      options = safeData.map((user: any) => ({
        label: `${user.userName || '未命名'}(${user.userNo || '无编号'})`,
        value: String(user.userId), // 确保使用字符串ID
        userNo: user.userNo,
      }));
    }

    userOptions.value = options;
  } catch (error) {
    console.error('Error loading users:', error);
  }
}

const [BasicDrawer, drawerInstance] = useVbenDrawer({
  onCancel: handleCancel,
  onConfirm: handleSubmit,
  async onOpenChange(isOpen) {
    if (!isOpen) {
      return null;
    }

    try {
      drawerInstance.drawerLoading(true);

      // 从drawerApi获取数据
      const data = drawerInstance.getData() || {};
      const { id, meterType: meterTypeParam, readonly } = data;

      // 设置只读模式标志
      isReadonly.value = !!readonly;

      // 设置更新状态
      isUpdate.value = !!id;

      // 设置表类型
      if (meterTypeParam) {
        meterType.value = Number(meterTypeParam);
      } else {
        // 如果是通过按钮传入的类型
        const type = data.type;
        meterType.value = Number(type) || meterTypeDict.MECHANICAL; // 默认为机械表
      }

      // 使用更安全的方式处理异步操作
      await new Promise((resolve) => {
        // 使用微任务队列确保DOM已更新
        setTimeout(async () => {
          try {
            // 表单重置
            await formApi.resetForm();

            // 新增模式设置默认值
            if (!isUpdate.value) {
              try {
                await formApi.setValues({
                  meterType: meterType.value,
                  meterNo: generateMeterNo(), // 自动生成水表编号
                });
              } catch (error) {
                console.error('Error setting default values:', error);
              }
            }

            // 加载下拉选项，忽略错误继续执行
            try {
              await loadAreaOptions();
            } catch (error) {
              console.error('Error loading area options:', error);
            }

            try {
              await loadMeterBookOptions();
            } catch (error) {
              console.error('Error loading meter book options:', error);
            }

            try {
              await loadUserOptions();
            } catch (error) {
              console.error('Error loading user options:', error);
            }

            // 编辑模式加载详情
            if (isUpdate.value && id) {
              try {
                // 确保ID作为字符串传递
                const record = await meterInfoById(String(id));
                if (record) {
                  meterType.value =
                    Number(record.meterType) || meterTypeDict.MECHANICAL;

                  // 对所有ID字段进行字符串转换处理
                  if (record.meterId) record.meterId = String(record.meterId);
                  if (record.userId) record.userId = String(record.userId);
                  if (record.businessAreaId)
                    record.businessAreaId = String(record.businessAreaId);
                  if (record.meterBookId)
                    record.meterBookId = String(record.meterBookId);

                  // 设置表单值
                  await formApi.setValues(record);
                }
              } catch (error) {
                console.error('Error loading meter details:', error);
              }
            }
          } catch (error) {
            console.error('Error in drawer async operations:', error);
          } finally {
            drawerInstance.drawerLoading(false);
            resolve(true);
          }
        }, 200); // 增加延迟时间确保DOM已经渲染
      });
    } catch (error) {
      console.error('Error opening drawer:', error);
      drawerInstance.drawerLoading(false);
    }
  },
});

async function handleSubmit() {
  try {
    // 只读模式下不执行提交
    if (isReadonly.value) {
      drawerInstance.close();
      return;
    }

    // 校验表单
    const values = await formApi.validate();

    if (!values.valid) {
      return;
    }

    const formValues = await formApi.getValues();
    console.log('Form values:', formValues);

    // 设置表类型 - 这里需要确保它是number类型
    formValues.meterType = Number(meterType.value);
    console.log('提交的水表类型:', formValues.meterType);

    // 确保ID字段是字符串类型
    if (isUpdate.value) {
      // 编辑模式下，从drawer实例获取原始ID
      const data = drawerInstance.getData() || {};
      formValues.meterId = ensureString(data.id);
    }
    if (formValues.userId) {
      formValues.userId = ensureString(formValues.userId);
    }
    if (formValues.businessAreaId) {
      formValues.businessAreaId = ensureString(formValues.businessAreaId);
    }
    if (formValues.meterBookId) {
      formValues.meterBookId = ensureString(formValues.meterBookId);
    }

    // 转换为JSON字符串再解析，防止精度丢失
    const jsonStr = JSON.stringify(formValues);
    const jsonValues = JSON.parse(jsonStr);

    console.log('提交表单数据:', jsonValues);
    await (isUpdate.value ? meterUpdate(jsonValues) : meterAdd(jsonValues));

    emit('reload');
    await handleCancel();
  } catch (error) {
    console.error('Error submitting form:', error);
  }
}

async function handleCancel() {
  drawerInstance.close();
  await formApi.resetForm();
}

// 表单提交处理
// async function submitHandle(values: Record<string, any>) {
//   try {
//     console.log('提交表单数据:', values);

//     // 只读模式下不提交
//     if (isReadonly.value) {
//       drawerInstance.close();
//       return true;
//     }

//     // ... existing code ...
//   } catch (error) {
//     console.error('提交表单失败:', error);
//     return false;
//   }
// }
</script>

<template>
  <BasicDrawer :close-on-click-modal="false" :title="title" class="w-[650px]">
    <component :is="Form" />

    <!-- 只读模式下只显示关闭按钮，编辑模式下显示全部按钮 -->
    <template #footer>
      <Space>
        <Button @click="drawerInstance.close()">
          {{ isReadonly ? '关闭' : '取消' }}
        </Button>
        <Button v-if="!isReadonly" type="primary" @click="handleSubmit">
          确定
        </Button>
      </Space>
    </template>
  </BasicDrawer>
</template>
