import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { getPopupContainer } from '@vben/utils';

import { renderDict } from '#/utils/render';

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'userNoOrUserName',
    label: '关键字',
    componentProps: {
      placeholder: '请输入用户编号或名称',
      allowClear: true,
    },
  },
  {
    component: 'RangePicker',
    fieldName: 'timeRange',
    label: '处理日期',
    componentProps: {
      getPopupContainer,
      placeholder: ['开始日期', '结束日期'],
      valueFormat: 'YYYY-MM-DD',
      allowClear: true,
    },
  },
];

export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
  {
    title: '用户编号',
    field: 'userNo',
  },
  {
    title: '用户名',
    field: 'userName',
  },
  {
    title: '水表编号',
    field: 'meterNo',
  },
  {
    title: '用水性质',
    field: 'useWaterNature',
    slots: {
      default: ({ row }) => {
        return renderDict(row.useWaterNature, 'waterfee_user_use_water_nature');
      },
    },
  },
  {
    title: '客户性质',
    field: 'customerNature',
    slots: {
      default: ({ row }) => {
        return renderDict(row.customerNature, 'waterfee_user_customer_nature');
      },
    },
  },
  {
    title: '小区名称',
    field: 'communityName',
  },
  {
    title: '报停原因',
    field: 'deactivateReason',
  },
  {
    title: '报停时间',
    field: 'cancellationTime',
  },
  {
    title: '处理人',
    field: 'createByUserName',
  },
  {
    title: '处理日期',
    field: 'createTime',
  },
];

export const drawerSchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
    fieldName: 'deactivateId',
    label: '主键',
  },
  {
    component: 'Input',
    fieldName: 'userId',
    label: '用户ID',
    rules: 'required',
  },
  {
    component: 'Textarea',
    fieldName: 'deactivateReason',
    label: '停用原因',
    rules: 'required',
  },
  {
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
    fieldName: 'cancellationTime',
    label: '停用时间',
  },
];
