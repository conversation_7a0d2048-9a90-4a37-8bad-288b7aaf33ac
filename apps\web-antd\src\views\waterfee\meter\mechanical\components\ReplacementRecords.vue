<script setup lang="ts">
import { onMounted, ref, watch } from 'vue';

import { Empty, message, Table } from 'ant-design-vue';

const props = defineProps({
  meterId: {
    type: String,
    required: true,
  },
});

const columns = [
  {
    title: '换表日期',
    dataIndex: 'replaceDate',
    key: 'replaceDate',
  },
  {
    title: '旧表编号',
    dataIndex: 'oldMeterNo',
    key: 'oldMeterNo',
  },
  {
    title: '旧表读数',
    dataIndex: 'oldReading',
    key: 'oldReading',
  },
  {
    title: '新表编号',
    dataIndex: 'newMeterNo',
    key: 'newMeterNo',
  },
  {
    title: '新表读数',
    dataIndex: 'newReading',
    key: 'newReading',
  },
  {
    title: '换表原因',
    dataIndex: 'reason',
    key: 'reason',
  },
  {
    title: '操作人',
    dataIndex: 'operator',
    key: 'operator',
  },
  {
    title: '备注',
    dataIndex: 'remark',
    key: 'remark',
  },
];

const dataSource = ref([]);
const loading = ref(false);

// 模拟获取换表记录的函数
async function fetchReplacementRecords(meterId: string) {
  // 移除非数字字符，确保 meterId 是有效的数字字符串
  const cleanMeterId = String(meterId || '').replaceAll(/\D/g, '');
  console.log('获取换表记录, 原始ID:', meterId, '处理后ID:', cleanMeterId);

  loading.value = true;
  try {
    // 这里应该调用实际的API
    // const response = await getReplacementRecords(cleanMeterId);
    // dataSource.value = response.data;

    // 模拟数据
    setTimeout(() => {
      dataSource.value = [
        {
          key: '1',
          replaceDate: '2024-12-15',
          oldMeterNo: 'M202312001',
          oldReading: 985.5,
          newMeterNo: 'M202412001',
          newReading: 0,
          reason: '表损坏',
          operator: '李四',
          remark: '老表已使用5年，表盘磨损严重',
        },
      ];
      loading.value = false;
    }, 1000);
  } catch (error) {
    console.error('获取换表记录失败:', error);
    message.error('获取换表记录失败');
    loading.value = false;
  }
}

watch(
  () => props.meterId,
  (newVal) => {
    if (newVal) {
      fetchReplacementRecords(newVal);
    }
  },
  { immediate: true },
);

onMounted(() => {
  if (props.meterId) {
    fetchReplacementRecords(props.meterId);
  }
});
</script>

<template>
  <div>
    <Table
      :columns="columns"
      :data-source="dataSource"
      :loading="loading"
      :pagination="{ pageSize: 10 }"
      bordered
    >
      <template #emptyText>
        <Empty description="暂无换表记录" />
      </template>
    </Table>
  </div>
</template>

<style scoped>
/* 可以添加自定义样式 */
</style>
