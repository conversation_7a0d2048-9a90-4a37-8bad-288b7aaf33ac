import type {
  PenaltyListGetResultModel,
  PenaltyModel,
  PenaltyParams,
} from './model/penaltyModel';

import type { ID } from '#/api/common';

import { requestClient } from '#/api/request';

enum Api {
  penaltyList = '/waterfee/penalty/list',
  root = '/waterfee/penalty',
}

/**
 * 违约金列表
 * @param params 查询参数
 * @returns 违约金列表
 */
export function penaltyList(params?: PenaltyParams) {
  return requestClient.get<PenaltyListGetResultModel>(Api.penaltyList, {
    params,
  });
}

/**
 * 违约金详情
 * @param id 违约金ID
 * @returns 违约金详情
 */
export function penaltyInfo(id: ID) {
  return requestClient.get<PenaltyModel>(`${Api.root}/${id}`);
}

/**
 * 违约金新增
 * @param data 参数
 */
export function penaltyAdd(data: Partial<PenaltyModel>) {
  return requestClient.postWithMsg<void>(Api.root, data);
}

/**
 * 违约金更新
 * @param data 参数
 */
export function penaltyUpdate(data: Partial<PenaltyModel>) {
  return requestClient.putWithMsg<void>(Api.root, data);
}

/**
 * 违约金删除
 * @param id 违约金ID
 * @returns void
 */
export function penaltyRemove(id: ID) {
  return requestClient.deleteWithMsg<void>(`${Api.root}/${id}`);
}
