import type { PageQuery } from '#/api/common';
import type {
  VatManagementForm,
  VatManagementQuery,
  VatManagementVO,
} from '#/api/waterfee/vatManagement/model.d';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

enum Api {
  list = '/waterfee/vatManagement/list',
  root = '/waterfee/vatManagement',
}

/**
 * 增值税管理导出
 * @param data data
 * @returns void
 */
export function VatManagementExport(data: Partial<VatManagementForm>) {
  return commonExport(`${Api.root}/export`, data);
}

/**
 * 查询增值税管理列表
 * @param params 查询参数
 * @returns 增值税管理列表
 */
export function listVatManagement(params?: PageQuery & VatManagementQuery) {
  return requestClient.get<VatManagementVO>(Api.list, { params });
}

/**
 * 查询增值税管理详细
 * @param id 增值税管理ID
 * @returns 增值税管理信息
 */
export function getVatManagement(id: number | string) {
  return requestClient.get<VatManagementForm>(`${Api.root}/${id}`);
}

/**
 * 新增增值税管理
 * @param data 新增数据
 * @returns void
 */
export function addVatManagement(data: VatManagementForm) {
  return requestClient.postWithMsg<void>(Api.root, data);
}

/**
 * 修改增值税管理
 * @param data 修改数据
 * @returns void
 */
export function updateVatManagement(data: VatManagementForm) {
  return requestClient.putWithMsg<void>(Api.root, data);
}

/**
 * 删除增值税管理
 * @param id 增值税管理ID或ID数组
 * @returns void
 */
export function delVatManagement(id: Array<number | string> | number | string) {
  return requestClient.deleteWithMsg<void>(`${Api.root}/${id}`);
}
