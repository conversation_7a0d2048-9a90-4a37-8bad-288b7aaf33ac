<script setup lang="ts">
import { ref, computed, watch, reactive, onMounted } from 'vue';
import { Drawer, Form, Button, Space, message, Input, Select, DatePicker, InputNumber, Radio, TreeSelect } from 'ant-design-vue';
import { readingTaskFormSchema } from '../reading.data';
import {
  getReadingTaskInfo,
  addReadingTask,
  updateReadingTask
} from '#/api/waterfee/meterReading';
import { areaList } from '#/api/waterfee/area';
import { meterBookList } from '#/api/waterfee/meterbook';
import { userList } from '#/api/system/user';
import { getDictOptions } from '#/utils/dict';

const props = defineProps({
  open: {
    type: Boolean,
    default: false
  },
  id: {
    type: String,
    default: ''
  },
  readonly: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:open', 'reload']);

const loading = ref(false);
const isUpdate = ref(false);

// 计算标题
const title = computed(() => {
  if (props.readonly) {
    return '抄表任务详情';
  }
  return isUpdate.value ? '编辑抄表任务' : '新增抄表任务';
});

// 初始化表单
const formRef = ref();
const formModel = reactive({
  taskName: '',
  businessAreaId: undefined,
  meterBookId: undefined,
  readerId: undefined,
  readingMethod: undefined,
  readingCycle: undefined,
  readingDay: undefined,
  baseDay: undefined,
  isCycle: '1',
  startDate: undefined,
  endDate: undefined,
  remark: '',
});

const formRules = {
  taskName: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
  businessAreaId: [{ required: true, message: '请选择业务区域', trigger: 'change' }],
  meterBookId: [{ required: true, message: '请选择抄表手册', trigger: 'change' }],
  readerId: [{ required: true, message: '请选择抄表员', trigger: 'change' }],
  readingMethod: [{ required: true, message: '请选择抄表方式', trigger: 'change' }],
  readingCycle: [{ required: true, message: '请选择抄表周期', trigger: 'change' }],
  readingDay: [{ required: true, message: '请输入抄表例日', trigger: 'blur' }],
  baseDay: [{ required: true, message: '请输入抄表基准日', trigger: 'blur' }],
  startDate: [{ required: true, message: '请选择开始日期', trigger: 'change' }],
};

// 初始化下拉选项
const areaOptions = ref([]);
const meterBookOptions = ref([]);
const userOptions = ref([]);
const readingMethodOptions = ref(getDictOptions('waterfee_reading_method'));
const readingCycleOptions = ref(getDictOptions('waterfee_reading_cycle'));

// 将平面列表转换为树形结构
function buildTree(list) {
  // 检查输入数据
  if (!list || !Array.isArray(list) || list.length === 0) {
    console.warn('buildTree: 输入数据为空或不是数组');
    return [];
  }

  try {
    // 检查数据格式
    const firstItem = list[0];
    if (!firstItem || typeof firstItem !== 'object') {
      console.warn('buildTree: 数据格式不正确，第一项不是对象');
      return [];
    }

    console.log('buildTree: 第一项数据:', firstItem);

    // 尝试猜测正确的字段名
    const idField = 'areaId' in firstItem ? 'areaId' :
                   'id' in firstItem ? 'id' :
                   'areaID' in firstItem ? 'areaID' :
                   'area_id' in firstItem ? 'area_id' : 'areaId';

    const nameField = 'areaName' in firstItem ? 'areaName' :
                     'name' in firstItem ? 'name' :
                     'area_name' in firstItem ? 'area_name' : 'areaName';

    const parentField = 'parentId' in firstItem ? 'parentId' :
                       'parent_id' in firstItem ? 'parent_id' :
                       'pid' in firstItem ? 'pid' : 'parentId';

    console.log(`buildTree: 使用字段: id=${idField}, name=${nameField}, parent=${parentField}`);

    const map = {};
    const roots = [];

    // 首先将所有项目按照 ID 存入 map
    for (const item of list) {
      if (!item || typeof item !== 'object') continue;

      const id = item[idField];
      if (!id) continue; // 跳过没有ID的项

      map[id] = {
        ...item,
        key: id,
        value: id,
        title: item[nameField] || '未命名',
        label: item[nameField] || '未命名',
        children: [],
      };
    }

    // 然后将每个项目添加到其父项目的 children 中
    for (const item of list) {
      if (!item || typeof item !== 'object') continue;

      const id = item[idField];
      if (!id || !map[id]) continue; // 跳过没有ID或未在map中的项

      const node = map[id];
      const parentId = item[parentField];

      if (parentId && map[parentId]) {
        // 如果有父节点且父节点存在于map中，则添加到父节点的children中
        map[parentId].children.push(node);
      } else {
        // 否则作为根节点
        roots.push(node);
      }
    }

    console.log('buildTree: 生成的根节点数量:', roots.length);
    return roots;
  } catch (error) {
    console.error('buildTree: 构建树结构时出错:', error);
    return [];
  }
}

// 加载业务区域树
async function loadBusinessAreaTree() {
  try {
    console.log('开始加载业务区域树...');

    // 获取区域数据
    const response = await areaList();

    // 记录原始响应
    console.log('区域API原始响应:', response);

    // 确保响应是数组
    let areaArray = [];

    if (response) {
      if (Array.isArray(response)) {
        // 如果响应本身就是数组
        areaArray = response;
      } else if (response.rows && Array.isArray(response.rows)) {
        // 如果响应有rows字段且是数组
        areaArray = response.rows;
      } else if (typeof response === 'object') {
        // 如果响应是对象，尝试转换为数组
        console.warn('区域API返回对象而非数组，尝试转换');
        areaArray = Object.values(response).filter(item =>
          item && typeof item === 'object' && 'areaId' in item
        );
      }
    }

    // 检查数组是否为空
    if (!areaArray.length) {
      console.warn('区域数据为空或格式不正确，尝试使用模拟数据');
      // 使用模拟数据作为备选
      areaArray = [
        { areaId: '1', areaName: '默认区域', parentId: '0' }
      ];
    }

    console.log('处理后的区域数组:', areaArray);

    // 转换为树形结构
    const treeData = buildTree(areaArray);
    console.log('生成的树形结构:', treeData);

    // 更新选项
    areaOptions.value = treeData;
    console.log('areaOptions已更新:', areaOptions.value);

    return treeData;
  } catch (error) {
    console.error('加载业务区域树失败:', error);
    message.error('加载业务区域失败，请刷新重试');

    // 出错时使用空数组
    areaOptions.value = [];
    return [];
  }
}

// 加载抄表手册列表
async function loadMeterBookList() {
  try {
    const res = await meterBookList();
    console.log('抄表手册列表原始数据:', res);
    if (res && res.rows) {
      const options = res.rows.map((item) => ({
        label: item.bookName,
        value: item.id, // 使用 id 字段作为 value
      }));

      console.log('抄表手册选项:', options);
      meterBookOptions.value = options;
    }
  } catch (error) {
    console.error('加载抄表手册列表失败:', error);
  }
}

// 加载抄表员列表
async function loadReaderList() {
  try {
    const res = await userList({
      pageSize: 100,
      pageNum: 1,
    });

    if (res && res.rows) {
      const options = res.rows.map((item) => ({
        label: item.userName,
        value: item.userId,
      }));

      userOptions.value = options;
    }
  } catch (error) {
    console.error('加载抄表员列表失败:', error);
  }
}

// 获取任务详情
async function getTaskDetail(id) {
  try {
    loading.value = true;
    console.log('获取抄表任务详情, ID:', id);

    const res = await getReadingTaskInfo(id);
    console.log('获取到的数据:', res);

    if (res) {
      // 直接使用响应数据替换表单模型
      Object.assign(formModel, {
        taskName: res.taskName || '',
        businessAreaId: res.businessAreaId,
        meterBookId: res.meterBookId,
        readerId: res.readerId,
        readingMethod: res.readingMethod,
        readingCycle: res.readingCycle,
        readingDay: res.readingDay,
        baseDay: res.baseDay,
        isCycle: res.isCycle || '1',
        startDate: res.startDate,
        endDate: res.endDate,
        remark: res.remark || ''
      });

      console.log('设置后的表单模型:', { ...formModel });
    }
  } catch (error) {
    console.error('获取抄表任务详情失败:', error);
    message.error(`获取详情失败：${error.message || '未知错误'}`);
  } finally {
    loading.value = false;
  }
}

// 提交表单
async function handleSubmit() {
  try {
    // 只读模式下不执行提交
    if (props.readonly) {
      emit('update:open', false);
      return;
    }

    loading.value = true;

    // 校验表单
    await formRef.value.validate();

    // 准备提交数据
    const formValues = { ...formModel };

    // 确保ID字段是字符串类型
    if (isUpdate.value) {
      formValues.taskId = props.id;
    }

    // 处理日期字段，确保格式正确
    if (formValues.startDate) {
      // 已经在 DatePicker 中设置了 value-format
      console.log('开始日期:', formValues.startDate);
      // 确保是字符串类型
      formValues.startDate = String(formValues.startDate);
    }

    if (formValues.endDate) {
      // 已经在 DatePicker 中设置了 value-format
      console.log('结束日期:', formValues.endDate);
      // 确保是字符串类型
      formValues.endDate = String(formValues.endDate);
    }

    // 根据 readerId 设置 readerName
    if (formValues.readerId) {
      const selectedReader = userOptions.value.find(item => item.value === formValues.readerId);
      if (selectedReader) {
        formValues.readerName = selectedReader.label;
        console.log('设置抄表员名称:', formValues.readerName);
      }
    }

    // 根据 meterBookId 设置 meterBookName
    if (formValues.meterBookId) {
      const selectedMeterBook = meterBookOptions.value.find(item => item.value === formValues.meterBookId);
      if (selectedMeterBook) {
        formValues.meterBookName = selectedMeterBook.label;
        console.log('设置抄表手册名称:', formValues.meterBookName);
      }
    }

    // 根据 businessAreaId 设置 businessAreaName
    if (formValues.businessAreaId) {
      const findAreaName = (nodes, id) => {
        for (const node of nodes) {
          if (node.value === id) {
            return node.title || node.label;
          }
          if (node.children && node.children.length > 0) {
            const name = findAreaName(node.children, id);
            if (name) return name;
          }
        }
        return null;
      };

      const areaName = findAreaName(areaOptions.value, formValues.businessAreaId);
      if (areaName) {
        formValues.businessAreaName = areaName;
        console.log('设置业务区域名称:', formValues.businessAreaName);
      }
    }

    // 转换为JSON字符串再解析，防止精度丢失
    const jsonStr = JSON.stringify(formValues);
    const jsonValues = JSON.parse(jsonStr);

    console.log('提交表单数据:', jsonValues);

    if (isUpdate.value) {
      // 编辑模式
      await updateReadingTask(jsonValues);
      message.success('更新成功');
    } else {
      // 新增模式
      await addReadingTask(jsonValues);
      message.success('添加成功');
    }

    emit('reload');
    emit('update:open', false);
  } catch (error) {
    console.error('提交抄表任务失败:', error);
    message.error(`提交失败：${error.message || '未知错误'}`);
  } finally {
    loading.value = false;
  }
}

// 取消操作
function handleCancel() {
  emit('update:open', false);
}

// 加载初始数据
onMounted(async () => {
  try {
    await Promise.all([
      loadBusinessAreaTree(),
      loadMeterBookList(),
      loadReaderList(),
    ]);
  } catch (error) {
    console.error('加载初始数据失败:', error);
  }
});

// 监听抽屉打开状态
watch(
  () => props.open,
  async (val) => {
    console.log('抽屉状态变化:', val);
    if (val) {
      // 抽屉打开时
      isUpdate.value = !!props.id;
      console.log('抽屉打开, isUpdate:', isUpdate.value, 'props.id:', props.id, 'props.readonly:', props.readonly);

      try {
        loading.value = true;

        // 重置表单
        formModel.taskName = '';
        formModel.businessAreaId = undefined;
        formModel.meterBookId = undefined;
        formModel.readerId = undefined;
        formModel.readingMethod = undefined;
        formModel.readingCycle = undefined;
        formModel.readingDay = undefined;
        formModel.baseDay = undefined;
        formModel.isCycle = '1';
        formModel.startDate = undefined;
        formModel.endDate = undefined;
        formModel.remark = '';

        console.log('重置表单后:', { ...formModel });

        // 确保每次打开抽屉时都重新加载选项数据
        console.log('抽屉打开，重新加载选项数据...');
        try {
          // 先加载业务区域树
          await loadBusinessAreaTree();
          console.log('业务区域树加载完成');

          // 再加载其他选项
          await Promise.all([
            loadMeterBookList(),
            loadReaderList(),
          ]);
          console.log('所有选项数据加载完成');
        } catch (error) {
          console.error('加载选项数据失败:', error);
          message.error('加载选项数据失败，请刷新重试');
        }

        // 编辑模式或详情模式加载详情
        if (props.id) {
          console.log('准备加载详情, ID:', props.id);
          try {
            await getTaskDetail(props.id);
          } catch (error) {
            console.error('获取抄表任务详情失败:', error);
          }
        }
      } catch (error) {
        console.error('初始化抽屉失败:', error);
      } finally {
        loading.value = false;
      }
    } else {
      console.log('抽屉关闭');
    }
  },
  { immediate: true }
);
</script>

<template>
  <Drawer
    :title="title"
    :open="open"
    :width="700"
    :maskClosable="false"
    :closable="true"
    @close="emit('update:open', false)"
    :afterVisibleChange="(visible) => { if (!visible) emit('update:open', false); }"
    :destroyOnClose="true"
  >
    <div style="padding: 0 20px;">
      <Form
        ref="formRef"
        :model="formModel"
        :rules="formRules"
        :label-col="{ span: 8 }"
        :wrapper-col="{ span: 16 }"
        :disabled="props.readonly"
      >
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px;">
          <Form.Item label="任务名称" name="taskName">
            <Input v-model:value="formModel.taskName" placeholder="请输入任务名称" />
          </Form.Item>
          <Form.Item label="业务区域" name="businessAreaId">
            <TreeSelect
              v-model:value="formModel.businessAreaId"
              :tree-data="areaOptions"
              placeholder="请选择业务区域"
              tree-default-expand-all
              :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
            />
          </Form.Item>
          <Form.Item label="抄表手册" name="meterBookId">
            <Select
              v-model:value="formModel.meterBookId"
              :options="meterBookOptions"
              placeholder="请选择抄表手册"
            />
          </Form.Item>
          <Form.Item label="抄表员" name="readerId">
            <Select
              v-model:value="formModel.readerId"
              :options="userOptions"
              placeholder="请选择抄表员"
            />
          </Form.Item>
          <Form.Item label="抄表方式" name="readingMethod">
            <Select
              v-model:value="formModel.readingMethod"
              :options="readingMethodOptions"
              placeholder="请选择抄表方式"
            />
          </Form.Item>
          <Form.Item label="抄表周期" name="readingCycle">
            <Select
              v-model:value="formModel.readingCycle"
              :options="readingCycleOptions"
              placeholder="请选择抄表周期"
            />
          </Form.Item>
          <Form.Item label="抄表例日" name="readingDay">
            <InputNumber
              v-model:value="formModel.readingDay"
              :min="1"
              :max="31"
              style="width: 100%"
              placeholder="请输入抄表例日"
            />
          </Form.Item>
          <Form.Item label="抄表基准日" name="baseDay">
            <InputNumber
              v-model:value="formModel.baseDay"
              :min="1"
              :max="31"
              style="width: 100%"
              placeholder="请输入抄表基准日"
            />
          </Form.Item>
          <Form.Item label="是否循环" name="isCycle">
            <Radio.Group v-model:value="formModel.isCycle">
              <Radio value="1">是</Radio>
              <Radio value="0">否</Radio>
            </Radio.Group>
          </Form.Item>
          <Form.Item label="开始日期" name="startDate">
            <DatePicker
              v-model:value="formModel.startDate"
              style="width: 100%"
              placeholder="请选择开始日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </Form.Item>
          <Form.Item
            v-if="formModel.isCycle === '0'"
            label="结束日期"
            name="endDate"
          >
            <DatePicker
              v-model:value="formModel.endDate"
              style="width: 100%"
              placeholder="请选择结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </Form.Item>
        </div>
        <Form.Item label="备注" name="remark">
          <Input.TextArea
            v-model:value="formModel.remark"
            :rows="4"
            placeholder="请输入备注"
          />
        </Form.Item>
      </Form>
    </div>

    <template #footer>
      <div style="text-align: right">
        <Space>
          <Button @click="emit('update:open', false)">
            {{ props.readonly ? '关闭' : '取消' }}
          </Button>
          <Button
            v-if="!props.readonly"
            type="primary"
            :loading="loading"
            @click="handleSubmit"
          >
            {{ isUpdate ? '更新' : '添加' }}
          </Button>
        </Space>
      </div>
    </template>
  </Drawer>
</template>

<style scoped>
/* 可以添加自定义样式 */
</style>
