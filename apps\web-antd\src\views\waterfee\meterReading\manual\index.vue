<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { Button, Space, Modal, message } from 'ant-design-vue';
import { Page } from '@vben/common-ui';
import { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined } from '@ant-design/icons-vue';
import { getMeterReadingManualList, deleteMeterReadingManual } from '#/api/waterfee/meterReadingManual';
import { columns, querySchema } from './manual.data';
import { preserveBigInt } from '#/utils/json-bigint';
import { useDictStore } from '#/store/dict';

// 引入组件
import MeterReadingManualDrawer from './components/MeterReadingManualDrawer.vue';

// 引入选项加载函数
import { initAllOptions } from './utils/options';

const dictStore = useDictStore();

// 表单配置
const formOptions = {
  commonConfig: {
    labelWidth: 80,
    componentProps: {
      allowClear: true,
    },
  },
  schema: querySchema(),
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
};

// 表格配置
const gridOptions = {
  columns,
  height: 'auto',
  width: '100%',
  fit: true,
  autoResize: true,
  tableLayout: 'auto',
  scrollX: {
    enabled: true,
    gt: 10,
  },
  resizable: true,
  resizeConfig: {
    minWidth: 80,
  },
  tooltipConfig: {
    showAll: true,
  },
  rowConfig: {
    isHover: true,
    // 设置行索引起始值为0，这样序号列显示的值就是从1开始
    // 因为我们在序号列中使用了 rowIndex + 1
    indexMethod: (row) => row._XID,
  },
  checkboxConfig: {
    // 高亮
    highlight: true,
    // 翻页时保留选中状态
    reserve: true,
  },
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues = {}) => {
        const params = { ...formValues };

        if (params.rangeTime && params.rangeTime.length === 2) {
          params.beginTime = params.rangeTime[0];
          params.endTime = params.rangeTime[1];
          delete params.rangeTime;
        }

        const resp = await getMeterReadingManualList({
          pageNum: page?.currentPage,
          pageSize: page?.pageSize,
          ...params,
        });

        return resp;
      },
    },
  },
  id: 'waterfee-meter-reading-manual-index',
};

// 初始化表格
const [BasicTable, tableApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

// 抽屉状态
const drawerOpen = ref(false);
const drawerProps = ref({
  id: '',
  readonly: true, // 默认只读模式
});

// 查看抄表补录详情
function handleDetail(row) {
  try {
    // 确保ID不会丢失精度
    const safeRecord = preserveBigInt(row);
    const id = String(safeRecord.id);

    console.log('查看抄表补录详情, ID:', id);

    drawerProps.value = {
      id: id,
      readonly: true,
    };
    drawerOpen.value = true;
  } catch (error) {
    console.error('查看抄表补录详情失败:', error);
    message.error(`查看详情失败：${error.message || '未知错误'}`);
  }
}

// 添加抄表补录
function handleAdd() {
  drawerProps.value = {
    id: '',
    readonly: false,
  };
  drawerOpen.value = true;
}

// 编辑抄表补录
function handleEdit(row) {
  try {
    // 确保ID不会丢失精度
    const safeRecord = preserveBigInt(row);
    const id = String(safeRecord.id);

    console.log('编辑抄表补录, ID:', id);

    drawerProps.value = {
      id: id,
      readonly: false,
    };
    drawerOpen.value = true;
  } catch (error) {
    console.error('编辑抄表补录失败:', error);
    message.error(`编辑失败：${error.message || '未知错误'}`);
  }
}

// 删除抄表补录
async function handleDelete(row) {
  try {
    // 确保ID不会丢失精度
    const safeRecord = preserveBigInt(row);
    const id = String(safeRecord.id);

    console.log('删除抄表补录, ID:', id);

    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这条抄表补录记录吗？',
      okType: 'danger',
      async onOk() {
        await deleteMeterReadingManual(id);
        message.success('删除成功');
        // 刷新表格
        tableApi.query();
      },
    });
  } catch (error) {
    console.error('删除抄表补录失败:', error);
    message.error(`删除失败：${error.message || '未知错误'}`);
  }
}

// 批量删除抄表补录
function handleBatchDelete() {
  try {
    // 获取选中的行
    const selectedRows = tableApi.grid.getCheckboxRecords();

    if (!selectedRows || selectedRows.length === 0) {
      message.warning('请选择要删除的抄表补录记录');
      return;
    }

    // 确保ID不会丢失精度
    const ids = selectedRows.map(row => {
      const safeRecord = preserveBigInt(row);
      return String(safeRecord.id);
    });

    console.log('批量删除抄表补录, IDs:', ids);

    Modal.confirm({
      title: '确认批量删除',
      content: `确定要删除选中的${ids.length}条抄表补录记录吗？`,
      okType: 'danger',
      async onOk() {
        await deleteMeterReadingManual(ids);
        message.success('批量删除成功');
        // 刷新表格
        tableApi.query();
      },
    });
  } catch (error) {
    console.error('批量删除抄表补录失败:', error);
    message.error(`批量删除失败：${error.message || '未知错误'}`);
  }
}

// 操作成功后刷新表格
function handleSuccess() {
  tableApi.query();
}

// 组件挂载时初始化所有选项
onMounted(async () => {
  try {
    await initAllOptions();
  } catch (error) {
    console.error('初始化选项失败:', error);
    message.error('加载选项数据失败，请刷新页面重试');
  }
});
</script>

<template>
  <Page :auto-content-height="true">
    <BasicTable @reload="tableApi.query()" table-title="抄表补录列表">
      <template #toolbar-tools>
        <Space>
          <Button type="primary" @click="handleAdd">
            <template #icon><PlusOutlined /></template>
            新增
          </Button>
          <Button type="primary" danger @click="handleBatchDelete">
            <template #icon><DeleteOutlined /></template>
            批量删除
          </Button>
        </Space>
      </template>
      <template #action="{ row }">
        <Space size="small">
          <Button
            type="link"
            size="small"
            style="padding: 0 4px; height: 22px;"
            @click="handleDetail(row)"
          >
            <template #icon><EyeOutlined /></template>
            详情
          </Button>
          <Button
            type="link"
            size="small"
            style="padding: 0 4px; height: 22px;"
            @click="handleEdit(row)"
          >
            <template #icon><EditOutlined /></template>
            编辑
          </Button>
          <Button
            type="link"
            size="small"
            danger
            style="padding: 0 4px; height: 22px;"
            @click="handleDelete(row)"
          >
            <template #icon><DeleteOutlined /></template>
            删除
          </Button>
        </Space>
      </template>
    </BasicTable>

    <!-- 抄表补录抽屉 -->
    <MeterReadingManualDrawer
      :open="drawerOpen"
      @update:open="drawerOpen = $event"
      :id="drawerProps.id"
      :readonly="drawerProps.readonly"
      @reload="handleSuccess"
    />
  </Page>
</template>

<style scoped>
/* 自定义样式 */
</style>
