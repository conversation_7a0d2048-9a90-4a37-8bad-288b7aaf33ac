import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'consolidatedAccountNo',
    label: '合收户编号',
  },
  {
    component: 'Input',
    fieldName: 'consolidatedAccountName',
    label: '合收户名',
  },
  {
    component: 'Input',
    fieldName: 'ownerName',
    label: '户主姓名',
  },
  {
    component: 'Input',
    fieldName: 'ownerPhone',
    label: '户主电话',
  },
];

export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
  {
    title: '主键',
    field: 'consolidatedAccountId',
  },
  {
    title: '合收户编号',
    field: 'consolidatedAccountNo',
  },
  {
    title: '合收户名',
    field: 'consolidatedAccountName',
  },
  {
    title: '户主姓名',
    field: 'ownerName',
  },
  {
    title: '户主电话',
    field: 'ownerPhone',
  },
  {
    title: '备注',
    field: 'remark',
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 180,
  },
];

export const modalSchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
    fieldName: 'consolidatedAccountId',
    label: '主键',
  },
  {
    component: 'Input',
    fieldName: 'consolidatedAccountNo',
    label: '合收户编号',
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'consolidatedAccountName',
    label: '合收户名',
  },
  {
    component: 'Input',
    fieldName: 'ownerName',
    label: '户主姓名',
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'ownerPhone',
    label: '户主电话',
    rules: 'required',
  },
  {
    component: 'Textarea',
    fieldName: 'remark',
    label: '备注',
  },
];
