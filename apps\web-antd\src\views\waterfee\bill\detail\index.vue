<template>
  <Page>
    <div v-if="loading" class="flex justify-center items-center py-10">
      <Spin tip="加载中..." />
    </div>
    <div v-else-if="!billData" class="flex justify-center items-center py-10">
      <Empty description="未找到账单数据" />
    </div>
    <div v-else class="p-4">
      <!-- 用户信息卡片 -->
      <Card class="mb-4">
        <template #title>
          <div class="card-title">
            <span class="title-icon"></span>
            <span>用户信息</span>
          </div>
        </template>
        <Descriptions bordered :column="{ xxl: 3, xl: 3, lg: 3, md: 3, sm: 2, xs: 1 }" :contentStyle="descContentStyle" :labelStyle="descLabelStyle">
          <DescriptionsItem label="用户编号">{{ billData.userNo || '-' }}</DescriptionsItem>
          <DescriptionsItem label="用户名称">{{ billData.userName || billData.customerName || '-' }}</DescriptionsItem>
          <DescriptionsItem label="小区名称">{{ billData.communityName || '-' }}</DescriptionsItem>
          <DescriptionsItem label="表册区域">{{ billData.bookName || billData.meterBookName || '-' }}</DescriptionsItem>
          <DescriptionsItem label="详细地址">{{ billData.address || '-' }}</DescriptionsItem>
          <DescriptionsItem label="用水人数">{{ billData.useWaterNumber || '-' }}</DescriptionsItem>
          <DescriptionsItem label="客户性质">{{ renderDictValue('waterfee_user_customer_nature', billData.customerNature) }}</DescriptionsItem>
          <DescriptionsItem label="用水性质">{{ renderDictValue('waterfee_user_use_water_nature', billData.useWaterNature) }}</DescriptionsItem>
          <DescriptionsItem label="用户状态">{{ renderDictValue('waterfee_user_user_status', billData.userStatus) }}</DescriptionsItem>
          <DescriptionsItem label="手机号码">{{ billData.phoneNumber || '-' }}</DescriptionsItem>
          <DescriptionsItem label="电子邮箱">{{ billData.email || '-' }}</DescriptionsItem>
          <DescriptionsItem label="证件类型">{{ renderDictValue('waterfee_user_certificate_type', billData.certificateType) }}</DescriptionsItem>
          <DescriptionsItem label="证件号码">{{ billData.certificateNumber || '-' }}</DescriptionsItem>
          <DescriptionsItem label="供水日期">{{ billData.supplyDate ? new Date(billData.supplyDate).toLocaleDateString() : (billData.createTime ? new Date(billData.createTime).toLocaleDateString() : '-') }}</DescriptionsItem>
        </Descriptions>
      </Card>

      <!-- 账单期间卡片 -->
      <Card class="mb-4">
        <template #title>
          <div class="card-title">
            <span class="title-icon"></span>
            <span>账单期间</span>
            <span style="margin-left: auto; font-weight: normal; font-size: 14px;">账单日期：{{ billData.billMonth || '-' }}</span>
          </div>
        </template>
        <Table
          :dataSource="billTableData"
          :columns="billColumns"
          :pagination="false"
          bordered
          size="middle"
          :rowKey="(record) => record.key"
        >
        </Table>
        <div class="bill-summary">
          <div class="summary-item">
            <span class="summary-label">费用总额：</span>
            <span class="summary-value">{{ billData.totalAmount || '0.00' }}</span>
          </div>
          <div class="summary-item">
            <span class="summary-label">上期结余：</span>
            <span class="summary-value">{{ billData.previousBalance || '0.00' }}</span>
          </div>
          <div class="summary-item">
            <span class="summary-label">应收合计：</span>
            <span class="summary-value">{{ billData.balanceDue || '0.00' }}</span>
          </div>
        </div>
      </Card>

      <div class="flex justify-center mt-6 space-x-4">
        <Button type="primary" @click="handlePrint">打印账单</Button>
        <Button @click="handleBack">返回</Button>
      </div>
    </div>
  </Page>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed, reactive, h } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import {
  Card,
  Descriptions,
  DescriptionsItem,
  Button,
  Spin,
  Empty,
  Modal,
  message,
  Table,
  Popover
} from 'ant-design-vue';
import { InfoCircleOutlined } from '@ant-design/icons-vue';
import type { ColumnType } from 'ant-design-vue/es/table';
import { Page } from '@vben/common-ui';
import { billInfo } from '#/api/waterfee/bill';
import type { BillModel } from '#/api/waterfee/model/billModel';
import { getDictOptions } from '#/utils/dict';
import { getStandardPrice } from '#/api/waterfee/price/standardPrice';
import { getLadderPrice } from '#/api/waterfee/price/ladderPrice';

const route = useRoute();
const router = useRouter();
const loading = ref(true);
const billData = ref<BillModel | null>(null);

// 描述列表样式
const descLabelStyle = {
  textAlign: 'center' as const,
  fontWeight: 'bold',
  backgroundColor: '#f5f5f5',
  padding: '8px 12px'
};

const descContentStyle = {
  textAlign: 'center' as const,
  padding: '8px 12px'
};

// 账单表格列定义
// 价格方案相关状态
const priceDetail = reactive({
  loading: false,
  type: '', // 'standard' 或 'ladder'
  data: null as any,
  error: ''
});

// 缓存已获取的价格详情
const priceDetailCache = reactive({
  standard: new Map<string | number, any>(),
  ladder: new Map<string | number, any>()
});

// 获取价格方案详情
async function fetchPriceDetail(id: string | number, type: string) {
  if (!id) return;

  // 检查缓存中是否已有数据
  if (type === 'standard' && priceDetailCache.standard.has(id)) {
    priceDetail.data = priceDetailCache.standard.get(id);
    priceDetail.type = 'standard';
    return;
  } else if (type === 'ladder' && priceDetailCache.ladder.has(id)) {
    priceDetail.data = priceDetailCache.ladder.get(id);
    priceDetail.type = 'ladder';
    return;
  }

  priceDetail.loading = true;
  priceDetail.error = '';
  priceDetail.data = null;

  try {
    if (type === 'standard') {
      const response = await getStandardPrice(id);
      priceDetail.data = response;
      priceDetail.type = 'standard';
      // 将数据存入缓存
      priceDetailCache.standard.set(id, response);
    } else if (type === 'ladder') {
      const response = await getLadderPrice(id);
      priceDetail.data = response;
      priceDetail.type = 'ladder';
      // 将数据存入缓存
      priceDetailCache.ladder.set(id, response);
    }
  } catch (error) {
    console.error('获取价格方案详情失败:', error);
    priceDetail.error = '获取价格方案详情失败';
  } finally {
    priceDetail.loading = false;
  }
}

// 渲染价格方案详情
function renderPriceDetail() {
  if (priceDetail.loading) {
    return h('div', {}, '加载中...');
  }

  if (priceDetail.error) {
    return h('div', {}, priceDetail.error);
  }

  if (!priceDetail.data) {
    return h('div', {}, '无价格方案详情');
  }

  if (priceDetail.type === 'standard') {
    return h('div', {}, `标准价格：${priceDetail.data.price || '0.00'} 元/吨`);
  } else if (priceDetail.type === 'ladder') {
    if (!priceDetail.data.priceTiers || priceDetail.data.priceTiers.length === 0) {
      return h('div', {}, '无阶梯价格详情');
    }

    const tiers = priceDetail.data.priceTiers.sort((a: any, b: any) => a.tierNumber - b.tierNumber);

    // 获取账单中的阶梯用量数据
    const tier1Usage = billData.value?.tier1 || 0;
    const tier2Usage = billData.value?.tier2 || 0;
    const tier3Usage = billData.value?.tier3 || 0;

    return h(
      'div',
      { class: 'ladder-price-detail' },
      [
        // 阶梯价格标题
        h('div', { class: 'ladder-price-title' }, ''),

        // 阶梯1
        tiers.length > 0 && h(
          'div',
          { key: 'tier1', class: 'ladder-price-item' },
          [
            h('span', { class: 'ladder-tier-name' }, `阶梯1: `),
            h('span', {}, `${tiers[0].startQuantity || '0'}-${tiers[0].endQuantity || '10.00'} 吨, `),
            h('span', { class: 'ladder-tier-price' }, `${tiers[0].price || '0.00'} 元/吨`),
            h('span', { class: 'ladder-tier-usage' }, ` (使用量: ${tier1Usage} 吨)`)
          ]
        ),

        // 阶梯2
        tiers.length > 1 && h(
          'div',
          { key: 'tier2', class: 'ladder-price-item' },
          [
            h('span', { class: 'ladder-tier-name' }, `阶梯2: `),
            h('span', {}, `${tiers[1].startQuantity || '11.00'}-${tiers[1].endQuantity || '20.00'} 吨, `),
            h('span', { class: 'ladder-tier-price' }, `${tiers[1].price || '0.00'} 元/吨`),
            h('span', { class: 'ladder-tier-usage' }, ` (使用量: ${tier2Usage} 吨)`)
          ]
        ),

        // 阶梯3
        tiers.length > 2 && h(
          'div',
          { key: 'tier3', class: 'ladder-price-item' },
          [
            h('span', { class: 'ladder-tier-name' }, `阶梯3: `),
            h('span', {}, `${tiers[2].startQuantity || '21.00'}-${tiers[2].endQuantity || '以上'} 吨, `),
            h('span', { class: 'ladder-tier-price' }, `${tiers[2].price || '0.00'} 元/吨`),
            h('span', { class: 'ladder-tier-usage' }, ` (使用量: ${tier3Usage} 吨)`)
          ]
        )
      ]
    );
  }

  return h('div', {}, '未知价格方案类型');
}

const billColumns: ColumnType<any>[] = [
  { title: '水表编号', dataIndex: 'meterNo', key: 'meterNo', align: 'center' as const },
  { title: '本期抄表时间', dataIndex: 'readingDate', key: 'readingDate', align: 'center' as const },
  { title: '上期读数值', dataIndex: 'previousReadingValue', key: 'previousReadingValue', align: 'center' as const },
  { title: '本期读数值', dataIndex: 'currentReadingValue', key: 'currentReadingValue', align: 'center' as const },
  { title: '正常用量', dataIndex: 'normalUsage', key: 'normalUsage', align: 'center' as const },
  { title: '用水量', dataIndex: 'consumptionVolume', key: 'consumptionVolume', align: 'center' as const },
  { title: '用水性质', dataIndex: 'waterUseType', key: 'waterUseType', align: 'center' as const },
  {
    title: '价格方案',
    dataIndex: 'priceName',
    key: 'priceName',
    align: 'center' as const,
    customCell: (record: any) => {
      // 使用一个标记来确保接口只被调用一次
      if (!record.priceDetailFetched && record.priceId) {
        record.priceDetailFetched = true;
        // 在渲染后自动获取价格详情
        setTimeout(() => {
          fetchPriceDetail(record.priceId, record.priceType);
        }, 500);
      }

      return {
        class: 'price-cell',
        onClick: () => {
          // 可以添加点击事件，如果需要
        }
      };
    },
    customRender: ({ text }: { text: string }) => {
      return h(
        Popover,
        {
          content: renderPriceDetail(),
          title: '价格方案详情',
          trigger: 'hover',
          overlayClassName: 'price-detail-popover'
        },
        {
          default: () => h(
            'div',
            {
              class: 'price-name-container',
            },
            [
              h(
                'span',
                {
                  class: 'price-name-text',
                },
                text
              ),
              h(
                InfoCircleOutlined,
                {
                  class: 'info-icon',
                }
              )
            ]
          )
        }
      );
    }
  },
  { title: '违约金', dataIndex: 'surchargeAmount', key: 'surchargeAmount', align: 'center' as const },
  { title: '附加费', dataIndex: 'additionalChargeAmount', key: 'additionalChargeAmount', align: 'center' as const },
  { title: '调整金额', dataIndex: 'adjustmentsAmount', key: 'adjustmentsAmount', align: 'center' as const },
  { title: '费用小计', dataIndex: 'subtotal', key: 'subtotal', align: 'center' as const },
];

// 账单表格数据 - 从接口返回的数据中动态获取
const billTableData = computed(() => {
  if (!billData.value) return [];

  // 计算正常用量（如果有上期和本期读数值）
  const normalUsage = billData.value.currentReadingValue && billData.value.previousReadingValue
    ? Number(billData.value.currentReadingValue) - Number(billData.value.previousReadingValue)
    : 0;

  // 格式化日期
  const readingDate = billData.value.billingPeriodEnd
    ? new Date(billData.value.billingPeriodEnd).toLocaleString()
    : '-';

  // 判断价格方案类型
  let priceId = billData.value.pricePlanId;
  let priceType = 'standard'; // 默认为标准价格

  // 如果价格方案名称包含“阶梯”，则认为是阶梯价格
  if (billData.value.pricePlanName && billData.value.pricePlanName.includes('阶梯')) {
    priceType = 'ladder';
  }

  return [{
    key: '1',
    meterNo: billData.value.meterNo || '-',
    readingDate: readingDate,
    previousReadingValue: billData.value.previousReadingValue?.toString() || '-',
    currentReadingValue: billData.value.currentReadingValue?.toString() || '-',
    normalUsage: normalUsage.toString(),
    consumptionVolume: billData.value.consumptionVolume?.toString() || '-',
    waterUseType: renderDictValue('waterfee_user_use_water_nature', billData.value.useWaterNature),
    priceName: billData.value.pricePlanName || '标准水费',
    priceId: priceId,
    priceType: priceType,
    surchargeAmount: billData.value.surchargeAmount?.toString() || '0.00',
    additionalChargeAmount: billData.value.additionalChargeAmount?.toString() || '0.00',
    adjustmentsAmount: billData.value.adjustmentsAmount?.toString() || '0.00',
    subtotal: billData.value.totalAmount?.toString() || '-',
  }];
});

// 格式化日期函数 - 当前不需要
// function formatDate(date: string | undefined) {
//   if (!date) return '';
//   return dayjs(date).format('YYYY-MM-DD');
// }

// 从字典获取值
function renderDictValue(dictType: string, value: string | undefined) {
  if (!value) return '-';

  const dictOptions = getDictOptions(dictType);
  const dictItem = dictOptions.find((item: any) => item.value === value);

  return dictItem ? dictItem.label : value;
}

// 获取账单详情
async function fetchBillData() {
  loading.value = true;
  try {
    const id = route.query.id;
    if (!id) {
      message.error('账单ID不能为空');
      return;
    }

    const response = await billInfo(String(id));
    billData.value = response;
    console.log('账单详情原始数据:', JSON.stringify(response, null, 2));
    console.log('账单详情对象:', response);
  } catch (error) {
    console.error('获取账单详情失败:', error);
    message.error('获取账单详情失败，请稍后重试');
  } finally {
    loading.value = false;
  }
}

// 返回上一页
function handleBack() {
  router.back();
}

// 处理打印
function handlePrint() {
  if (!billData.value) return;

  Modal.confirm({
    title: '打印账单',
    content: `确定要打印用户 ${billData.value?.customerName || '未知'} 的账单吗？`,
    onOk: async () => {
      try {
        // 这里可以调用打印接口或者打开打印预览页面
        // 示例：打开打印预览页面
        // window.open(`/api/waterfee/bill/print/${billData.value.billId}`);

        message.success('打印功能开发中，敬请期待');
      } catch (error) {
        console.error('打印失败:', error);
        message.error('打印失败，请稍后重试');
      }
    },
  });
}

onMounted(() => {
  // 预加载字典数据
  getDictOptions('waterfee_user_customer_nature');
  getDictOptions('waterfee_user_use_water_nature');
  getDictOptions('waterfee_user_certificate_type');
  getDictOptions('waterfee_user_user_status');
  getDictOptions('waterfee_bill_status');

  fetchBillData();
});
</script>

<style scoped>
.card-title {
  display: flex;
  align-items: center;
  font-weight: bold;
  font-size: 16px;
}

.title-icon {
  display: inline-block;
  width: 4px;
  height: 16px;
  background-color: #1890ff;
  margin-right: 8px;
}

.bill-summary {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
  padding: 8px 16px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.summary-item {
  margin-left: 24px;
}

.summary-label {
  font-weight: bold;
  margin-right: 8px;
}

.summary-value {
  color: #1890ff;
  font-weight: bold;
}

.price-name-text {
  color: #333;
}

.ladder-price-detail {
  max-width: 350px;
}

.ladder-price-title {
  font-weight: bold;
  margin-bottom: 8px;
  color: #1890ff;
  border-bottom: 1px solid #e8e8e8;
  padding-bottom: 4px;
}

.ladder-price-item {
  margin-bottom: 6px;
  line-height: 1.5;
}

.ladder-tier-name {
  font-weight: bold;
}

.ladder-tier-price {
  color: #f5222d;
  font-weight: bold;
}

.ladder-tier-usage {
  color: #52c41a;
}

.price-cell {
  cursor: default;
}

.price-name-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.info-icon {
  color: #1890ff;
  margin-left: 5px;
  font-size: 14px;
}

.price-detail-popover {
  max-width: 400px;
}
</style>
