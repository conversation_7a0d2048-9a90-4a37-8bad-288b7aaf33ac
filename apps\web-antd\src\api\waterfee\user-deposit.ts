import type { PageResult } from '#/api/common';
import { requestClient } from '#/api/request';

enum Api {
  getUserDepositBalance = '/waterfee/user-deposit/balance',
  getUserDepositBalanceInfo = '/waterfee/user-deposit/balance/info',
  getUserDepositRecords = '/waterfee/user-deposit/records',
  getUserDepositRecordPage = '/waterfee/user-deposit/records/page',
  deposit = '/waterfee/user-deposit/deposit',
}

/**
 * 获取用户预存款余额
 * @param userId 用户ID
 * @returns 预存款余额
 */
export function getUserDepositBalance(userId: string) {
  return requestClient.get<number>(`${Api.getUserDepositBalance}`, { params: { userId } });
}

/**
 * 获取用户预存款余额信息
 * @param userId 用户ID
 * @returns 预存款余额信息
 */
export function getUserDepositBalanceInfo(userId: string) {
  return requestClient.get(`${Api.getUserDepositBalanceInfo}`, { params: { userId } });
}

/**
 * 查询用户预存款交易记录列表
 * @param userId 用户ID
 * @returns 交易记录列表
 */
export function getUserDepositRecords(userId: string) {
  return requestClient.get(`${Api.getUserDepositRecords}`, { params: { userId } });
}

/**
 * 分页查询用户预存款交易记录
 * @param userId 用户ID
 * @param params 分页参数
 * @returns 分页结果
 */
export function getUserDepositRecordPage(userId: string, params: any) {
  return requestClient.get<PageResult<any>>(`${Api.getUserDepositRecordPage}`, {
    params: {
      userId,
      ...params,
    },
  });
}

/**
 * 充值预存款
 * @param data 充值数据
 * @returns void
 */
export function deposit(data: {
  userId: string;
  amount: number;
  paymentMethod: string;
  remark?: string;
}) {
  return requestClient.postWithMsg<void>(`${Api.deposit}`, data);
}
