<script setup lang="ts">
import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { batchActivateUser } from '#/api/waterfee/user/archivesManage';

const emit = defineEmits<{ reload: [] }>();

const userIds = ref<string[]>([]);
const submitting = ref(false);

const [BasicModal, modalApi] = useVbenModal({
  title: '启用确认',
  class: 'w-[500px]',
  fullscreenButton: false,
  onCancel: handleCancel,
  onConfirm: handleConfirm,
  onOpenChange: async (isOpen) => {
    if (!isOpen) {
      return null;
    }
    const { row } = modalApi.getData() as { row?: any };
    userIds.value = row.map((item: any) => item.userId);
  },
});

async function handleConfirm() {
  try {
    submitting.value = true;
    modalApi.modalLoading(true);

    await batchActivateUser(userIds.value);
    emit('reload');
    await handleCancel();
  } catch (error) {
    console.error(error);
  } finally {
    submitting.value = false;
    modalApi.modalLoading(false);
  }
}

function handleCancel() {
  modalApi.close();
  userIds.value = [];
}
</script>

<template>
  <BasicModal>
    <div class="p-4">
      <div class="flex items-center">
        <div class="mr-3 rounded-full bg-blue-50 p-3 text-blue-600">
          <i class="fas fa-info-circle text-xl"></i>
        </div>
        <div class="text-gray-700">
          <p class="font-medium">确定要启用此用户吗？</p>
          <p class="text-sm text-gray-500">启用后，该用户将恢复正常计量用水</p>
        </div>
      </div>
    </div>
  </BasicModal>
</template>
