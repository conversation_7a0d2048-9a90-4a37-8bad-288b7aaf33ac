import type { PageQuery } from '#/api/common';
import type {
  UserTransferOwnershipRecordForm,
  UserTransferOwnershipRecordQuery,
  UserTransferOwnershipRecordVO,
} from '#/api/waterfee/user/transferOwnershipRecord/model.d';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

enum Api {
  list = '/waterfee/userTransferOwnershipRecord/list',
  root = '/waterfee/userTransferOwnershipRecord',
}

/**
 * 用水用户过户记录导出
 * @param data data
 * @returns void
 */
export function UserTransferOwnershipRecordExport(
  data: Partial<UserTransferOwnershipRecordForm>,
) {
  return commonExport(`${Api.root}/export`, data);
}

/**
 * 查询用水用户过户记录列表
 * @param params 查询参数
 * @returns 用水用户过户记录列表
 */
export function listUserTransferOwnershipRecord(
  params?: PageQuery & UserTransferOwnershipRecordQuery,
) {
  return requestClient.get<UserTransferOwnershipRecordVO>(Api.list, { params });
}

/**
 * 查询用水用户过户记录详细
 * @param transferId 用水用户过户记录ID
 * @returns 用水用户过户记录信息
 */
export function getUserTransferOwnershipRecord(transferId: number | string) {
  return requestClient.get<UserTransferOwnershipRecordForm>(
    `${Api.root}/${transferId}`,
  );
}

/**
 * 新增用水用户过户记录
 * @param data 新增数据
 * @returns void
 */
export function addUserTransferOwnershipRecord(
  data: UserTransferOwnershipRecordForm,
) {
  return requestClient.postWithMsg<void>(Api.root, data);
}

/**
 * 修改用水用户过户记录
 * @param data 修改数据
 * @returns void
 */
export function updateUserTransferOwnershipRecord(
  data: UserTransferOwnershipRecordForm,
) {
  return requestClient.putWithMsg<void>(Api.root, data);
}

/**
 * 删除用水用户过户记录
 * @param transferId 用水用户过户记录ID或ID数组
 * @returns void
 */
export function delUserTransferOwnershipRecord(
  transferId: Array<number | string> | number | string,
) {
  return requestClient.deleteWithMsg<void>(`${Api.root}/${transferId}`);
}
