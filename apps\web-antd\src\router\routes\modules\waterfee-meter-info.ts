import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    path: '/meterInfo/mechanicalDetail',
    name: 'MeterMechanicalDetail',
    component: () => import('#/views/waterfee/meter/mechanical/detail/index.vue'),
    meta: {
      title: '机械水表详情',
      hideMenu: true,
    },
  },
  {
    path: '/meterInfo/intelligentDetail',
    name: 'MeterIntelligentDetail',
    component: () => import('#/views/waterfee/meter/intelligent/detail/index.vue'),
    meta: {
      title: '智能水表详情',
      hideMenu: true,
    },
  },
  {
    path: '/meterInfo/changeAdd',
    name: 'MeterChangeAdd',
    component: () => import('#/views/waterfee/meter/change/add/index.vue'),
    meta: {
      title: '添加换表记录',
      hideMenu: true,
    },
  },
];

export default routes;
