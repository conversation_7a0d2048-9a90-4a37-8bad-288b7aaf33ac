import type { BaseEntity, PageQuery } from '#/api/common';

/**
 * 阶梯价格层级接口 (对应后端 WaterfeePriceTier)
 */
export interface PriceTier {
  /**
   * 主键ID
   */
  id?: string;

  /**
   * 价格配置ID (关联 waterfee_price_config.id)
   */
  priceConfigId?: number | string;

  /**
   * 阶梯序号 (从1开始)
   */
  tierNumber?: number;

  /**
   * 阶梯起始量 (包含)
   */
  startQuantity?: null | string;

  /**
   * 阶梯结束量 (包含, null表示以上)
   */
  endQuantity?: null | string;

  /**
   * 本阶梯价格
   */
  price?: null | string;
}

/**
 * 阶梯价格配置视图对象
 */
export interface LadderPriceVO {
  /**
   * 主键ID
   */
  id: string;

  /**
   * 价格名称
   */
  name?: string; // VO 中可能为 null

  /**
   * 用水性质 (字典值)
   */
  waterUseType: string;

  /**
   * 计算方式 (例如：按年、按月、按人口) (字典值)
   */
  calculationMethod?: string; // VO 中可能为 null

  /**
   * 是否按人口收费 (例如 0=否, 1=是 或 true/false，根据后端类型调整)
   */
  isPopulation?: number; // 后端是 Long (0/1)

  /**
   * 人口数量
   */
  populationCount?: number;

  /**
   * 阶梯详情列表
   */
  priceTiers?: PriceTier[];

  /**
   * 备注
   */
  description?: string; // VO 中可能为 null

  /**
   * 创建时间 (或其他基础字段)
   */
  createTime?: string;
}

export interface LadderPriceRes {
  code: number;
  msg: string;
  total: number;
  rows: LadderPriceVO[];
}

/**
 * 阶梯价格配置表单对象
 */
export interface LadderPriceForm extends BaseEntity {
  /**
   * 主键ID (编辑时需要)
   */
  id?: string;

  /**
   * 价格名称
   */
  name?: string;

  /**
   * 用水性质
   */
  waterUseType?: string; // Bo 中可能为 null

  /**
   * 计算方式
   */
  calculationMethod?: string; // Bo 中可能为 null

  /**
   * 是否按人口收费
   */
  isPopulation?: number; // Bo 中可能为 null

  /**
   * 人口数量
   */
  populationCount?: number;

  /**
   * 阶梯详情列表 (提交时需要)
   */
  priceTiers?: PriceTier[];

  /**
   * 备注
   */
  description?: string; // Bo 中可能为 null
}

/**
 * 阶梯价格配置查询对象
 */
export interface LadderPriceQuery extends PageQuery {
  /**
   * 价格名称 (模糊查询)
   */
  name?: string;

  /**
   * 用水性质 (精确查询)
   */
  waterUseType?: string;

  // 可以根据后端 Bo 添加其他查询条件
}
