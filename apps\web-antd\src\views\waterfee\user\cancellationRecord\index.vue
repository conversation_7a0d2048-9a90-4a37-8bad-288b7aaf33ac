<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';

import type { VxeGridProps } from '#/adapter/vxe-table';
import type { UserCancellationRecordVO } from '#/api/waterfee/user/cancellationRecord/model.d';

import { Page, useVbenDrawer } from '@vben/common-ui';
import { getVxePopupContainer } from '@vben/utils';

import { Popconfirm, Space } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  delUserCancellationRecord,
  listUserCancellationRecord,
  UserCancellationRecordExport,
} from '#/api/waterfee/user/cancellationRecord';
import { commonDownloadExcel } from '#/utils/file/download';

import { columns, querySchema } from './data';
import userCancellationRecordDrawer from './userCancellationRecord-drawer.vue';

const formOptions: VbenFormProps = {
  commonConfig: {
    labelWidth: 80,
    componentProps: {
      allowClear: true,
    },
  },
  schema: querySchema(),
  fieldMappingTime: [['timeRange', ['startTime', 'endTime'], 'YYYY-MM-DD']],
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
};

const gridOptions: VxeGridProps = {
  checkboxConfig: {
    // 高亮
    highlight: true,
    // 翻页时保留选中状态
    reserve: true,
  },
  columns,
  height: 'auto',
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues = {}) => {
        return await listUserCancellationRecord({
          pageNum: page.currentPage,
          pageSize: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  rowConfig: {
    keyField: 'cancellationId',
  },
  id: 'waterfee-userCancellationRecord-index',
};

const [BasicTable, tableApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

const [UserCancellationRecordDrawer, drawerApi] = useVbenDrawer({
  connectedComponent: userCancellationRecordDrawer,
});

// function handleAdd() {
//   drawerApi.setData({});
//   drawerApi.open();
// }

async function handleEdit(record: UserCancellationRecordVO) {
  drawerApi.setData({ id: record.cancellationId });
  drawerApi.open();
}

async function handleDelete(row: UserCancellationRecordVO) {
  await delUserCancellationRecord([row.cancellationId]);
  await tableApi.query();
}

// function handleMultiDelete() {
//   const rows = tableApi.grid.getCheckboxRecords();
//   const ids = rows.map((row: UserCancellationRecordVO) => row.cancellationId);
//   Modal.confirm({
//     title: '提示',
//     okType: 'danger',
//     content: `确认删除选中的${ids.length}条记录吗？`,
//     onOk: async () => {
//       await delUserCancellationRecord(ids);
//       await tableApi.query();
//     },
//   });
// }

function handleDownloadExcel() {
  commonDownloadExcel(
    UserCancellationRecordExport,
    '用水用户销户记录数据',
    tableApi.formApi.form.values,
  );
}
</script>

<template>
  <Page :auto-content-height="true">
    <BasicTable table-title="销户记录">
      <template #toolbar-tools>
        <Space>
          <!-- <a-button
            :disabled="!vxeCheckboxChecked(tableApi)"
            danger
            type="primary"
            v-access:code="['waterfee:userCancellationRecord:remove']"
            @click="handleMultiDelete"
          >
            {{ $t('pages.common.delete') }}
          </a-button>
          <a-button
            type="primary"
            v-access:code="['waterfee:userCancellationRecord:add']"
            @click="handleAdd"
          >
            {{ $t('pages.common.add') }}
          </a-button> -->
          <a-button
            type="primary"
            v-access:code="['waterfee:userCancellationRecord:export']"
            @click="handleDownloadExcel"
          >
            {{ $t('pages.common.export') }}
          </a-button>
        </Space>
      </template>
      <template #action="{ row }">
        <Space>
          <ghost-button
            v-access:code="['waterfee:userCancellationRecord:edit']"
            @click="handleEdit(row)"
          >
            {{ $t('pages.common.edit') }}
          </ghost-button>
          <Popconfirm
            :get-popup-container="getVxePopupContainer"
            placement="left"
            title="确认删除？"
            @confirm="handleDelete(row)"
          >
            <ghost-button
              danger
              v-access:code="['waterfee:userCancellationRecord:remove']"
              @click.stop=""
            >
              {{ $t('pages.common.delete') }}
            </ghost-button>
          </Popconfirm>
        </Space>
      </template>
    </BasicTable>
    <UserCancellationRecordDrawer @reload="tableApi.query()" />
  </Page>
</template>
