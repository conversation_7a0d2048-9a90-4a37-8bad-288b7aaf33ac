<!-- 审批同意的弹窗 -->
<script setup lang="ts">
import type { User } from '#/api/system/user/model';
import type {
  CompleteTaskReqData,
  NextNodeInfo,
} from '#/api/workflow/task/model';

import { h, ref } from 'vue';
import { useRouter } from 'vue-router';

import { useVbenModal } from '@vben/common-ui';
import { cloneDeep } from '@vben/utils';

import { useDebounceFn } from '@vueuse/core';
import { message, Spin } from 'ant-design-vue';
import { omit } from 'lodash-es';

import { useVbenForm } from '#/adapter/form';
import { meterList } from '#/api/waterfee/meter';
import { completeWaterTask, getNextNodeList } from '#/api/workflow/task';
import { getDictOptions } from '#/utils/dict';

import { CopyComponent } from '.';

interface ModalProps {
  taskId: string;
  // 是否具有抄送权限
  copyPermission: boolean;
  // 是有具有选人权限
  assignPermission: boolean;
  // 节点
  nodeCode: string;
}

const emit = defineEmits<{ complete: [] }>();
const router = useRouter();

const keyword = ref('');
const fetching = ref(false);
const modalTitle = ref('');

const [BasicForm, formApi] = useVbenForm({
  commonConfig: {
    // 默认占满两列
    formItemClass: 'col-span-1',
    // 默认label宽度 px
    labelWidth: 100,
    // 通用配置项 会影响到所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
  schema: [
    {
      fieldName: 'taskId',
      component: 'Input',
      label: '任务ID',
      dependencies: {
        show: false,
        triggerFields: [''],
      },
    },
    {
      fieldName: 'nodeCode',
      component: 'Input',
      label: '节点code',
      dependencies: {
        show: false,
        triggerFields: [''],
      },
    },
    // 水表信息
    {
      component: 'ApiSelect',
      fieldName: 'meterNo',
      label: '水表编号',
      componentProps: () => {
        return {
          api: meterList,
          // 禁止本地过滤
          filterOption: false,
          // 如果正在获取数据，使用插槽显示一个loading
          notFoundContent: fetching.value ? undefined : null,
          // 搜索词变化时记录下来， 使用useDebounceFn防抖。
          onSearch: useDebounceFn((value: string) => {
            keyword.value = value;
          }, 300),
          // 远程搜索参数。当搜索词变化时，params也会更新
          params: {
            meterNo: keyword.value || undefined,
            page: 1,
            pageNum: 99,
          },
          showSearch: true,
          afterFetch: (data: any) => {
            return data.rows.map((item: any) => ({
              label: h('div', { class: 'flex flex-col' }, [
                h('span', { class: 'font-medium' }, item.meterNo),
                h(
                  'span',
                  { class: 'text-xs text-gray-500' },
                  item.installAddress || '暂无安装地址',
                ),
              ]),
              value: item.meterNo,
              // 将完整的水表信息保存在extraData中
              extraData: item,
            }));
          },
          // 添加选择事件处理
          onChange: async (value: string, option: any) => {
            if (!value) {
              // 清空相关字段
              await formApi.setValues({
                caliber: '',
                meterType: '',
                installAddress: '',
              });
              return;
            }
            // 从选项中获取完整的水表信息
            const meterInfo = option.extraData;
            if (!meterInfo) {
              console.warn('未找到对应的水表信息');
              return;
            }
            // 设置相关字段的值
            await formApi.setValues({
              caliber: meterInfo.caliber,
              meterType: String(meterInfo.meterType) || undefined,
              installAddress: meterInfo.installAddress || '未找到安装地址',
            });
          },
        };
      },
      renderComponentContent: () => {
        return {
          notFoundContent: fetching.value ? h(Spin) : undefined,
        };
      },
      rules: 'selectRequired',
      formItemClass: 'col-span-2',
    },
    {
      component: h('div', { class: 'mt-1 text-sm' }, [
        h('span', null, '未找到水表信息？点击'),
        h(
          'a',
          {
            class: 'text-primary cursor-pointer ml-1',
            onClick: () => {
              router.push('/register/meter');
            },
          },
          '添加水表',
        ),
      ]),
      fieldName: 'slot-addMeter',
      label: '',
      formItemClass: 'col-span-2',
    },
    {
      fieldName: 'caliber',
      component: 'Select',
      label: '水表口径',
      rules: 'required',
      disabled: true,
      dependencies: {
        show: (values) => !!values.meterNo,
        triggerFields: ['meterNo'],
      },
      componentProps: {
        options: getDictOptions('dnmm'),
      },
    },
    {
      fieldName: 'meterType',
      component: 'Select',
      label: '水表类型',
      rules: 'required',
      disabled: true,
      dependencies: {
        show: (values) => !!values.meterNo,
        triggerFields: ['meterNo'],
      },
      componentProps: {
        options: getDictOptions('waterfee_meter_type'),
      },
    },
    {
      fieldName: 'installAddress',
      component: 'Input',
      label: '水表地址',
      rules: 'required',
      disabled: true,
      dependencies: {
        show: (values) => !!values.meterNo,
        triggerFields: ['meterNo'],
      },
    },
    // 价格信息
    {
      fieldName: 'priceUseWaterNature',
      component: 'Select',
      label: '用水性质',
      rules: 'required',
      componentProps: {
        options: getDictOptions('waterfee_user_use_water_nature'),
      },
    },
    {
      fieldName: 'billingMethod',
      component: 'Select',
      label: '计费方式',
      rules: 'required',
      componentProps: {
        options: getDictOptions('waterfee_user_billing_method'),
      },
    },
    {
      fieldName: 'ifPenalty',
      component: 'RadioGroup',
      label: '是否有违约金',
      componentProps: {
        buttonStyle: 'solid',
        options: getDictOptions('yes_no'),
        optionType: 'button',
      },
    },
    {
      fieldName: 'penaltyType',
      component: 'Select',
      label: '违约金类型',
      dependencies: {
        show: (values) => values.ifPenalty === '1',
        triggerFields: ['ifPenalty'],
        // 如果有违约金,则必填
        rules: (values) => (values.ifPenalty === '1' ? 'required' : null),
      },
      componentProps: {
        options: getDictOptions('waterfee_user_penalty_type'),
      },
    },
    {
      fieldName: 'ifExtraCharge',
      component: 'RadioGroup',
      label: '是否有附加费',
      componentProps: {
        buttonStyle: 'solid',
        options: getDictOptions('yes_no'),
        optionType: 'button',
      },
    },
    {
      fieldName: 'extraChargeType',
      component: 'Select',
      label: '附加费类型',
      dependencies: {
        show: (values) => values.ifExtraCharge === '1',
        triggerFields: ['ifExtraCharge'],
        // 如果有附加费,则必填
        rules: (values) => (values.ifExtraCharge === '1' ? 'required' : null),
      },
      componentProps: {
        options: getDictOptions('waterfee_user_extra_charge_type'),
        mode: 'multiple',
      },
    },
    // 通知方式
    {
      fieldName: 'messageType',
      component: 'CheckboxGroup',
      componentProps: {
        options: [
          { label: '站内信', value: '1', disabled: true },
          { label: '邮件', value: '2' },
          { label: '短信', value: '3' },
        ],
      },
      label: '通知方式',
      defaultValue: ['1'],
    },
    {
      fieldName: 'attachment',
      component: 'FileUpload',
      componentProps: {
        resultField: 'ossId',
        maxNumber: 10,
        maxSize: 20,
        accept: [
          'png',
          'jpg',
          'jpeg',
          'doc',
          'docx',
          'xlsx',
          'xls',
          'ppt',
          'pdf',
        ],
      },
      defaultValue: [],
      label: '附件上传',
      formItemClass: 'items-start',
    },
    {
      fieldName: 'flowCopyList',
      component: 'Input',
      defaultValue: [],
      label: '抄送人',
    },
    {
      fieldName: 'assigneeMap',
      component: 'Input',
      label: '下一步审批人',
    },
    // {
    //   fieldName: 'message',
    //   component: 'Textarea',
    //   label: '审批意见',
    //   formItemClass: 'items-start',
    // },
  ],
  showDefaultActions: false,
  wrapperClass: 'grid-cols-2',
});

// 自定义添加选人属性 给组件v-for绑定
const nextNodeInfo = ref<(NextNodeInfo & { selectUserList: User[] })[]>([]);

const [BasicModal, modalApi] = useVbenModal({
  title: '',
  fullscreenButton: false,
  class: 'min-h-[365px] min-w-[800px]',
  onConfirm: handleSubmit,
  async onOpenChange(isOpen) {
    if (!isOpen) {
      await formApi.resetForm();
      return null;
    }
    modalApi.modalLoading(true);

    const { taskId, copyPermission, assignPermission, nodeCode } =
      modalApi.getData() as ModalProps;

    // 是否显示抄送选择
    formApi.updateSchema([
      {
        fieldName: 'flowCopyList',
        dependencies: {
          if: copyPermission,
          triggerFields: [''],
        },
      },
      {
        fieldName: 'assigneeMap',
        dependencies: {
          if: assignPermission,
          triggerFields: [''],
        },
      },
    ]);
    // 判断是水表安装还是水价设置，如果是水表安装，则把价格相关的字段隐藏
    // 如果是水价设置，则把水表相关的字段隐藏
    if (nodeCode === 'water-meter-installation') {
      modalTitle.value = '水表安装审批';
      formApi.updateSchema([
        {
          fieldName: 'priceUseWaterNature',
          dependencies: {
            show: false,
            triggerFields: [''],
          },
        },
        {
          fieldName: 'billingMethod',
          dependencies: {
            show: false,
            triggerFields: [''],
          },
        },
        {
          fieldName: 'ifPenalty',
          dependencies: {
            show: false,
            triggerFields: [''],
          },
        },
        {
          fieldName: 'penaltyType',
          dependencies: {
            show: false,
            triggerFields: [''],
          },
        },
        {
          fieldName: 'ifExtraCharge',
          dependencies: {
            show: false,
            triggerFields: [''],
          },
        },
        {
          fieldName: 'extraChargeType',
          dependencies: {
            show: false,
            triggerFields: [''],
          },
        },
      ]);
    } else {
      modalTitle.value = '水价设置审批';
      formApi.updateSchema([
        {
          fieldName: 'meterNo',
          dependencies: {
            show: false,
            triggerFields: [''],
          },
        },
        {
          fieldName: 'slot-addMeter',
          dependencies: {
            show: false,
            triggerFields: [''],
          },
        },
      ]);
    }

    // 获取下一节点名称
    if (assignPermission) {
      const resp = await getNextNodeList({ taskId });
      nextNodeInfo.value = resp.map((item) => ({
        ...item,
        // 用于给组件绑定
        selectUserList: [],
      }));
    }

    await formApi.setFieldValue('taskId', taskId);
    await formApi.setFieldValue('nodeCode', nodeCode);

    modalApi.modalLoading(false);
  },
});

async function handleSubmit() {
  try {
    modalApi.modalLoading(true);
    const { valid } = await formApi.validate();
    if (!valid) {
      return;
    }
    const data = cloneDeep(await formApi.getValues());
    // 需要转换数据 抄送人员
    const flowCopyList = (data.flowCopyList as Array<any>).map((item) => ({
      userId: item.userId,
      userName: item.nickName,
    }));
    const requestData = {
      ...omit(data, ['attachment']),
      fileId: data.attachment.join(','),
      taskVariables: {},
      variables: {},
      flowCopyList,
      waterfeeUserDTO: {
        priceUseWaterNature: data.priceUseWaterNature,
        billingMethod: data.billingMethod,
        ifExtraCharge: data.ifExtraCharge,
        ifPenalty: data.ifPenalty,
        penaltyType: data.penaltyType,
        extraChargeType: data.extraChargeType?.join(','),
      },
    } as CompleteTaskReqData;

    // 选人
    if (modalApi.getData()?.assignPermission) {
      // 判断是否选中
      for (const item of nextNodeInfo.value) {
        if (item.selectUserList.length === 0) {
          message.warn(`未选择节点[${item.nodeName}]审批人`);
          return;
        }
      }

      const assigneeMap: { [key: string]: string } = {};
      nextNodeInfo.value.forEach((item) => {
        assigneeMap[item.nodeCode] = item.selectUserList
          .map((u) => u.userId)
          .join(',');
      });
      requestData.assigneeMap = assigneeMap;
    }

    await completeWaterTask(requestData);
    modalApi.close();
    emit('complete');
  } catch (error) {
    console.error(error);
  } finally {
    modalApi.modalLoading(false);
  }
}
</script>

<template>
  <BasicModal :title="modalTitle">
    <BasicForm>
      <template #flowCopyList="slotProps">
        <CopyComponent v-model:user-list="slotProps.modelValue" />
      </template>
      <template #assigneeMap>
        <div
          v-for="item in nextNodeInfo"
          :key="item.nodeCode"
          class="flex items-center gap-2"
        >
          <template v-if="item.permissionFlag">
            <span class="opacity-70">{{ item.nodeName }}</span>
            <CopyComponent
              :allow-user-ids="item.permissionFlag"
              v-model:user-list="item.selectUserList"
            />
          </template>
          <template v-else>
            <span class="text-red-500">没有权限, 请联系管理员</span>
          </template>
        </div>
      </template>
    </BasicForm>
  </BasicModal>
</template>
