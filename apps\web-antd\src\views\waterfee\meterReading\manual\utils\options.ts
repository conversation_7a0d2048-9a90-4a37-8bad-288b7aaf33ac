import { ref } from 'vue';
import { userList } from '#/api/system/user';
import { areaList } from '#/api/waterfee/area';
import { meterBookList } from '#/api/waterfee/meterbook';

// 营业区域选项
export const businessAreaOptions = ref([]);

// 抄表手册选项
export const meterBookOptions = ref([]);

// 抄表员选项
export const readerOptions = ref([]);

// 加载营业区域列表
export async function loadBusinessAreaOptions() {
  try {
    const res = await areaList();
    if (res && res.length > 0) {
      businessAreaOptions.value = res.map(item => ({
        label: item.areaName,
        value: item.areaId,
      }));
    }
    return businessAreaOptions.value;
  } catch (error) {
    console.error('加载营业区域列表失败:', error);
    return [];
  }
}

// 加载抄表手册列表
export async function loadMeterBookOptions() {
  try {
    const res = await meterBookList();
    if (res && res.rows) {
      meterBookOptions.value = res.rows.map(item => ({
        label: item.bookName,
        value: item.id,
      }));
    }
    return meterBookOptions.value;
  } catch (error) {
    console.error('加载抄表手册列表失败:', error);
    return [];
  }
}

// 加载抄表员列表
export async function loadReaderOptions() {
  try {
    const res = await userList({
      pageSize: 100,
      pageNum: 1,
    });

    if (res && res.rows) {
      readerOptions.value = res.rows.map(item => ({
        label: item.userName,
        value: item.userId,
      }));
    }
    return readerOptions.value;
  } catch (error) {
    console.error('加载抄表员列表失败:', error);
    return [];
  }
}

// 初始化所有选项
export async function initAllOptions() {
  try {
    await Promise.all([
      loadBusinessAreaOptions(),
      loadMeterBookOptions(),
      loadReaderOptions(),
    ]);
    console.log('所有选项加载完成');
  } catch (error) {
    console.error('初始化选项失败:', error);
  }
}
