import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { areaList, areaNodeList } from '#/api/waterfee/area';
import type { Area } from '#/api/waterfee/area/model';
import { addFullName, listToTree } from '@vben/utils';

// 查询表单配置
export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'communityNo',
    label: '小区编号',
  },
  {
    component: 'Input',
    fieldName: 'communityName',
    label: '小区名称',
  },
  {
    component: 'TreeSelect',
    componentProps: {
      fieldNames: { label: 'areaName', value: 'areaId' },
      treeData: [],
      placeholder: '请选择所属区域',
    },
    fieldName: 'areaId',
    label: '所属区域',
    rules: 'required',
  },
];

// 表格列定义
export const columns: VxeGridProps['columns'] = [
  {
    title: '序号',
    field: 'id',
    width: 150,
  },
  {
    title: '小区编号',
    field: 'communityNo',
    align: 'left',
  },
  {
    title: '小区名称',
    field: 'communityName',
    align: 'left',
  },
  {
    title: '所属区域',
    field: 'areaName',
    align: 'left',
  },
  {
    title: '小区地址',
    field: 'address',
    align: 'left',
  },
  {
    title: '操作',
    field: 'action',
    width: 180,
    fixed: 'right',
    slots: {
      default: 'action',
    },
  },
];

// 表单配置
export const formSchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
    fieldName: 'id',
    label: 'ID',
  },
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入小区编号',
    },
    fieldName: 'communityNo',
    label: '小区编号',
    rules: 'required',
  },
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入小区名称',
    },
    fieldName: 'communityName',
    label: '小区名称',
    rules: 'required',
  },
  {
    component: 'TreeSelect',
    componentProps: {
      fieldNames: { label: 'areaName', value: 'areaId' },
      treeData: [],
      placeholder: '请选择所属区域',
    },
    fieldName: 'areaId',
    label: '所属区域',
    rules: 'required',
  },
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入小区地址',
    },
    fieldName: 'address',
    label: '小区地址',
    rules: 'required',
  },
];
