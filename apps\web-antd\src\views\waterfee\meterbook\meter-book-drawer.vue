<script setup lang="ts">
import type { Area } from '#/api/waterfee/area/model';
import type { MeterBookModel } from '#/api/waterfee/model/meterbookModel';

import { computed, ref } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';
import { addFullName, cloneDeep, listToTree } from '@vben/utils';

import { useVbenForm } from '#/adapter/form';
import { roleAllocatedList } from '#/api/system/role';
import { areaList, areaNodeList } from '#/api/waterfee/area/index';
import {
  meterBookAdd,
  meterBookInfo,
  meterBookUpdate,
} from '#/api/waterfee/meterbook';

import { ROLE_IDS } from './config';
import { formSchema } from './meterbook.data';

const emit = defineEmits<{ reload: [] }>();

const isUpdate = ref(false);
const title = computed(() => {
  return isUpdate.value ? '编辑抄表手册' : '新增抄表手册';
});

const [BasicForm, formApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'col-span-2',
    labelWidth: 100,
  },
  schema: formSchema(),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-2',
});

/**
 * 获取区域树状数据
 * @param areaId 区域ID
 * @param exclude 是否排除当前区域
 */
async function getAreaTree(areaId?: number | string, exclude = false) {
  let ret: Area[] = [];
  ret = await (!areaId || exclude ? areaList({}) : areaNodeList(areaId));
  console.log(ret);
  const treeData = listToTree(ret, { id: 'areaId', pid: 'parentId' });
  // 添加营业区域名称 如 xx-xx-xx
  addFullName(treeData, 'areaName', ' / ');
  return treeData;
}

// 获取抄表员列表
async function loadAreaOptions() {
  try {
    const areaData = await getAreaTree();
    // // 处理角色信息可能没有users属性的情况
    // const areaUsers = areaData && typeof areaData === 'object' ? (areaData.rows || []) : [];
    // const options = areaUsers.map((area: any) => ({
    //   label: `${area.areaName}`,
    //   value: area.areaId,
    // }));

    formApi.updateSchema([
      {
        componentProps: {
          treeData: areaData,
        },
        fieldName: 'areaId',
      },
    ]);
  } catch (error) {
    console.error('Error loading area:', error);
  }
}

// 获取抄表员列表
async function loadReaderOptions() {
  try {
    const roleData = await roleAllocatedList({
      pageNum: 1,
      pageSize: 1000,
      roleId: ROLE_IDS.METER_READER_ROLE_ID,
    });
    // 处理角色信息可能没有users属性的情况
    const roleUsers =
      roleData && typeof roleData === 'object' ? roleData.rows || [] : [];
    const options = roleUsers.map((user: any) => ({
      label: `${user.nickName}`,
      value: user.userId,
    }));

    formApi.updateSchema([
      {
        componentProps: {
          options,
        },
        fieldName: 'reader',
      },
    ]);
  } catch (error) {
    console.error('Error loading readers:', error);
  }
}

// 获取抄表组长列表
async function loadReaderLeaderOptions() {
  try {
    const roleData = await roleAllocatedList({
      pageNum: 1,
      pageSize: 1000,
      roleId: ROLE_IDS.METER_READER_LEADER_ROLE_ID,
    });
    // 处理角色信息可能没有users属性的情况
    const roleUsers =
      roleData && typeof roleData === 'object' ? roleData.rows || [] : [];
    const options = roleUsers.map((user: any) => ({
      label: `${user.nickName}`,
      value: user.userId,
    }));

    formApi.updateSchema([
      {
        componentProps: {
          options,
        },
        fieldName: 'readerLeader',
      },
    ]);
  } catch (error) {
    console.error('Error loading reader leaders:', error);
  }
}

const [BasicDrawer, drawerApi] = useVbenDrawer({
  onCancel: handleCancel,
  onConfirm: handleSubmit,
  async onOpenChange(isOpen) {
    if (!isOpen) {
      return null;
    }
    drawerApi.drawerLoading(true);

    try {
      const { id } = drawerApi.getData() as { id?: number | string };
      isUpdate.value = !!id;

      // Load dropdown options
      await Promise.all([
        loadReaderOptions(),
        loadReaderLeaderOptions(),
        loadAreaOptions(),
      ]);

      if (isUpdate.value && id) {
        const record = await meterBookInfo(id);
        await formApi.setValues(record);
      } else {
        await formApi.resetForm();
      }
    } catch (error) {
      console.error('Error in drawer open:', error);
    } finally {
      drawerApi.drawerLoading(false);
    }
  },
});

async function handleSubmit() {
  try {
    drawerApi.drawerLoading(true);
    const { valid } = await formApi.validate();
    if (!valid) {
      return;
    }
    const values = cloneDeep(await formApi.getValues());

    // Filter out unnecessary fields to prevent long requests
    const necessaryFields = [
      'id',
      'areaId',
      'bookNo',
      'bookName',
      'readType',
      'readCycle',
      'readDay',
      'readBaseDay',
      'reader',
      'readerLeader',
    ];

    const filteredValues: Partial<MeterBookModel> = {};
    necessaryFields.forEach((field) => {
      if (values[field as keyof typeof values] !== undefined) {
        filteredValues[field as keyof MeterBookModel] =
          values[field as keyof typeof values];
      }
    });

    console.log('Filtered form values:', filteredValues);

    await (isUpdate.value
      ? meterBookUpdate(filteredValues)
      : meterBookAdd(filteredValues));

    emit('reload');
    await handleCancel();
  } catch (error) {
    console.error('Error in form submission:', error);
  } finally {
    drawerApi.drawerLoading(false);
  }
}

async function handleCancel() {
  drawerApi.close();
  await formApi.resetForm();
}
</script>

<template>
  <BasicDrawer :close-on-click-modal="false" :title="title" class="w-[600px]">
    <BasicForm />
  </BasicDrawer>
</template>
