import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { getPopupContainer } from '@vben/utils';

import { getDictOptions } from '#/utils/dict';
import { renderDict } from '#/utils/render';

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'name',
    label: '附加费名称',
  },
  {
    component: 'Select',
    componentProps: {
      getPopupContainer,
      options: getDictOptions('surcharge_calculation_method'),
    },
    fieldName: 'calculationMethod',
    label: '计算方式',
  },
  {
    component: 'Input',
    fieldName: 'fixedAmount',
    label: '固定金额',
  },
  {
    component: 'Input',
    fieldName: 'ratePercent',
    label: '比例(%)',
  },
  {
    component: 'Select',
    componentProps: {
      getPopupContainer,
      options: getDictOptions('surcharge_category'),
    },
    fieldName: 'category',
    label: '附加费类别',
  },
  {
    component: 'Input',
    fieldName: 'remarks',
    label: '备注',
  },
];

export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
  {
    title: '序号',
    field: 'id',
  },
  {
    title: '附加费名称',
    field: 'name',
  },
  {
    title: '计算方式',
    field: 'calculationMethod',
    slots: {
      default: ({ row }) => {
        return renderDict(
          row.calculationMethod,
          'surcharge_calculation_method',
        );
      },
    },
  },
  {
    title: '固定金额（元）',
    field: 'fixedAmount',
  },
  {
    title: '比例(%)',
    field: 'ratePercent',
  },
  {
    title: '附加费类别',
    field: 'category',
    slots: {
      default: ({ row }) => {
        return renderDict(row.category, 'surcharge_category');
      },
    },
  },
  {
    title: '备注',
    field: 'remarks',
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 180,
  },
];

export const drawerSchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
    fieldName: 'id',
    label: '主键',
  },
  {
    component: 'Input',
    fieldName: 'name',
    label: '附加费名称',
    rules: 'required',
  },
  {
    component: 'Select',
    componentProps: {
      options: getDictOptions('surcharge_calculation_method'),
    },
    fieldName: 'calculationMethod',
    label: '计算方式',
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'fixedAmount',
    label: '固定金额（元）',
    // 当计算方式选择为固定金额选项的时候显示此输入框，否则隐藏
    dependencies: {
      if(values) {
        return !!values.calculationMethod && values.calculationMethod === '1';
      },
      // 只有指定的字段改变时，才会触发
      triggerFields: ['calculationMethod'],
    },
  },
  {
    component: 'Input',
    fieldName: 'ratePercent',
    label: '比例(%)',
    // 当计算方式选择为比例金额选项的时候显示此输入框，否则隐藏
    dependencies: {
      if(values) {
        return !!values.calculationMethod && values.calculationMethod === '2';
      },
      // 只有指定的字段改变时，才会触发
      triggerFields: ['calculationMethod'],
    },
  },
  {
    component: 'Select',
    componentProps: {
      options: getDictOptions('surcharge_category'),
    },
    fieldName: 'category',
    label: '附加费类别',
    rules: 'required',
  },
  {
    component: 'Textarea',
    fieldName: 'remarks',
    label: '备注',
  },
];
