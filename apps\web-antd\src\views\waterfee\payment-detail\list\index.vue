<template>
  <Page :auto-content-height="true">
    <BasicTable table-title="缴费明细列表">
      <template #toolbar-tools>
        <Space>
          <Button @click="handleRefresh">
            <template #icon><ReloadOutlined /></template>
            刷新
          </Button>
          <Button type="primary" ghost @click="exportExcel">
            <template #icon><DownloadOutlined /></template>
            导出
          </Button>
        </Space>
      </template>
      <template #action="{ row }">
        <Space>
          <Button type="link" @click="handleView(row)">
            查看
          </Button>
        </Space>
      </template>
    </BasicTable>
  </Page>
</template>

<script lang="ts" setup>
  import { Button, Space, message, Modal } from 'ant-design-vue';
  import { ReloadOutlined, DownloadOutlined } from '@ant-design/icons-vue';
  import { useVbenVxeGrid } from '#/adapter/vxe-table';
  import { Page } from '@vben/common-ui';
  import { paymentDetailList, paymentDetailExport } from '#/api/waterfee/paymentDetail';
  import { commonDownloadExcel } from '#/utils/file/download';
  import { columns, querySchema } from './data';
  import type { PaymentDetailVo } from '#/api/waterfee/model/paymentDetailModel';
  import { ref, onMounted, reactive } from 'vue';
  import { useRouter } from 'vue-router';

  const router = useRouter();

  // 表单配置
  const formOptions = {
    commonConfig: {
      componentProps: {
        allowClear: true,
      },
    },
    schema: querySchema(),
    wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
  };

  // 表格配置
  const gridOptions = {
    columns,
    height: 'auto',
    proxyConfig: {
      ajax: {
        query: async ({ page }: { page?: { currentPage: number; pageSize: number } }, formValues: Record<string, any> = {}) => {
          try {
            const params: any = {
              pageNum: page?.currentPage,
              pageSize: page?.pageSize,
              ...formValues,
            };

            // 处理时间范围
            if (formValues.paymentTimeRange && formValues.paymentTimeRange.length === 2) {
              params.beginTime = formValues.paymentTimeRange[0];
              params.endTime = formValues.paymentTimeRange[1];
              delete params.paymentTimeRange;
            }

            // 调用后端 API
            const response = await paymentDetailList(params);
            return {
              records: response.rows || [],
              total: response.total || 0,
            };
          } catch (error) {
            console.error('获取缴费明细列表失败:', error);
            message.error('获取缴费明细列表失败，请稍后重试');
            return {
              records: [],
              total: 0,
            };
          }
        },
      },
    },
    id: 'waterfee-payment-detail-list-index',
    rowConfig: {
      keyField: 'paymentDetailId',
    },
  };

  const [BasicTable, tableApi] = useVbenVxeGrid({
    formOptions,
    gridOptions,
  });

  // 组件挂载时初始化
  onMounted(() => {
    tableApi.query();
  });

  // 查看详情
  /**
   * @description 查看详情
   */
  function handleView(record: PaymentDetailVo) {
    // 实现查看逻辑，如导航到详情页面或显示模态框
    message.info(`查看缴费明细: ${record.paymentDetailId}`);
    // 如果有详情页面，可以导航到详情页面
    if (record.paymentDetailId) {
      router.push(`/waterfee/payment-detail/detail?id=${record.paymentDetailId}`);
    }
  }

  // 刷新
  function handleRefresh() {
    tableApi.query();
  }

  // 导出 Excel
  function exportExcel() {
    Modal.confirm({
      title: '确认导出',
      content: '确定要导出当前筛选条件下的缴费明细数据吗？',
      onOk: async () => {
        try {
          // 获取表单值，确保它不是 undefined
          const formValues = {};
          if (tableApi.formApi && tableApi.formApi.form && tableApi.formApi.form.values) {
            Object.assign(formValues, tableApi.formApi.form.values);
          }

          commonDownloadExcel(
            paymentDetailExport,
            '缴费明细',
            formValues
          );
        } catch (error) {
          console.error('导出失败:', error);
          message.error('导出失败，请稍后重试');
        }
      },
    });
  }

</script>

<style lang="scss" scoped>
/* Add custom styles if needed */
</style>