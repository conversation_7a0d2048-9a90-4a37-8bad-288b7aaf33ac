<script setup lang="ts">
import type { WaterfeeUserBatchCancellationBo } from '#/api/waterfee/user/archivesManage/model.d';
import type { UserCancellationRecordForm } from '#/api/waterfee/user/cancellationRecord/model.d';

import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';
import { cloneDeep } from '@vben/utils';

import { useVbenForm } from '#/adapter/form';
import { batchCancellationUser } from '#/api/waterfee/user/archivesManage';

const emit = defineEmits<{ reload: [] }>();

const userIds = ref<string[]>([]);

const [BasicForm, formApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
  },
  layout: 'vertical',
  schema: [
    // {
    //   component: 'Input',
    //   dependencies: {
    //     show: () => false,
    //     triggerFields: [''],
    //   },
    //   fieldName: 'userId',
    //   label: '用户ID',
    // },
    {
      component: 'Textarea',
      fieldName: 'cancellationReason',
      label: '销户原因',
      componentProps: {
        rows: 4,
        placeholder: '请输入销户原因',
        showCount: true,
        maxlength: 100,
      },
      rules: 'required',
    },
  ],
  showDefaultActions: false,
});

const [BasicModal, modalApi] = useVbenModal({
  title: '销户确认',
  class: 'w-[500px]',
  fullscreenButton: false,
  onCancel: handleCancel,
  onConfirm: handleConfirm,
  onOpenChange: async (isOpen) => {
    if (!isOpen) {
      return null;
    }
    modalApi.modalLoading(true);
    const { row } = modalApi.getData() as { row?: any };
    userIds.value = row.map((item: any) => item.userId);
    modalApi.modalLoading(false);
  },
});

async function handleConfirm() {
  try {
    modalApi.modalLoading(true);
    const { valid } = await formApi.validate();
    if (!valid) {
      return;
    }
    // 可能会做数据处理 使用cloneDeep深拷贝
    const data = cloneDeep(
      await formApi.getValues(),
    ) as UserCancellationRecordForm;
    data.userIds = userIds.value;
    await batchCancellationUser(data as WaterfeeUserBatchCancellationBo);
    emit('reload');
    await handleCancel();
  } catch (error) {
    console.error(error);
  } finally {
    modalApi.modalLoading(false);
  }
}

async function handleCancel() {
  modalApi.close();
  await formApi.resetForm();
}
</script>

<template>
  <BasicModal>
    <div class="p-4">
      <div class="mb-4 flex items-center">
        <div class="mr-3 rounded-full bg-yellow-50 p-3 text-yellow-600">
          <i class="fas fa-exclamation-triangle text-xl"></i>
        </div>
        <div class="text-gray-700">
          <p class="font-medium">销户后此用户将无法计量用水！</p>
          <p class="text-sm text-gray-500">请确认该用户已结清所有费用</p>
        </div>
      </div>
      <BasicForm />
    </div>
  </BasicModal>
</template>
