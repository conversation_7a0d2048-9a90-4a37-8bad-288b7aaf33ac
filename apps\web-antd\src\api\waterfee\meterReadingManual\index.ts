import type { BasicFetchResult, BasicPageParams } from '#/api/types/common';

import { requestClient } from '#/api/request';
import { preserveBigInt } from '#/utils/json-bigint';

// 抄表补录API
enum Api {
  add = '/waterfee/meterReadingManual',
  // delete = '/waterfee/meterReadingManual',
  // detail = '/waterfee/meterReadingManual',
  getMeterInfo = '/waterfee/meter/no', // 根据水表编号查询水表信息
  list = '/waterfee/meterReadingManual/list',
  meterList = '/waterfee/meter/list', // 获取水表列表
  // update = '/waterfee/meterReadingManual',
}

// 抄表补录模型
export interface MeterReadingManualModel {
  id?: string;
  meterBookId?: string;
  meterBookName?: string;
  businessAreaId?: string;
  businessAreaName?: string;
  meterId?: string;
  meterNo?: string;
  userId?: string;
  userName?: string;
  readerId?: string;
  readerName?: string;
  lastReading?: number;
  currentReading?: number;
  waterUsage?: number;
  readingTime?: string;
  remark?: string;
  createTime?: string;
  updateTime?: string;
  [key: string]: any;
}

// 抄表补录查询参数
export interface MeterReadingManualParams extends BasicPageParams {
  meterBookId?: string;
  businessAreaId?: string;
  meterNo?: string;
  userId?: string;
  readerId?: string;
  readingTimeRange?: [string, string];
  [key: string]: any;
}

// 获取抄表补录列表
export function getMeterReadingManualList(params?: MeterReadingManualParams) {
  // 处理查询参数
  const queryParams = params ? { ...params } : {};

  // 处理日期范围
  if (
    queryParams.readingTimeRange &&
    Array.isArray(queryParams.readingTimeRange)
  ) {
    queryParams.readingTimeBegin = queryParams.readingTimeRange[0];
    queryParams.readingTimeEnd = queryParams.readingTimeRange[1];
    delete queryParams.readingTimeRange;
  }

  // 保留大整数精度
  const safeParams = preserveBigInt(queryParams);

  return requestClient.get<BasicFetchResult<MeterReadingManualModel[]>>(
    Api.list,
    {
      params: safeParams,
    },
  );
}

// 获取抄表补录详情
export function getMeterReadingManualInfo(id: string) {
  return requestClient.get<MeterReadingManualModel>(`${Api.add}/${id}`);
}

// 添加抄表补录
export function addMeterReadingManual(data: MeterReadingManualModel) {
  return requestClient.post(Api.add, preserveBigInt(data));
}

// 更新抄表补录
export function updateMeterReadingManual(data: MeterReadingManualModel) {
  return requestClient.put(Api.add, preserveBigInt(data));
}

// 删除抄表补录
export function deleteMeterReadingManual(ids: string | string[]) {
  return requestClient.delete(`${Api.add}/${ids}`);
}

// 水表信息模型
export interface MeterInfoModel {
  meterId?: string;
  meterNo?: string;
  userId?: string;
  userNo?: string;
  userName?: string;
  lastReading?: number;
  oldMeterEndReading?: number;
  businessAreaId?: string;
  businessAreaName?: string;
  meterBookId?: string;
  meterBookName?: string;
  [key: string]: any;
}

// 根据水表编号查询水表信息
export function getMeterInfoByNo(meterNo: string) {
  return requestClient.get<MeterInfoModel>(`${Api.getMeterInfo}/${meterNo}`);
}

// 水表查询参数
export interface MeterParams extends BasicPageParams {
  meterNo?: string;
  businessAreaId?: string;
  meterBookId?: string;
  [key: string]: any;
}

// 获取水表列表
export function getMeterList(params?: MeterParams) {
  // 处理查询参数
  const queryParams = params ? { ...params } : {};

  // 保留大整数精度
  const safeParams = preserveBigInt(queryParams);

  return requestClient.get<BasicFetchResult<any[]>>(Api.meterList, {
    params: safeParams,
  });
}

// 从 meterReadingRecord 导出抄表记录相关接口
export type {
  HistoryReadingModel,
  LatestReadingModel,
} from '../meterReadingRecord';
export { getHistoryReadings, getLatestReading } from '../meterReadingRecord';
