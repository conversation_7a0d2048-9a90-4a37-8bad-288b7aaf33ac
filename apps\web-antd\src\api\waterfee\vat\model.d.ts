export interface VatManagementVO {
  /**
   * 主键ID
   */
  id: number | string;

  /**
   * 关联账单编号
   */
  billNumber: string;

  /**
   * 发票类型（普通/专用）（字典invoice_type）
   */
  invoiceType: string;

  /**
   * 发票代码
   */
  invoiceCode: string;

  /**
   * 发票号码
   */
  invoiceNumber: string;

  /**
   * 发票状态（已开具/已红冲/已作废）（字典invoice_status）
   */
  invoiceStatus: string;

  /**
   * 价税合计金额
   */
  totalAmount: number;

  /**
   * 不含税金额
   */
  taxExclusiveAmount: number;

  /**
   * 税率（如0.13）
   */
  taxRate: number;

  /**
   * 税额
   */
  taxAmount: number;

  /**
   * 是否含税（1含税/0不含税）（字典yes_no）
   */
  isTaxIncluded: string;

  /**
   * 购方名称
   */
  buyerName: string;

  /**
   * 购方纳税人识别号
   */
  buyerTaxId: number | string;

  /**
   * 购方地址电话
   */
  buyerAddressTel: string;

  /**
   * 购方开户行及账号
   */
  buyerBankAccount: string;

  /**
   * 销方名称
   */
  sellerName: string;

  /**
   * 销方纳税人识别号
   */
  sellerTaxId: number | string;

  /**
   * 销方地址电话
   */
  sellerAddressTel: string;

  /**
   * 销方开户行及账号
   */
  sellerBankAccount: string;

  /**
   * 开票时间
   */
  issueTime: string;

  /**
   * 红冲时间
   */
  redFlushTime: string;

  /**
   * 作废时间
   */
  cancelTime: string;

  /**
   * 操作人ID
   */
  operatorId: number | string;

  /**
   * 红冲原因
   */
  redFlushReason: string;

  /**
   * 作废原因
   */
  cancelReason: string;
}

export interface VatManagementForm extends BaseEntity {
  /**
   * 主键ID
   */
  id?: number | string;

  /**
   * 关联账单编号
   */
  billNumber?: string;

  /**
   * 发票类型（普通/专用）（字典invoice_type）
   */
  invoiceType?: string;

  /**
   * 发票代码
   */
  invoiceCode?: string;

  /**
   * 发票号码
   */
  invoiceNumber?: string;

  /**
   * 发票状态（已开具/已红冲/已作废）（字典invoice_status）
   */
  invoiceStatus?: string;

  /**
   * 价税合计金额
   */
  totalAmount?: number;

  /**
   * 不含税金额
   */
  taxExclusiveAmount?: number;

  /**
   * 税率（如0.13）
   */
  taxRate?: number;

  /**
   * 税额
   */
  taxAmount?: number;

  /**
   * 是否含税（1含税/0不含税）（字典yes_no）
   */
  isTaxIncluded?: string;

  /**
   * 购方名称
   */
  buyerName?: string;

  /**
   * 购方纳税人识别号
   */
  buyerTaxId?: number | string;

  /**
   * 购方地址电话
   */
  buyerAddressTel?: string;

  /**
   * 购方开户行及账号
   */
  buyerBankAccount?: string;

  /**
   * 销方名称
   */
  sellerName?: string;

  /**
   * 销方纳税人识别号
   */
  sellerTaxId?: number | string;

  /**
   * 销方地址电话
   */
  sellerAddressTel?: string;

  /**
   * 销方开户行及账号
   */
  sellerBankAccount?: string;

  /**
   * 开票时间
   */
  issueTime?: string;

  /**
   * 红冲时间
   */
  redFlushTime?: string;

  /**
   * 作废时间
   */
  cancelTime?: string;

  /**
   * 操作人ID
   */
  operatorId?: number | string;

  /**
   * 红冲原因
   */
  redFlushReason?: string;

  /**
   * 作废原因
   */
  cancelReason?: string;
}

export interface VatManagementQuery extends PageQuery {
  /**
   * 关联账单编号
   */
  billNumber?: string;

  /**
   * 发票类型（普通/专用）（字典invoice_type）
   */
  invoiceType?: string;

  /**
   * 发票代码
   */
  invoiceCode?: string;

  /**
   * 发票号码
   */
  invoiceNumber?: string;

  /**
   * 发票状态（已开具/已红冲/已作废）（字典invoice_status）
   */
  invoiceStatus?: string;

  /**
   * 价税合计金额
   */
  totalAmount?: number;

  /**
   * 不含税金额
   */
  taxExclusiveAmount?: number;

  /**
   * 税率（如0.13）
   */
  taxRate?: number;

  /**
   * 税额
   */
  taxAmount?: number;

  /**
   * 是否含税（1含税/0不含税）（字典yes_no）
   */
  isTaxIncluded?: string;

  /**
   * 购方名称
   */
  buyerName?: string;

  /**
   * 购方纳税人识别号
   */
  buyerTaxId?: number | string;

  /**
   * 购方地址电话
   */
  buyerAddressTel?: string;

  /**
   * 购方开户行及账号
   */
  buyerBankAccount?: string;

  /**
   * 销方名称
   */
  sellerName?: string;

  /**
   * 销方纳税人识别号
   */
  sellerTaxId?: number | string;

  /**
   * 销方地址电话
   */
  sellerAddressTel?: string;

  /**
   * 销方开户行及账号
   */
  sellerBankAccount?: string;

  /**
   * 开票时间
   */
  issueTime?: string;

  /**
   * 红冲时间
   */
  redFlushTime?: string;

  /**
   * 作废时间
   */
  cancelTime?: string;

  /**
   * 操作人ID
   */
  operatorId?: number | string;

  /**
   * 红冲原因
   */
  redFlushReason?: string;

  /**
   * 作废原因
   */
  cancelReason?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
