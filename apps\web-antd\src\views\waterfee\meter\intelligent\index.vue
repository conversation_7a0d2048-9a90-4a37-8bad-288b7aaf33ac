<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';
import { Button, message, Popconfirm, Space, Tag } from 'ant-design-vue';
import { DownloadOutlined } from '@ant-design/icons-vue';
import { Page } from '@vben/common-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';

import {
  getIntelligentMeterList,
  exportIntelligentMeter,
  getIntelligentMeterRealtime,
  controlValve
} from '#/api/waterfee/meter/intelligent';
import { columns, querySchema } from './intelligent.data';
// 不再需要抽屉组件
import { preserveBigInt } from '#/utils/json-bigint';
import { ensureMeterIdString } from '#/api/waterfee/meter/index';
import { commonDownloadExcel } from '#/utils/file/download';
import { useDictStore } from '#/store/dict';
import { getDictOptions } from '#/utils/dict';

const router = useRouter();
const dictStore = useDictStore();

const formOptions = {
  commonConfig: {
    labelWidth: 80,
    componentProps: {
      allowClear: true,
    },
  },
  schema: querySchema(),
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
};

const gridOptions = {
  columns,
  height: 'auto',
  width: '100%',
  fit: true,
  autoResize: true,
  tableLayout: 'auto',
  scrollX: {
    enabled: true,
    gt: 10,
  },
  resizable: true,
  resizeConfig: {
    minWidth: 80,
  },
  tooltipConfig: {
    showAll: true,
  },
  proxyConfig: {
    ajax: {
      query: async ({ page }: { page?: { currentPage: number; pageSize: number } }, formValues: Record<string, any> = {}) => {
        const resp = await getIntelligentMeterList({
          pageNum: page?.currentPage,
          pageSize: page?.pageSize,
          ...formValues,
        });
        return resp;
      },
    },
  },
  id: 'waterfee-meter-intelligent-index',
};

const [BasicTable, tableApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

// 不再需要抽屉组件

// 查看详情
function handleDetail(record) {
  try {
    const safeRecord = preserveBigInt(record);
    const meterId = ensureMeterIdString(safeRecord.meterId);

    console.log('查看智能水表详情, ID:', meterId);

    // 跳转到详情页
    router.push(`/meterInfo/intelligentDetail?id=${meterId}`);

  } catch (error) {
    console.error('Error navigating to meter detail page:', error);
    message.error('跳转详情页面失败');
  }
}

// // 编辑水表
// function handleEdit(record: any) {
//   try {
//     const safeRecord = preserveBigInt(record);
//     const meterId = ensureMeterIdString(safeRecord.meterId);

//     router.push({
//       path: '/waterfee/meter/intelligent/detail',
//       query: { id: meterId },
//     });
//   } catch (error) {
//     console.error('Error navigating to edit page:', error);
//     message.error('跳转编辑页面失败');
//   }
// }

// 远程阀门控制
async function handleValveControl(record, action) {
  try {
    const safeRecord = preserveBigInt(record);
    const meterId = ensureMeterIdString(safeRecord.meterId);

    await controlValve(meterId, action);
    message.success(`${action === 'open' ? '开阀' : '关阀'}操作成功`);

    // 刷新实时数据
    const realtime = await getIntelligentMeterRealtime(meterId);
    // 这里可以更新表格中的阀门状态等数据
    await tableApi.query();
  } catch (error) {
    console.error(`${action === 'open' ? '开阀' : '关阀'}操作失败:`, error);
    message.error(`${action === 'open' ? '开阀' : '关阀'}失败`);
  }
}

// 导出数据
async function handleExport() {
  try {
    const formValues = await tableApi.getFormValues();

    // 创建额外的表头映射
    const dictHeaders = [
      { label: '厂家翻译', prop: 'manufacturerName' },
      { label: '口径翻译', prop: 'caliberName' },
      { label: '精度翻译', prop: 'accuracyName' },
    ];

    await commonDownloadExcel(exportIntelligentMeter, '智能水表数据', {
      ...formValues,
      headers: dictHeaders,
    });

    message.success('导出成功');
  } catch (error) {
    console.error('导出智能水表数据失败:', error);
    message.error(`导出失败：${error.message || '未知错误'}`);
  }
}

// 新增水表
function handleAdd() {
  router.push('/waterfee/meter/intelligent/detail');
}

const schemas = querySchema();

// 获取字典数据
const initDicts = async () => {
  try {
    const [manufacturerDict, caliberDict, accuracyDict, communicationDict] =
      await Promise.all([
        getDictOptions('meter_factory'),
        getDictOptions('dnmm'),
        getDictOptions('water_meter_accuracy'),
        getDictOptions('waterfee_communication_mode'),
      ]);

    for (const schema of schemas) {
      switch (schema.field) {
        case 'accuracy': {
          schema.componentProps.options = accuracyDict;
          break;
        }
        case 'caliber': {
          schema.componentProps.options = caliberDict;
          break;
        }
        case 'communicationMode': {
          schema.componentProps.options = communicationDict;
          break;
        }
        case 'manufacturer': {
          schema.componentProps.options = manufacturerDict;
          break;
        }
      }
    }
  } catch (error) {
    console.error('获取字典数据失败:', error);
  }
};

// 格式化信号强度状态
function getSignalStatus(strength) {
  if (strength >= 20) return { text: '良好', type: 'success' };
  if (strength >= 15) return { text: '一般', type: 'warning' };
  return { text: '差', type: 'error' };
}

// 格式化电池状态
function getBatteryStatus(voltage) {
  if (voltage >= 3.6) return { text: '正常', type: 'success' };
  if (voltage >= 3.3) return { text: '偏低', type: 'warning' };
  return { text: '低电量', type: 'error' };
}

// 组件挂载时初始化
onMounted(async () => {
  await initDicts();
  tableApi.query();
});
</script>

<template>
  <Page :auto-content-height="true">
    <BasicTable @reload="tableApi.query()" table-title="智能水表管理">
      <template #toolbar-tools>
        <Space>
          <Button type="primary" @click="handleExport">
            <template #icon><DownloadOutlined /></template>
            导出
          </Button>
        </Space>
      </template>
      <template #action="{ row }">
        <Space>
          <Button type="link" @click="handleDetail(row)">详情</Button>
          <Button
            v-if="row.valveControl === 1"
            type="link"
            @click="handleValveControl(row, 'open')"
          >
            开阀
          </Button>
          <Button
            v-if="row.valveControl === 1"
            type="link"
            danger
            @click="handleValveControl(row, 'close')"
          >
            关阀
          </Button>
        </Space>
      </template>
      <!-- 自定义信号强度显示 -->
      <template #signalStrength="{ row }">
        <span v-if="row.signalStrength !== undefined">
          <Tag :color="getSignalStatus(row.signalStrength).type">
            {{ row.signalStrength }} ({{ getSignalStatus(row.signalStrength).text }})
          </Tag>
        </span>
        <span v-else>- -</span>
      </template>

      <!-- 自定义电池电压显示 -->
      <template #batteryVoltage="{ row }">
        <span v-if="row.batteryVoltage !== undefined">
          <Tag :color="getBatteryStatus(row.batteryVoltage).type">
            {{ row.batteryVoltage }}V ({{ getBatteryStatus(row.batteryVoltage).text }})
          </Tag>
        </span>
        <span v-else>- -</span>
      </template>
    </BasicTable>
    <!-- 不再需要抽屉组件 -->
  </Page>
</template>

<style scoped>
/* 增强表格自适应能力 */
:deep(.vben-vxe-grid-toolbar) {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

:deep(.vben-vxe-grid-toolbar-left) {
  flex: 1;
}

/* 确保表格容器能够撑满可用宽度 */
:deep(.vxe-table--main-wrapper),
:deep(.vxe-grid--main-wrapper) {
  width: 100% !important;
}

/* 表格内容区自适应 */
:deep(.vxe-table--body-wrapper) {
  width: 100% !important;
}

/* 表格头部自适应 */
:deep(.vxe-table--header-wrapper) {
  width: 100% !important;
}
</style>





