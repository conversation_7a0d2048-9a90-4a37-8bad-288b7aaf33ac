<script setup lang="ts">
import type { SelectProps, TableColumnType } from 'ant-design-vue';
import type { Key } from 'ant-design-vue/lib/table/interface';

import { onMounted, reactive, ref } from 'vue'; // Import toRaw

import {
  DeleteOutlined,
  EditOutlined,
  PlusOutlined,
  ReloadOutlined,
  SearchOutlined,
} from '@ant-design/icons-vue';
import {
  Button as AButton,
  Card as ACard,
  Form as AForm,
  FormItem as AFormItem,
  Input as AInput,
  Popconfirm as APopconfirm,
  Select as ASelect,
  Space as ASpace,
  Table as ATable,
} from 'ant-design-vue';

// 假设 API 路径和函数名称
import {
  addLadderPrice,
  deleteLadderPrice,
  listLadderPrice,
  updateLadderPrice,
} from '#/api/waterfee/price/ladderPrice';
import { getDictOptions } from '#/utils/dict';
import { renderDict } from '#/utils/render';

import LadderPriceDrawer from './LadderPriceDrawer.vue';
// 确保实际文件名大小写完全匹配（包括L的大小写）

// 搜索参数
const searchParams = reactive({
  name: '', // 价格名称
  waterUseType: undefined, // 用水性质
});

// 搜索表单下拉选项
const searchWaterUsageTypeOptions = ref<SelectProps['options']>([]);

// 表格数据行接口定义
interface TableDataType {
  id: string;
  waterUseType: string; // 必需字段改为可选
  name?: string;
  calculationMethod?: string;
  isPopulation?: number;
  populationCount?: number;
  ladder1?: string; // Formatted string
  ladder2?: string; // Formatted string
  ladder3?: string; // Formatted string
  description?: string;
  priceTiers?: any[]; // Original tiers for editing
}

// 抽屉表单数据类型
type FormDataType = Omit<
  TableDataType,
  'id' | 'ladder1' | 'ladder2' | 'ladder3'
> & { id?: number | string };

// 表格列定义
const columns: TableColumnType<TableDataType>[] = [
  { title: '序号', dataIndex: 'id', key: 'id', width: 60, align: 'center' },
  {
    title: '用水性质',
    dataIndex: 'waterUseType',
    key: 'waterUseType',
    align: 'center',
    customRender: ({ text }) => renderDict(text, 'water_use_type'),
  },
  { title: '价格名称', dataIndex: 'name', key: 'name', align: 'center' },
  {
    title: '计算方式',
    dataIndex: 'calculationMethod',
    key: 'calculationMethod',
    align: 'center',
    customRender: ({ text }) => renderDict(text, 'calculation_method'),
  },
  {
    title: '是否人口',
    dataIndex: 'isPopulation',
    key: 'isPopulation',
    align: 'center',
    customRender: ({ text }) => renderDict(text, 'sys_on_off'),
  },
  {
    title: '人口数量',
    dataIndex: 'populationCount',
    key: 'populationCount',
    align: 'center',
  },
  {
    title: '阶梯1',
    dataIndex: 'ladder1',
    key: 'ladder1',
    align: 'center',
  },
  {
    title: '阶梯2',
    dataIndex: 'ladder2',
    key: 'ladder2',
    align: 'center',
  },
  {
    title: '阶梯3',
    dataIndex: 'ladder3',
    key: 'ladder3',
    align: 'center',
  },
  {
    title: '备注',
    dataIndex: 'description',
    key: 'description',
    align: 'center',
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
    align: 'center',
    fixed: 'right',
  },
];

// 表格数据
const tableData = ref<TableDataType[]>([]);

// 选中行
const selectedRowKeys = ref<Key[]>([]);
const onSelectChange = (keys: Key[]) => {
  selectedRowKeys.value = keys;
};

const getList = async () => {
  try {
    const response = await listLadderPrice({ ...searchParams });

    tableData.value = response.rows.map((item) => ({
      ...item,
      ladder1: formatLadderDisplay(item.priceTiers?.[0]),
      ladder2: formatLadderDisplay(item.priceTiers?.[1]),
      ladder3: formatLadderDisplay(item.priceTiers?.[2]),
    }));
    console.log('Extracted tableData:', tableData.value);
  } catch (error) {
    console.error('Failed to fetch or process ladder price list:', error);
    tableData.value = [];
  }
};

// 格式化阶梯显示函数
const formatLadderDisplay = (tierDetail: any) => {
  if (!tierDetail) return '-';
  const end =
    tierDetail.endQuantity === null || tierDetail.endQuantity === undefined
      ? '以上'
      : tierDetail.endQuantity;
  return `${tierDetail.startQuantity}~${end} (¥ ${tierDetail.price})`;
};

// 组件挂载时获取数据
onMounted(async () => {
  searchWaterUsageTypeOptions.value = await getDictOptions('water_use_type');
  await getList();
});

// 搜索
const handleSearch = () => {
  getList();
};

// 重置
const handleReset = () => {
  searchParams.name = '';
  searchParams.waterUseType = undefined;
  getList();
};

// --- 抽屉状态管理 ---
const drawerVisible = ref(false);
const isEditing = ref(false);
// 使用类型断言解决类型不兼容问题
const currentRecord = ref<any | null>(null);

// 添加
const handleAdd = () => {
  isEditing.value = false;
  currentRecord.value = null;
  drawerVisible.value = true;
};

// 编辑
const handleEdit = (record: TableDataType) => {
  isEditing.value = true;
  currentRecord.value = { ...record };
  drawerVisible.value = true;
};

// 保存 (从抽屉接收)
const handleDrawerSave = async (data: FormDataType) => {
  console.log('Received save event from ladder price drawer:', data);
  try {
    await (isEditing.value && currentRecord.value
      ? updateLadderPrice({ ...data, id: currentRecord.value.id })
      : addLadderPrice({ ...data, id: data.id?.toString() }));
    drawerVisible.value = false;
    await getList();
  } catch (error) {
    console.error('Save ladder price failed:', error);
  }
};

// 取消 (从抽屉接收)
const handleDrawerCancel = () => {
  // Drawer component handles closing
};

// 删除
const handleDelete = async (record: TableDataType) => {
  console.log('Delete ladder price record:', record);
  try {
    // 修正参数匹配API接口定义
    await deleteLadderPrice([record.id]);
    await getList();
  } catch (error) {
    console.error('Delete ladder price failed:', error);
  }
};
</script>

<template>
  <div class="p-4">
    <ACard title="阶梯价格配置">
      <template #extra>
        <AButton type="primary" @click="handleAdd">
          <PlusOutlined /> 添加
        </AButton>
      </template>
      <AForm layout="inline" class="mb-4">
        <AFormItem label="价格名称">
          <AInput
            v-model:value="searchParams.name"
            placeholder="请输入价格名称"
          />
        </AFormItem>
        <AFormItem label="用水性质">
          <ASelect
            v-model:value="searchParams.waterUseType"
            placeholder="请选择用水性质"
            style="width: 150px"
            :options="searchWaterUsageTypeOptions"
            allow-clear
          />
        </AFormItem>
        <AFormItem>
          <ASpace>
            <AButton type="primary" @click="handleSearch">
              <SearchOutlined /> 查询
            </AButton>
            <AButton @click="handleReset"> <ReloadOutlined /> 重置 </AButton>
          </ASpace>
        </AFormItem>
      </AForm>

      <ATable
        :columns="columns"
        :data-source="tableData"
        :row-selection="{
          selectedRowKeys,
          onChange: onSelectChange,
        }"
        row-key="id"
        bordered
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <ASpace>
              <AButton type="link" @click="handleEdit(record as TableDataType)">
                <EditOutlined /> 编辑
              </AButton>
              <APopconfirm
                title="确定删除吗?"
                ok-text="确定"
                cancel-text="取消"
                @confirm="handleDelete(record as TableDataType)"
              >
                <AButton type="link" danger> <DeleteOutlined /> 删除 </AButton>
              </APopconfirm>
            </ASpace>
          </template>
        </template>
      </ATable>
    </ACard>

    <LadderPriceDrawer
      v-model:visible="drawerVisible"
      :is-editing="isEditing"
      :record="currentRecord"
      @save="handleDrawerSave"
      @cancel="handleDrawerCancel"
    />
  </div>
</template>

<style scoped>
/* Add any specific styles if needed */
</style>
