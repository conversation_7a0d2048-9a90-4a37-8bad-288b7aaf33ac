<script setup lang="ts">
import type { LeaveVO } from './api/model';

import type { StartWorkFlowReqData } from '#/api/workflow/task/model';

import { computed, onMounted, ref, useTemplateRef } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { useVbenModal } from '@vben/common-ui';

import { Card } from 'ant-design-vue';
import { cloneDeep } from 'lodash-es';

import { useVbenForm } from '#/adapter/form';
// import { leaveAdd, leaveInfo, leaveUpdate } from './api';
import {
  addUser,
  getUserFlowDetail,
  updateUser,
} from '#/api/waterfee/user/archivesManage';
import { startWorkFlow } from '#/api/workflow/task';

import applyModal from './components/apply-modal.vue';
import { flowSchema } from './data';
import LeaveDescription from './leave-description.vue';

const route = useRoute();
const readonly = route.query?.readonly === 'true';
const id = route.query?.id as string;

/**
 * id存在&readonly时候
 */
const showActionBtn = computed(() => {
  return !readonly;
});

const [BasicForm, formApi] = useVbenForm({
  commonConfig: {
    // 默认占满两列
    formItemClass: 'col-span-1',
    // 默认label宽度 px
    labelWidth: 100,
    // 通用配置项 会影响到所有表单项
    componentProps: {
      class: 'w-full',
      disabled: readonly,
    },
  },
  schema: flowSchema(),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-2 gap-x-4',
});

const leaveDescription = ref<LeaveVO>();
const showDescription = computed(() => {
  return readonly && leaveDescription.value;
});
const cardRef = useTemplateRef('cardRef');
onMounted(async () => {
  // 只读 获取信息赋值
  if (id) {
    const {
      userBasicInfo,
      // userPrice,
      // waterfeeMeterBo
    } = await getUserFlowDetail(id);
    leaveDescription.value = userBasicInfo;
    await formApi.setValues(userBasicInfo);
    // const dateRange = [dayjs(resp.startDate), dayjs(resp.endDate)];
    // await formApi.setFieldValue('dateRange', dateRange);

    /**
     * window.parent（最近的上一级父页面）
     * 主要解决内嵌iframe卡顿的问题
     */
    if (readonly) {
      // 渲染完毕才显示表单
      window.parent.postMessage({ type: 'mounted' }, '*');
      // 获取表单高度 内嵌时保持一致
      setTimeout(() => {
        const el = cardRef.value?.$el as HTMLDivElement;
        // 获取高度
        const height = el?.offsetHeight ?? 0;
        if (height) {
          window.parent.postMessage({ type: 'height', height }, '*');
        }
      });
    }
  }
});

const router = useRouter();

/**
 * 提取通用逻辑
 */
async function handleSaveOrUpdate() {
  const { valid } = await formApi.validate();
  if (!valid) {
    return;
  }
  const data = cloneDeep(await formApi.getValues()) as any;

  if (id) {
    data.id = id;
    return await updateUser(data);
  } else {
    return await addUser(data);
  }
}

const [ApplyModal, applyModalApi] = useVbenModal({
  connectedComponent: applyModal,
});
/**
 * 暂存 草稿状态
 */
async function handleTempSave() {
  try {
    await handleSaveOrUpdate();
    router.push('/demo/leave');
  } catch (error) {
    console.error(error);
  }
}

/**
 * 保存业务 & 发起流程
 */
async function handleStartWorkFlow() {
  try {
    // 保存业务
    const leaveResp = await handleSaveOrUpdate();
    // 启动流程
    // const taskVariables = {
    //   leaveDays: leaveResp?.leaveDays || 0,
    //   userList: ['1', '3', '4'],
    // };

    const startWorkFlowData: StartWorkFlowReqData = {
      businessId: leaveResp.userId,
      flowCode: 'newWaterfeeUser',
      // variables: taskVariables,
      variables: {},
    };
    const { taskId } = await startWorkFlow(startWorkFlowData);
    // 打开窗口
    applyModalApi.setData({
      taskId,
      // taskVariables,
      variables: {},
    });
    applyModalApi.open();
  } catch (error) {
    console.error(error);
  }
}

function handleComplete() {
  formApi.resetForm();
  router.push('/register/userRegister');
}

/**
 * 显示详情时 需要较小的padding
 */
const cardSize = computed(() => {
  return showDescription.value ? 'small' : 'default';
});
</script>

<template>
  <Card ref="cardRef" :size="cardSize">
    <div id="leave-form">
      <!-- 使用v-if会影响生命周期 -->
      <BasicForm v-show="!showDescription" />
      <LeaveDescription v-if="showDescription" :data="leaveDescription!" />
      <div v-if="showActionBtn" class="flex justify-end gap-2">
        <a-button @click="handleTempSave">暂存</a-button>
        <a-button type="primary" @click="handleStartWorkFlow">提交</a-button>
      </div>
      <ApplyModal @complete="handleComplete" />
    </div>
  </Card>
</template>

<style lang="scss">
html:has(#leave-form) {
  /**
  去除顶部进度条样式
  */
  #nprogress {
    display: none;
  }
}
</style>
