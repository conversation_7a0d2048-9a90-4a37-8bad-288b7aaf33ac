import type { BasicPageParams, BasicFetchResult } from '#/api/types/common';

export interface MeterImportParam {
  meterType: string;
  file: Blob | File;
}

export interface MeterModel {
  meterId?: string;
  meterNo: string;
  meterType: number; // 1-机械表 2-智能表
  manufacturer?: string;
  meterCategory?: string;
  meterClassification?: string;
  measurementPurpose?: string;
  caliber?: string;
  accuracy?: string;
  initialReading?: number;
  installDate?: string;
  businessAreaId?: string;
  businessAreaName?: string;
  meterBookId?: string;
  meterBookName?: string;
  installAddress?: string;
  meterRatio?: number;
  communicationMode?: string;
  valveControl?: number; // 0-无 1-有
  imei?: string;
  imsi?: string;
  iotPlatform?: string;
  prepaid?: string; // 0-否 1-是
  userId?: string;
  userNo?: string;
  waterNature?: string;
  priceName?: string;
  penaltyEnabled?: boolean;
  penaltyType?: string;
  penaltyValue?: number;
  additionalFeesEnabled?: boolean;
  additionalFeesItems?: string;
  createBy?: string;
  createTime?: string;
  updateBy?: string;
  updateTime?: string;
  remark?: string;
}

export type MeterParams = {
  businessAreaId?: string;
  caliber?: string;
  communicationMode?: string;
  installAddressLike?: string;
  installDateEnd?: string;
  installDateRange?: string[]; // 前端日期范围选择器使用
  installDateStart?: string;
  manufacturer?: string;
  meterBookId?: string;
  meterNo?: string;
  meterType?: number;
} & BasicPageParams;

export type MeterListGetResultModel = BasicFetchResult<MeterModel>;


