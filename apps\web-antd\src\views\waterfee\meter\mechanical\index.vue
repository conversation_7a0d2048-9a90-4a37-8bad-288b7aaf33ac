<script setup lang="ts">
import { onMounted } from 'vue';
import { useRouter } from 'vue-router';

import { Page } from '@vben/common-ui';

import { DownloadOutlined } from '@ant-design/icons-vue';
import { Button, message, Space } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { ensureMeterIdString } from '#/api/waterfee/meter/index';
import {
  exportMechanicalMeter,
  getMechanicalMeterList,
  getMechanicalMeterStatistics,
} from '#/api/waterfee/meter/mechanical';
import { getDictOptions } from '#/utils/dict';
import { commonDownloadExcel } from '#/utils/file/download';

import { columns, querySchema } from './mechanical.data';

const router = useRouter();
// const dictStore = useDictStore();

const formOptions = {
  commonConfig: {
    labelWidth: 80,
    componentProps: {
      allowClear: true,
    },
  },
  schema: querySchema(),
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
};

const gridOptions = {
  columns,
  height: 'auto',
  width: '100%',
  fit: true,
  autoResize: true,
  tableLayout: 'auto',
  scrollX: {
    enabled: true,
    gt: 10,
  },
  resizable: true,
  resizeConfig: {
    minWidth: 80,
  },
  tooltipConfig: {
    showAll: true,
  },
  proxyConfig: {
    ajax: {
      query: async (
        { page }: { page?: { currentPage: number; pageSize: number } },
        formValues: Record<string, any> = {},
      ) => {
        const resp = await getMechanicalMeterList({
          pageNum: page?.currentPage,
          pageSize: page?.pageSize,
          ...formValues,
        });
        return resp;
      },
    },
  },
  id: 'waterfee-meter-mechanical-index',
};

const [BasicTable, tableApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

// 查看详情
function handleDetail(row: Record<string, any>) {
  const meterId = ensureMeterIdString(row.meterId);
  router.push(`/meterInfo/mechanicalDetail?id=${meterId}}`);
}

// 导出数据
async function handleExport() {
  try {
    const formValues = await tableApi.getFormValues();

    // 创建额外的表头映射
    const dictHeaders = [
      { label: '厂家翻译', prop: 'manufacturerName' },
      { label: '口径翻译', prop: 'caliberName' },
      { label: '精度翻译', prop: 'accuracyName' },
    ];

    await commonDownloadExcel(exportMechanicalMeter, '机械水表数据', {
      ...formValues,
      headers: dictHeaders,
    });

    message.success('导出成功');
  } catch (error) {
    console.error('导出机械水表数据失败:', error);
    message.error(`导出失败：${error.message || '未知错误'}`);
  }
}

const schemas = querySchema();

// 获取字典数据
const initDicts = async () => {
  try {
    const [manufacturerDict, caliberDict, accuracyDict] = await Promise.all([
      getDictOptions('meter_factory'),
      getDictOptions('dnmm'),
      getDictOptions('water_meter_accuracy'),
    ]);

    for (const schema of schemas) {
      switch (schema.field) {
        case 'accuracy': {
          schema.componentProps.options = accuracyDict;
          break;
        }
        case 'caliber': {
          schema.componentProps.options = caliberDict;
          break;
        }
        case 'manufacturer': {
          schema.componentProps.options = manufacturerDict;
          break;
        }
      }
    }
  } catch (error) {
    console.error('获取字典数据失败:', error);
  }
};

// 获取统计数据
async function getStatistics() {
  try {
    const formValues = await tableApi.getFormValues();
    const statistics = await getMechanicalMeterStatistics(formValues);
    console.log('机械水表统计数据:', statistics);
    // 这里可以添加统计数据的展示逻辑
  } catch (error) {
    console.error('获取统计数据失败:', error);
  }
}

onMounted(() => {
  initDicts();
});
</script>

<template>
  <Page :auto-content-height="true">
    <BasicTable @reload="tableApi.query()" table-title="机械水表管理">
      <template #toolbar-tools>
        <Space>
          <Button type="primary" @click="handleExport">
            <template #icon><DownloadOutlined /></template>
            导出
          </Button>
          <Button type="primary" @click="getStatistics"> 统计 </Button>
        </Space>
      </template>
      <template #action="{ row }">
        <Space>
          <Button type="link" @click="handleDetail(row)">详情</Button>
          <!-- <Button type="link" @click="handleReading(row)">抄表</Button>
          <Button type="link" @click="handleEdit(row)">编辑</Button>
          <Button type="link" danger @click="handleDelete(row)">删除</Button> -->
        </Space>
      </template>
    </BasicTable>
  </Page>
</template>

<style scoped>
/* 可以添加自定义样式 */
</style>
