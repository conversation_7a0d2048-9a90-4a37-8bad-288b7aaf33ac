export interface LiquidatedDamagesConfigsVO {
  /**
   * 
   */
  id: string | number;

  /**
   * 违约金名称
   */
  name: string;

  /**
   * 计算方式 (e.g., daily_rate, fixed_amount)
   */
  calculationMethod: string;

  /**
   * 固定金额 (if calculation_method is fixed_amount)
   */
  fixedAmount: number;

  /**
   * 利率(%), null if not applicable
   */
  interestRatePercent: number;

  /**
   * 收取方式 (e.g., term, next_month)
   */
  collectionMethod: string;

  /**
   * 开始日期 (if applicable)
   */
  startDate: string;

  /**
   * 是否大于本金 (True/False)
   */
  canExceedPrincipal: number;

  /**
   * 免除违约金启用状态
   */
  waiverEnabled: number;

  /**
   * 免除截止时间 (if waiver_enabled is true)
   */
  waiverTime: string;

  /**
   * 备注
   */
  remarks: string;

}

export interface LiquidatedDamagesConfigsForm extends BaseEntity {
  /**
   * 
   */
  id?: string | number;

  /**
   * 违约金名称
   */
  name?: string;

  /**
   * 计算方式 (e.g., daily_rate, fixed_amount)
   */
  calculationMethod?: string;

  /**
   * 固定金额 (if calculation_method is fixed_amount)
   */
  fixedAmount?: number;

  /**
   * 利率(%), null if not applicable
   */
  interestRatePercent?: number;

  /**
   * 收取方式 (e.g., term, next_month)
   */
  collectionMethod?: string;

  /**
   * 开始日期 (if applicable)
   */
  startDate?: string;

  /**
   * 是否大于本金 (True/False)
   */
  canExceedPrincipal?: number;

  /**
   * 免除违约金启用状态
   */
  waiverEnabled?: number;

  /**
   * 免除截止时间 (if waiver_enabled is true)
   */
  waiverTime?: string;

  /**
   * 备注
   */
  remarks?: string;

}

export interface LiquidatedDamagesConfigsQuery extends PageQuery {

  /**
   * 违约金名称
   */
  name?: string;

  /**
   * 计算方式 (e.g., daily_rate, fixed_amount)
   */
  calculationMethod?: string;

  /**
   * 固定金额 (if calculation_method is fixed_amount)
   */
  fixedAmount?: number;

  /**
   * 利率(%), null if not applicable
   */
  interestRatePercent?: number;

  /**
   * 收取方式 (e.g., term, next_month)
   */
  collectionMethod?: string;

  /**
   * 开始日期 (if applicable)
   */
  startDate?: string;

  /**
   * 是否大于本金 (True/False)
   */
  canExceedPrincipal?: number;

  /**
   * 免除违约金启用状态
   */
  waiverEnabled?: number;

  /**
   * 免除截止时间 (if waiver_enabled is true)
   */
  waiverTime?: string;

  /**
   * 备注
   */
  remarks?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



