<template>
  <div>
    <div class="counter-payment-container">
      <!-- 搜索区域 -->
      <Card title="用户查询" :bordered="false" class="mb-4">
        <div class="search-container">
          <div class="search-input-wrapper">
            <AutoComplete
              v-model:value="searchForm.keyword"
              :options="searchOptions"
              :filterOption="false"
              placeholder="请输入用户编号或姓名"
              style="width: 100%"
              @search="handleSearchInput"
              @select="handleSelectUser"
            >
              <template #option="{ value, label, data }">
                <div class="search-option">
                  <div class="search-option-title">{{ label }}</div>
                  <div class="search-option-desc">{{ data.desc }}</div>
                </div>
              </template>
            </AutoComplete>
          </div>
          <div class="search-buttons">
            <Button type="primary" @click="handleSearch">
              <template #icon><SearchOutlined /></template>
              查询
            </Button>
            <Button class="ml-2" @click="resetSearch">
              <template #icon><ReloadOutlined /></template>
              重置
            </Button>
          </div>
        </div>
      </Card>

      <!-- 用户信息展示区域 -->
      <Card v-if="userInfo.userNo" title="用户信息" :bordered="false" class="mb-4">
        <Descriptions :column="{ xxl: 4, xl: 3, lg: 3, md: 2, sm: 1, xs: 1 }">
          <DescriptionsItem label="用户编号">{{ userInfo.userNo }}</DescriptionsItem>
          <DescriptionsItem label="用户姓名">{{ userInfo.userName }}</DescriptionsItem>
          <DescriptionsItem label="联系电话">{{ userInfo.phoneNumber }}</DescriptionsItem>
          <DescriptionsItem label="用水地址">{{ userInfo.address }}</DescriptionsItem>
          <DescriptionsItem label="用户性质">{{ userInfo.customerNature }}</DescriptionsItem>
          <DescriptionsItem label="用水性质">{{ userInfo.useWaterNature }}</DescriptionsItem>
          <DescriptionsItem label="用水人数">{{ userInfo.useWaterNumber }}</DescriptionsItem>
          <DescriptionsItem label="供水日期">{{ userInfo.supplyDate }}</DescriptionsItem>
        </Descriptions>
      </Card>

      <!-- 标签页区域 -->
      <Card :bordered="false">
        <Tabs v-model:activeKey="activeTabKey" @change="handleTabChange">
          <!-- 窗口收费 -->
          <TabPane key="payment" tab="窗口收费">
            <div v-if="!userInfo.userNo" class="empty-tip">
              请先查询用户信息
            </div>
            <template v-else>
              <div class="bill-table-container">
                <div class="table-header">
                  <div class="table-title">待缴费账单</div>
                  <div class="table-actions">
                    <Button type="primary" :disabled="!selectedBills.length" @click="handlePayBills">
                      <template #icon><DollarOutlined /></template>
                      缴费
                    </Button>
                  </div>
                </div>
                <Table
                  ref="billTableRef"
                  :dataSource="billList"
                  :columns="billColumns"
                  :rowKey="record => record.billId"
                  :rowSelection="{ onChange: handleBillSelectionChange }"
                  :scroll="{ y: 300 }"
                  :pagination="false"
                >
                  <template #emptyText>
                    <div class="empty-data">暂无待缴费账单</div>
                  </template>
                </Table>
              </div>

              <div class="payment-summary" v-if="selectedBills.length">
                <div class="summary-item">
                  <span class="label">已选账单数:</span>
                  <span class="value">{{ selectedBills.length }}</span>
                </div>
                <div class="summary-item">
                  <span class="label">应缴总金额:</span>
                  <span class="value amount">{{ totalPaymentAmount }} 元</span>
                </div>
              </div>
            </template>
          </TabPane>

          <!-- 缴费明细 -->
          <TabPane key="paymentDetail" tab="缴费明细">
            <div v-if="!userInfo.userNo" class="empty-tip">
              请先查询用户信息
            </div>
            <template v-else>
              <Table
                ref="paymentDetailTableRef"
                :dataSource="paymentDetailList"
                :columns="paymentDetailColumns"
                :rowKey="record => record.paymentDetailId || record.id"
                :scroll="{ y: 300 }"
                :pagination="false"
              >
                <template #emptyText>
                  <div class="empty-data">暂无缴费记录</div>
                </template>
              </Table>
            </template>
          </TabPane>

          <!-- 账单信息 -->
          <TabPane key="billInfo" tab="账单信息">
            <div v-if="!userInfo.userNo" class="empty-tip">
              请先查询用户信息
            </div>
            <template v-else>
              <div class="bill-filter-container">
                <div class="filter-item">
                  <span class="filter-label">账单月份：</span>
                  <DatePicker
                    v-model:value="billMonthFilter"
                    picker="month"
                    format="YYYY-MM"
                    valueFormat="YYYY-MM"
                    placeholder="选择月份"
                    style="width: 200px"
                    @change="handleBillMonthChange"
                    allowClear
                  />
                </div>
              </div>
              <Table
                ref="allBillTableRef"
                :dataSource="allBillList"
                :columns="billColumns"
                :rowKey="record => record.billId"
                :scroll="{ y: 300 }"
                :pagination="false"
              >
                <template #emptyText>
                  <div class="empty-data">暂无账单信息</div>
                </template>
              </Table>
            </template>
          </TabPane>

          <!-- 抄表计费 -->
          <!--<TabPane key="meterReading" tab="抄表计费">
            <div v-if="!userInfo.userNo" class="empty-tip">
              请先查询用户信息
            </div>
            <template v-else>
              <Table
                ref="meterReadingTableRef"
                :dataSource="meterReadingList"
                :columns="meterReadingColumns"
                :scroll="{ y: 300 }"
                :pagination="false"
              >
                <template #emptyText>
                  <div class="empty-data">暂无抄表记录</div>
                </template>
              </Table>
            </template>
          </TabPane> -->

          <!-- 预存充值 -->
          <TabPane key="deposit" tab="预存充值">
            <div v-if="!userInfo.userNo" class="empty-tip">
              请先查询用户信息
            </div>
            <template v-else>
              <div class="deposit-container">
                <div class="deposit-info">
                  <div class="info-item">
                    <span class="label">当前预存余额:</span>
                    <span class="value amount">{{ depositBalance || 0 }} 元</span>
                  </div>
                </div>
                <div class="deposit-form">
                  <Form layout="vertical" :model="depositForm">
                    <FormItem label="充值金额" required>
                      <InputNumber
                        v-model:value="depositForm.amount"
                        :min="0"
                        :precision="2"
                        style="width: 200px"
                        addon-after="元"
                      />
                    </FormItem>
                    <FormItem label="支付方式" required>
                      <Select
                        v-model:value="depositForm.paymentMethod"
                        style="width: 200px"
                        :options="paymentMethodOptions.filter(item => item.value !== 'DEPOSIT')"
                      />
                    </FormItem>
                    <FormItem label="备注">
                      <Input
                        v-model:value="depositForm.remark"
                        placeholder="请输入备注信息"
                        allow-clear
                      />
                    </FormItem>
                    <FormItem>
                      <Button type="primary" :disabled="!depositForm.amount" @click="handleDeposit">
                        <template #icon><PlusOutlined /></template>
                        充值
                      </Button>
                    </FormItem>
                  </Form>
                </div>
              </div>
            </template>
          </TabPane>

          <!-- 账单调整 -->
          <TabPane key="adjustment" tab="账单调整">
            <div v-if="!userInfo.userId" class="empty-tip">
              请先查询用户信息
            </div>
            <template v-else>
              <div class="adjustment-container">
                <div class="adjustment-form">
                  <Form layout="vertical" :model="adjustmentForm">
                    <FormItem label="选择账单" required>
                      <Select
                        v-model:value="adjustmentForm.billId"
                        style="width: 100%"
                        placeholder="请选择需要调整的账单"
                        :options="adjustableBillOptions"
                        @change="handleAdjustmentBillChange"
                      />
                    </FormItem>

                    <div v-if="adjustmentForm.billId">
                      <!-- 账单信息展示 -->
                      <div class="bill-info-card">
                        <Descriptions title="账单信息" bordered size="small" :column="2">
                          <DescriptionsItem label="账单编号">{{ allBillList.value && allBillList.value.length > 0 ? (allBillList.value.find(bill => bill.billId === adjustmentForm.billId)?.billNumber || '-') : '-' }}</DescriptionsItem>
                          <DescriptionsItem label="账单状态">{{ allBillList.value && allBillList.value.length > 0 ? (allBillList.value.find(bill => bill.billId === adjustmentForm.billId)?.billStatus || '-') : '-' }}</DescriptionsItem>
                          <DescriptionsItem label="账单总额">{{ adjustmentForm.totalAmount }} 元</DescriptionsItem>
                          <DescriptionsItem label="调整后总额">{{ adjustmentForm.adjustedTotalAmount }} 元</DescriptionsItem>
                        </Descriptions>
                      </div>

                      <!-- 读数和用量调整 -->
                      <div class="adjustment-section">
                        <h3>读数和用量调整</h3>
                        <FormItem label="上期读数">
                          <InputNumber
                            :value="adjustmentForm.previousReadingValue"
                            disabled
                            style="width: 200px"
                            addon-after="m³"
                          />
                        </FormItem>
                        <FormItem label="当前读数">
                          <InputNumber
                            :value="adjustmentForm.currentReadingValue"
                            disabled
                            style="width: 200px"
                            addon-after="m³"
                          />
                        </FormItem>
                        <FormItem label="调整后读数" required>
                          <InputNumber
                            v-model:value="adjustmentForm.adjustedCurrentReadingValue"
                            :min="adjustmentForm.previousReadingValue"
                            :precision="2"
                            style="width: 200px"
                            addon-after="m³"
                            @change="handleReadingChange"
                          />
                        </FormItem>
                        <FormItem label="当前用水量">
                          <InputNumber
                            :value="adjustmentForm.currentConsumption"
                            disabled
                            style="width: 200px"
                            addon-after="m³"
                          />
                        </FormItem>
                        <FormItem label="调整后用水量">
                          <InputNumber
                            :value="adjustmentForm.adjustedConsumption"
                            disabled
                            style="width: 200px"
                            addon-after="m³"
                          />
                        </FormItem>
                        <FormItem label="调整后基础费用">
                          <InputNumber
                            :value="adjustmentForm.adjustedBaseChargeAmount"
                            disabled
                            style="width: 200px"
                            addon-after="元"
                          />
                        </FormItem>
                      </div>

                      <!-- 附加费和违约金调整 -->
                      <div class="adjustment-section">
                        <h3>附加费和违约金调整</h3>
                        <FormItem label="当前附加费">
                          <InputNumber
                            :value="adjustmentForm.additionalChargeAmount"
                            disabled
                            style="width: 200px"
                            addon-after="元"
                          />
                        </FormItem>
                        <FormItem label="调整后附加费">
                          <InputNumber
                            v-model:value="adjustmentForm.adjustedAdditionalChargeAmount"
                            :min="0"
                            :precision="2"
                            style="width: 200px"
                            addon-after="元"
                            @change="handleAdditionalChargeChange"
                          />
                        </FormItem>
                        <FormItem label="当前违约金">
                          <InputNumber
                            :value="adjustmentForm.surchargeAmount"
                            disabled
                            style="width: 200px"
                            addon-after="元"
                          />
                        </FormItem>
                        <FormItem label="调整后违约金">
                          <InputNumber
                            v-model:value="adjustmentForm.adjustedSurchargeAmount"
                            :min="0"
                            :precision="2"
                            style="width: 200px"
                            addon-after="元"
                            @change="handleSurchargeChange"
                          />
                        </FormItem>
                      </div>

                      <!-- 阶梯水价调整结果 -->
                      <div v-if="adjustmentForm.priceType === 'LADDER'" class="adjustment-section">
                        <h3>阶梯水价调整结果</h3>
                        <div class="ladder-price-detail">
                          <!-- 阶梯1 -->
                          <div class="ladder-tier">
                            <div class="ladder-tier-header">阶梯1</div>
                            <div class="ladder-tier-content">
                              <div class="ladder-tier-item">
                                <span class="label">原用量:</span>
                                <span class="value">{{ adjustmentForm.tier1 }} m³</span>
                              </div>
                              <div class="ladder-tier-item">
                                <span class="label">原金额:</span>
                                <span class="value">{{ adjustmentForm.tier1Amount }} 元</span>
                              </div>
                              <div class="ladder-tier-item">
                                <span class="label">调整后用量:</span>
                                <span class="value">{{ adjustmentForm.adjustedTier1 }} m³</span>
                              </div>
                              <div class="ladder-tier-item">
                                <span class="label">调整后金额:</span>
                                <span class="value">{{ adjustmentForm.adjustedTier1Amount }} 元</span>
                              </div>
                            </div>
                          </div>

                          <!-- 阶梯2 -->
                          <div v-if="adjustmentForm.tier2 > 0 || adjustmentForm.adjustedTier2 > 0" class="ladder-tier">
                            <div class="ladder-tier-header">阶梯2</div>
                            <div class="ladder-tier-content">
                              <div class="ladder-tier-item">
                                <span class="label">原用量:</span>
                                <span class="value">{{ adjustmentForm.tier2 }} m³</span>
                              </div>
                              <div class="ladder-tier-item">
                                <span class="label">原金额:</span>
                                <span class="value">{{ adjustmentForm.tier2Amount }} 元</span>
                              </div>
                              <div class="ladder-tier-item">
                                <span class="label">调整后用量:</span>
                                <span class="value">{{ adjustmentForm.adjustedTier2 }} m³</span>
                              </div>
                              <div class="ladder-tier-item">
                                <span class="label">调整后金额:</span>
                                <span class="value">{{ adjustmentForm.adjustedTier2Amount }} 元</span>
                              </div>
                            </div>
                          </div>

                          <!-- 阶梯3 -->
                          <div v-if="adjustmentForm.tier3 > 0 || adjustmentForm.adjustedTier3 > 0" class="ladder-tier">
                            <div class="ladder-tier-header">阶梯3</div>
                            <div class="ladder-tier-content">
                              <div class="ladder-tier-item">
                                <span class="label">原用量:</span>
                                <span class="value">{{ adjustmentForm.tier3 }} m³</span>
                              </div>
                              <div class="ladder-tier-item">
                                <span class="label">原金额:</span>
                                <span class="value">{{ adjustmentForm.tier3Amount }} 元</span>
                              </div>
                              <div class="ladder-tier-item">
                                <span class="label">调整后用量:</span>
                                <span class="value">{{ adjustmentForm.adjustedTier3 }} m³</span>
                              </div>
                              <div class="ladder-tier-item">
                                <span class="label">调整后金额:</span>
                                <span class="value">{{ adjustmentForm.adjustedTier3Amount }} 元</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- 调整原因 -->
                      <FormItem label="调整原因" required>
                        <Input
                          v-model:value="adjustmentForm.reason"
                          placeholder="请输入调整原因"
                          allow-clear
                        />
                      </FormItem>

                      <!-- 提交按钮 -->
                      <FormItem>
                        <Button
                          type="primary"
                          :disabled="!isAdjustmentFormValid"
                          @click="handleAdjustConsumption"
                        >
                          <template #icon><EditOutlined /></template>
                          提交调整
                        </Button>
                      </FormItem>
                    </div>
                  </Form>
                </div>
              </div>
            </template>
          </TabPane>

          <!-- 账单退费 -->
          <TabPane key="refund" tab="账单退费">
            <div v-if="!userInfo.userId" class="empty-tip">
              请先查询用户信息
            </div>
            <template v-else>
              <div class="refund-container">
                <div class="refund-form">
                  <Form layout="vertical" :model="refundForm">
                    <FormItem label="选择账单" required>
                      <Select
                        v-model:value="refundForm.billId"
                        style="width: 100%"
                        placeholder="请选择需要退费的账单"
                        :options="refundableBillOptions"
                        @change="handleRefundBillChange"
                      />
                    </FormItem>
                    <FormItem v-if="refundForm.billId" label="已付金额">
                      <InputNumber
                        :value="refundForm.paidAmount"
                        disabled
                        style="width: 200px"
                        addon-after="元"
                      />
                    </FormItem>
                    <FormItem v-if="refundForm.billId" label="已退费金额">
                      <InputNumber
                        :value="refundForm.refundedAmount"
                        disabled
                        style="width: 200px"
                        addon-after="元"
                      />
                    </FormItem>
                    <FormItem v-if="refundForm.billId" label="可退费金额">
                      <InputNumber
                        :value="refundForm.refundableAmount"
                        disabled
                        style="width: 200px"
                        addon-after="元"
                      />
                    </FormItem>
                    <FormItem v-if="refundForm.billId" label="退费金额" required>
                      <InputNumber
                        v-model:value="refundForm.refundAmount"
                        :min="0"
                        :max="refundForm.refundableAmount"
                        :precision="2"
                        style="width: 200px"
                        addon-after="元"
                      />
                    </FormItem>
                    <FormItem v-if="refundForm.billId" label="退款方式" required>
                      <Select
                        v-model:value="refundForm.refundMethod"
                        style="width: 200px"
                        :options="refundMethodOptions"
                      />
                    </FormItem>
                    <FormItem v-if="refundForm.billId" label="退费原因" required>
                      <Input
                        v-model:value="refundForm.reason"
                        placeholder="请输入退费原因"
                        allow-clear
                      />
                    </FormItem>
                    <FormItem v-if="refundForm.billId">
                      <Button
                        type="primary"
                        :disabled="!isRefundFormValid"
                        @click="handleRefund"
                      >
                        <template #icon><RollbackOutlined /></template>
                        提交退费
                      </Button>
                    </FormItem>
                  </Form>
                </div>
              </div>
            </template>
          </TabPane>
        </Tabs>
      </Card>
    </div>

    <!-- 缴费弹窗 -->
    <Modal
      v-model:visible="paymentModalVisible"
      title="账单缴费"
      :width="600"
      :maskClosable="false"
      @cancel="cancelPayment"
    >
      <div class="payment-modal-content">
        <div class="payment-bills-summary">
          <div class="summary-item">
            <span class="label">账单数量:</span>
            <span class="value">{{ selectedBills.length }}</span>
          </div>
          <div class="summary-item">
            <span class="label">应缴总金额:</span>
            <span class="value amount">{{ totalPaymentAmount }} 元</span>
          </div>
        </div>

        <Form layout="vertical" :model="paymentForm">
          <FormItem label="实缴金额" required>
            <InputNumber
              v-model:value="paymentForm.amount"
              :min="0"
              :precision="2"
              style="width: 100%"
              addon-after="元"
            />
          </FormItem>
          <FormItem label="支付方式" required>
            <Select
              v-model:value="paymentForm.paymentMethod"
              style="width: 100%"
              :options="paymentMethodOptions"
            />
          </FormItem>
          <FormItem label="备注">
            <Input
              v-model:value="paymentForm.remark"
              placeholder="请输入备注信息"
              allow-clear
            />
          </FormItem>
        </Form>
      </div>
      <template #footer>
        <Button @click="cancelPayment">取消</Button>
        <Button
          type="primary"
          :disabled="!isPaymentFormValid"
          @click="confirmPayment"
        >
          确认缴费
        </Button>
      </template>
    </Modal>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, reactive, onMounted, nextTick } from 'vue';
import {
  Card,
  Form,
  FormItem,
  Input,
  Button,
  Tabs,
  TabPane,
  Descriptions,
  DescriptionsItem,
  Modal,
  InputNumber,
  Select,
  message,
  Table,
  DatePicker,
  AutoComplete,
  Radio,
} from 'ant-design-vue';
import {
  SearchOutlined,
  ReloadOutlined,
  DollarOutlined,
  PlusOutlined,
  EditOutlined,
  RollbackOutlined,
} from '@ant-design/icons-vue';
// 使用原生Ant Design Vue组件
import {
  billColumns,
  paymentDetailColumns,
  meterReadingColumns,
  paymentMethodOptions,
  refundMethodOptions,
} from './data';
import {
  getUserByKeyword,
  getUserById,
  searchUsersByKeyword,
  getUserBills,
  getUserPaymentDetails,
  // getUserMeterReadings,
  payBills,
  addDeposit,
  adjustConsumption,
  calculateAdjustment,
  refundBill,
} from '#/api/waterfee/counter-payment';
import { getUserDepositBalance } from '#/api/waterfee/user-deposit';

// 搜索表单
const searchForm = reactive({
  keyword: '',
});

// 搜索选项
const searchOptions = ref<any[]>([]);

// 账单月份过滤
const billMonthFilter = ref<string>();

// 用户信息
const userInfo = ref({
  userId: '',
  userNo: '',
  userName: '',
  phoneNumber: '',
  address: '',
  customerNature: '',
  useWaterNature: '',
  useWaterNumber: 0,
  supplyDate: '',
});

// 预存款余额
const depositBalance = ref(0);

// 账单列表
const billList = ref<any[]>([]);
const allBillList = ref<any[]>([]);
const paymentDetailList = ref<any[]>([]);
const meterReadingList = ref<any[]>([]);

// 选中的账单
const selectedBills = ref<any[]>([]);

// 当前激活的标签页
const activeTabKey = ref('payment');

// 表格引用
const billTableRef = ref(null);
const paymentDetailTableRef = ref(null);
const allBillTableRef = ref(null);
const meterReadingTableRef = ref(null);

// 缴费弹窗
const paymentModalVisible = ref(false);
const paymentForm = reactive({
  amount: 0,
  paymentMethod: 'CASH',
  remark: '',
});

// 预存充值表单
const depositForm = reactive({
  amount: 0,
  paymentMethod: 'CASH',
  remark: '',
});

// 账单调整表单
const adjustmentForm = reactive({
  billId: undefined as any,
  // 用水量相关
  currentConsumption: 0,
  adjustedConsumption: 0,
  // 读数相关
  previousReadingValue: 0,
  currentReadingValue: 0,
  adjustedCurrentReadingValue: 0,
  // 费用相关
  baseChargeAmount: 0,
  adjustedBaseChargeAmount: 0,
  // 附加费相关
  additionalChargeAmount: 0,
  adjustedAdditionalChargeAmount: 0,
  // 违约金相关
  surchargeAmount: 0,
  adjustedSurchargeAmount: 0,
  // 总金额
  totalAmount: 0,
  adjustedTotalAmount: 0,
  // 阶梯水价相关
  priceType: 'STANDARD', // 价格类型：'STANDARD'(标准价格), 'LADDER'(阶梯价格)
  // 阶梯1相关
  tier1: 0,
  tier1Amount: 0,
  adjustedTier1: 0,
  adjustedTier1Amount: 0,
  // 阶梯2相关
  tier2: 0,
  tier2Amount: 0,
  adjustedTier2: 0,
  adjustedTier2Amount: 0,
  // 阶梯3相关
  tier3: 0,
  tier3Amount: 0,
  adjustedTier3: 0,
  adjustedTier3Amount: 0,
  // 调整原因
  reason: '',
  // 调整类型：'consumption'(用量), 'reading'(读数), 'additionalCharge'(附加费), 'surcharge'(违约金)
  adjustmentType: 'consumption',
});

// 退费表单
const refundForm = reactive({
  billId: undefined as any,
  paidAmount: 0,
  refundedAmount: 0,    // 已退费金额
  refundableAmount: 0,  // 可退费金额
  refundAmount: 0,
  reason: '',
  refundMethod: 'CASH', // 退款方式：现金、银行卡、预存款
});

// 计算属性：总缴费金额
const totalPaymentAmount = computed(() => {
  if (!selectedBills.value || selectedBills.value.length === 0) {
    return '0.00';
  }

  let total = 0;
  for (const bill of selectedBills.value) {
    const balanceDue = parseFloat(bill.balanceDue || 0);
    if (!isNaN(balanceDue)) {
      total += balanceDue;
    }
  }

  return total.toFixed(2);
});

// 计算属性：可调整的账单选项
const adjustableBillOptions = computed(() => {
  return allBillList.value
    .filter(bill => bill.billStatus === 'DRAFT' || bill.billStatus === 'ISSUED')
    .map(bill => ({
      label: `${bill.billNumber} (${bill.billingPeriodStart?.substring(0, 10)} ~ ${bill.billingPeriodEnd?.substring(0, 10)})`,
      value: bill.billId,
    }));
});

// 计算属性：可退费的账单选项
const refundableBillOptions = computed(() => {
  // 只显示已支付且状态为PAID的账单
  return allBillList.value
    .filter(bill => bill.billStatus === 'PAID' && bill.amountPaid > 0)
    .map(bill => ({
      label: `${bill.billNumber} (已付: ${bill.amountPaid} 元)`,
      value: bill.billId,
    }));
});

// 计算属性：缴费表单是否有效
const isPaymentFormValid = computed(() => {
  return paymentForm.amount > 0 && paymentForm.paymentMethod;
});

// 计算属性：调整表单是否有效
const isAdjustmentFormValid = computed(() => {
  // 必须有账单ID和调整原因
  if (!adjustmentForm.billId || !adjustmentForm.reason) {
    return false;
  }

  // 读数必须大于等于上期读数
  if (adjustmentForm.adjustedCurrentReadingValue < adjustmentForm.previousReadingValue) {
    return false;
  }

  // 附加费和违约金必须大于等于0
  if (adjustmentForm.adjustedAdditionalChargeAmount < 0 || adjustmentForm.adjustedSurchargeAmount < 0) {
    return false;
  }

  // 至少有一项调整
  const hasReadingChange = adjustmentForm.adjustedCurrentReadingValue !== adjustmentForm.currentReadingValue;
  const hasAdditionalChargeChange = adjustmentForm.adjustedAdditionalChargeAmount !== adjustmentForm.additionalChargeAmount;
  const hasSurchargeChange = adjustmentForm.adjustedSurchargeAmount !== adjustmentForm.surchargeAmount;

  return hasReadingChange || hasAdditionalChargeChange || hasSurchargeChange;
});

// 计算属性：退费表单是否有效
const isRefundFormValid = computed(() => {
  return (
    refundForm.billId &&
    refundForm.refundAmount > 0 &&
    refundForm.refundAmount <= refundForm.refundableAmount &&
    refundForm.reason &&
    refundForm.refundMethod
  );
});

// 处理搜索输入
async function handleSearchInput(value: string) {
  if (!value || value.length < 2) {
    searchOptions.value = [];
    return;
  }

  try {
    // 调用模糊查询接口
    const res = await searchUsersByKeyword(value);
    if (res && res.length > 0) {
      // 转换为AutoComplete需要的格式
      searchOptions.value = res.map(item => ({
        value: item.userId.toString(), // 使用用户ID作为值
        label: `${item.userNo} - ${item.userName}`, // 显示用户编号和姓名
        data: {
          desc: item.phoneNumber || '无手机号码', // 显示手机号码作为描述
          ...item // 保存完整的用户信息
        }
      }));
    } else {
      searchOptions.value = [];
    }
  } catch (error) {
    console.error('模糊查询失败:', error);
    searchOptions.value = [];
  }
}

// 处理选择用户
async function handleSelectUser(value: string) {
  try {
    // 根据用户ID查询详细信息
    const res = await getUserById(value);
    if (res && res.userId) {
      // 重置标签页为第一个（窗口收费）
      activeTabKey.value = 'payment';

      // 清空所有数据
      resetAllTabsData();

      // 设置用户信息
      userInfo.value = res;

      // 只加载窗口收费标签页的数据
      loadPaymentData();
    } else {
      message.warning('未找到用户信息');
      resetUserData();
    }
  } catch (error) {
    console.error('查询用户详情失败:', error);
    message.error('查询用户详情失败，请稍后重试');
    resetUserData();
  }
}

// 搜索用户
async function handleSearch() {
  if (!searchForm.keyword) {
    message.warning('请输入用户编号或姓名');
    return;
  }

  try {
    const res = await getUserByKeyword(searchForm.keyword);
    if (res && res.userId) {
      // 重置标签页为第一个（窗口收费）
      activeTabKey.value = 'payment';

      // 清空所有数据
      resetAllTabsData();

      // 设置用户信息
      userInfo.value = res;

      // 只加载窗口收费标签页的数据
      loadPaymentData();
    } else {
      message.warning('未找到用户信息');
      resetUserData();
    }
  } catch (error) {
    console.error('查询用户失败:', error);
    message.error('查询用户失败，请稍后重试');
    resetUserData();
  }
}

// 重置搜索
function resetSearch() {
  searchForm.keyword = '';
  searchOptions.value = [];
  resetUserData();
}

// 清空所有标签页的数据
function resetAllTabsData() {
  // 清空窗口收费标签页数据
  billList.value = [];
  selectedBills.value = [];

  // 清空缴费明细标签页数据
  paymentDetailList.value = [];

  // 清空账单信息标签页数据
  allBillList.value = [];

  // 清空预存充值标签页数据
  depositBalance.value = 0;

  // 清空用量调整标签页数据
  resetAdjustmentForm();

  // 清空账单退费标签页数据
  refundForm.billId = undefined;
  refundForm.paidAmount = 0;
  refundForm.refundedAmount = 0;
  refundForm.refundableAmount = 0;
  refundForm.refundAmount = 0;
  refundForm.reason = '';
  refundForm.refundMethod = 'CASH';

  // 清空其他数据
  meterReadingList.value = [];
}

// 重置用户数据
function resetUserData() {
  // 重置用户信息
  userInfo.value = {
    userId: '',
    userNo: '',
    userName: '',
    phoneNumber: '',
    address: '',
    customerNature: '',
    useWaterNature: '',
    useWaterNumber: 0,
    supplyDate: '',
  };

  // 重置标签页为第一个（窗口收费）
  activeTabKey.value = 'payment';

  // 清空所有标签页的数据
  resetAllTabsData();
}

// 加载用户相关数据
async function loadUserData(_userId: string) {
  try {
    // 确保用户信息已加载
    if (!userInfo.value.userId) {
      return;
    }

    // 根据当前激活的标签页加载相应的数据
    if (activeTabKey.value === 'payment') {
      // 窗口收费：加载待缴费账单
      loadPaymentData();
    } else if (activeTabKey.value === 'paymentDetail') {
      // 缴费明细：加载缴费明细数据
      loadPaymentDetailData();
    } else if (activeTabKey.value === 'billInfo') {
      // 账单信息：加载账单数据
      loadBillInfoData();
    } else if (activeTabKey.value === 'deposit') {
      // 预存充值：加载预存款余额
      loadDepositBalance();
    } else if (activeTabKey.value === 'adjustment') {
      // 用量调整：加载账单数据（用于选择可调整的账单）
      loadBillInfoData();
    } else if (activeTabKey.value === 'refund') {
      // 账单退费：加载账单数据（用于选择可退费的账单）
      loadBillInfoData();
    } else {
      // 默认加载窗口收费数据
      loadPaymentData();
    }
  } catch (error) {
    console.error('加载用户数据失败:', error);
    message.error('加载用户数据失败，请稍后重试');
  }
}

// 处理标签页切换
function handleTabChange(key: any) {
  activeTabKey.value = key;

  // 如果用户已经加载
  if (userInfo.value.userId) {
    console.log('切换到标签页:', key);

    // 根据不同的标签页加载相应的数据
    if (key === 'payment') {
      // 窗口收费：加载待缴费账单
      loadPaymentData();
    } else if (key === 'paymentDetail') {
      // 缴费明细：加载缴费明细数据
      loadPaymentDetailData();
    } else if (key === 'billInfo') {
      // 账单信息：加载账单数据
      loadBillInfoData();
    } else if (key === 'deposit') {
      // 预存充值：加载预存款余额
      loadDepositBalance();
    } else if (key === 'adjustment') {
      // 用量调整：加载账单数据（用于选择可调整的账单）
      console.log('加载用量调整数据');
      // 确保账单数据已加载
      if (!allBillList.value || allBillList.value.length === 0) {
        loadBillInfoData();
      }
    } else if (key === 'refund') {
      // 账单退费：加载账单数据（用于选择可退费的账单）
      // 确保账单数据已加载
      if (!allBillList.value || allBillList.value.length === 0) {
        loadBillInfoData();
      }
    }
  } else {
    console.warn('用户信息未加载，无法加载标签页数据');
  }
}

// 处理账单月份变更
async function handleBillMonthChange(_: any, _dateString: string) {
  if (userInfo.value.userId) {
    loadBillInfoData();
  }
}

// 加载窗口收费数据（待缴费账单）
async function loadPaymentData() {
  try {
    // 加载待缴费账单
    const billsRes = await getUserBills(userInfo.value.userId.toString(), { status: 'ISSUED' });
    billList.value = billsRes || [];
  } catch (error) {
    console.error('加载待缴费账单失败:', error);
    message.error('加载待缴费账单失败，请稍后重试');
  }
}

// 加载账单信息数据
async function loadBillInfoData() {
  try {
    // 构建查询参数
    const params: { billMonth?: string } = {};
    if (billMonthFilter.value) {
      params.billMonth = billMonthFilter.value;
    }

    // 加载账单数据
    const allBillsRes = await getUserBills(userInfo.value.userId.toString(), params);
    allBillList.value = allBillsRes || [];

    // 调试日志：查看账单数据和可调整的账单选项
    console.log('账单数据:', allBillList.value);
    console.log('可调整的账单选项:', adjustableBillOptions.value);
  } catch (error) {
    console.error('加载账单数据失败:', error);
    message.error('加载账单数据失败，请稍后重试');
  }
}

// 加载缴费明细数据
async function loadPaymentDetailData() {
  try {
    // 加载缴费明细
    const paymentDetailsRes = await getUserPaymentDetails(userInfo.value.userId);
    paymentDetailList.value = paymentDetailsRes || [];
  } catch (error) {
    console.error('加载缴费明细失败:', error);
    message.error('加载缴费明细失败，请稍后重试');
  }
}

// 加载预存款余额
async function loadDepositBalance() {
  if (userInfo.value.userId) {
    try {
      const balanceRes = await getUserDepositBalance(userInfo.value.userId);
      depositBalance.value = balanceRes || 0;
    } catch (error) {
      console.error('加载预存款余额失败:', error);
      depositBalance.value = 0;
    }
  }
}

// 处理账单选择变化
function handleBillSelectionChange(_selectedRowKeys: any[], selectedRows: any[]) {
  selectedBills.value = selectedRows;
}

// 处理缴费
function handlePayBills() {
  if (!selectedBills.value.length) {
    message.warning('请选择需要缴费的账单');
    return;
  }

  // 设置默认缴费金额为总应付金额
  const amount = parseFloat(totalPaymentAmount.value);
  paymentForm.amount = isNaN(amount) ? 0 : amount;
  paymentForm.paymentMethod = 'CASH';
  paymentForm.remark = '';

  // 显示缴费弹窗
  paymentModalVisible.value = true;
}

// 取消缴费
function cancelPayment() {
  paymentModalVisible.value = false;
}

// 确认缴费
async function confirmPayment() {
  if (!isPaymentFormValid.value) {
    message.warning('请填写完整的缴费信息');
    return;
  }

  try {
    const billIds = selectedBills.value.map(bill => bill.billId);
    await payBills({
      billIds,
      amount: paymentForm.amount,
      paymentMethod: paymentForm.paymentMethod,
      remark: paymentForm.remark,
    });

    message.success('缴费成功');
    paymentModalVisible.value = false;

    // 重新加载数据
    if (userInfo.value.userId) {
      loadUserData(userInfo.value.userId);
    }
  } catch (error) {
    console.error('缴费失败:', error);
    message.error('缴费失败，请稍后重试');
  }
}

// 处理预存充值
async function handleDeposit() {
  if (!depositForm.amount) {
    message.warning('请输入充值金额');
    return;
  }

  try {
    await addDeposit({
      userId: userInfo.value.userId,
      amount: depositForm.amount,
      paymentMethod: depositForm.paymentMethod,
      remark: depositForm.remark,
    });

    message.success('充值成功');

    // 重置表单
    depositForm.amount = 0;
    depositForm.remark = '';

    // 重新加载预存款余额
    if (userInfo.value.userId) {
      try {
        const balanceRes = await getUserDepositBalance(userInfo.value.userId);
        depositBalance.value = balanceRes || 0;
      } catch (error) {
        console.error('加载预存款余额失败:', error);
      }
    }
  } catch (error) {
    console.error('充值失败:', error);
    message.error('充值失败，请稍后重试');
  }
}

// 处理调整账单选择变化
async function handleAdjustmentBillChange(billId: any) {
  console.log('选择调整账单:', billId);

  // 检查账单列表是否已加载
  if (!allBillList.value || allBillList.value.length === 0) {
    console.warn('账单数据未加载，正在加载数据...');
    message.warning('正在加载账单数据，请稍后再试');

    // 尝试加载账单数据
    if (userInfo.value.userId) {
      try {
        await loadBillInfoData();
        // 数据加载完成后重新尝试选择账单
        if (allBillList.value && allBillList.value.length > 0) {
          handleAdjustmentBillChange(billId);
        }
      } catch (error) {
        console.error('加载账单数据失败:', error);
        message.error('加载账单数据失败，请稍后重试');
      }
      return;
    }
    return;
  }

  const selectedBill = allBillList.value.find(bill => bill.billId === billId);
  console.log('找到的账单:', selectedBill);

  if (selectedBill) {
    // 设置账单ID
    adjustmentForm.billId = Number(billId);

    try {
      // 调用后端计算接口获取初始数据
      // 创建一个简单的请求对象
      const requestData: Record<string, any> = {
        billId: Number(billId) // 确保 billId 是数字
      };
      const response = await calculateAdjustment(requestData);

      if (response) {
        const result = response;
        console.log('计算调整结果:', result);

        // 设置用水量相关数据
        adjustmentForm.currentConsumption = parseFloat(result.originalConsumption) || 0;
        adjustmentForm.adjustedConsumption = parseFloat(result.adjustedConsumption) || 0;

        // 设置读数相关数据
        adjustmentForm.previousReadingValue = parseFloat(result.originalPreviousReading) || 0;
        adjustmentForm.currentReadingValue = parseFloat(result.originalCurrentReading) || 0;
        adjustmentForm.adjustedCurrentReadingValue = parseFloat(result.adjustedCurrentReading) || 0;

        // 设置费用相关数据
        adjustmentForm.baseChargeAmount = parseFloat(result.originalBaseCharge) || 0;
        adjustmentForm.adjustedBaseChargeAmount = parseFloat(result.adjustedBaseCharge) || 0;

        // 设置附加费相关数据
        adjustmentForm.additionalChargeAmount = parseFloat(result.originalAdditionalCharge) || 0;
        adjustmentForm.adjustedAdditionalChargeAmount = parseFloat(result.adjustedAdditionalCharge) || 0;

        // 设置违约金相关数据
        adjustmentForm.surchargeAmount = parseFloat(result.originalSurcharge) || 0;
        adjustmentForm.adjustedSurchargeAmount = parseFloat(result.adjustedSurcharge) || 0;

        // 设置总金额
        adjustmentForm.totalAmount = parseFloat(result.originalTotalAmount) || 0;
        adjustmentForm.adjustedTotalAmount = parseFloat(result.adjustedTotalAmount) || 0;

        // 设置价格类型
        adjustmentForm.priceType = result.priceType || 'STANDARD';

        // 如果是阶梯水价，设置阶梯水价相关数据
        if (adjustmentForm.priceType === 'LADDER') {
          // 设置阶梯1相关数据
          adjustmentForm.tier1 = parseFloat(result.originalTier1) || 0;
          adjustmentForm.tier1Amount = parseFloat(result.originalTier1Amount) || 0;
          adjustmentForm.adjustedTier1 = parseFloat(result.adjustedTier1) || 0;
          adjustmentForm.adjustedTier1Amount = parseFloat(result.adjustedTier1Amount) || 0;

          // 设置阶梯2相关数据
          adjustmentForm.tier2 = parseFloat(result.originalTier2) || 0;
          adjustmentForm.tier2Amount = parseFloat(result.originalTier2Amount) || 0;
          adjustmentForm.adjustedTier2 = parseFloat(result.adjustedTier2) || 0;
          adjustmentForm.adjustedTier2Amount = parseFloat(result.adjustedTier2Amount) || 0;

          // 设置阶梯3相关数据
          adjustmentForm.tier3 = parseFloat(result.originalTier3) || 0;
          adjustmentForm.tier3Amount = parseFloat(result.originalTier3Amount) || 0;
          adjustmentForm.adjustedTier3 = parseFloat(result.adjustedTier3) || 0;
          adjustmentForm.adjustedTier3Amount = parseFloat(result.adjustedTier3Amount) || 0;
        }
      } else {
        // 如果后端计算接口没有返回数据，使用账单数据
        // 设置用水量相关数据
        adjustmentForm.currentConsumption = selectedBill.consumptionVolume || 0;
        adjustmentForm.adjustedConsumption = selectedBill.consumptionVolume || 0;

        // 设置读数相关数据
        adjustmentForm.previousReadingValue = selectedBill.previousReadingValue || 0;
        adjustmentForm.currentReadingValue = selectedBill.currentReadingValue || 0;
        adjustmentForm.adjustedCurrentReadingValue = selectedBill.currentReadingValue || 0;

        // 设置费用相关数据
        adjustmentForm.baseChargeAmount = selectedBill.baseChargeAmount || 0;
        adjustmentForm.adjustedBaseChargeAmount = selectedBill.baseChargeAmount || 0;

        // 设置附加费相关数据
        adjustmentForm.additionalChargeAmount = selectedBill.additionalChargeAmount || 0;
        adjustmentForm.adjustedAdditionalChargeAmount = selectedBill.additionalChargeAmount || 0;

        // 设置违约金相关数据
        adjustmentForm.surchargeAmount = selectedBill.surchargeAmount || 0;
        adjustmentForm.adjustedSurchargeAmount = selectedBill.surchargeAmount || 0;

        // 设置总金额
        adjustmentForm.totalAmount = selectedBill.totalAmount || 0;
        adjustmentForm.adjustedTotalAmount = selectedBill.totalAmount || 0;
      }
    } catch (error) {
      console.error('获取账单初始数据失败:', error);

      // 如果后端计算接口调用失败，使用账单数据
      // 设置用水量相关数据
      adjustmentForm.currentConsumption = selectedBill.consumptionVolume || 0;
      adjustmentForm.adjustedConsumption = selectedBill.consumptionVolume || 0;

      // 设置读数相关数据
      adjustmentForm.previousReadingValue = selectedBill.previousReadingValue || 0;
      adjustmentForm.currentReadingValue = selectedBill.currentReadingValue || 0;
      adjustmentForm.adjustedCurrentReadingValue = selectedBill.currentReadingValue || 0;

      // 设置费用相关数据
      adjustmentForm.baseChargeAmount = selectedBill.baseChargeAmount || 0;
      adjustmentForm.adjustedBaseChargeAmount = selectedBill.baseChargeAmount || 0;

      // 设置附加费相关数据
      adjustmentForm.additionalChargeAmount = selectedBill.additionalChargeAmount || 0;
      adjustmentForm.adjustedAdditionalChargeAmount = selectedBill.additionalChargeAmount || 0;

      // 设置违约金相关数据
      adjustmentForm.surchargeAmount = selectedBill.surchargeAmount || 0;
      adjustmentForm.adjustedSurchargeAmount = selectedBill.surchargeAmount || 0;

      // 设置总金额
      adjustmentForm.totalAmount = selectedBill.totalAmount || 0;
      adjustmentForm.adjustedTotalAmount = selectedBill.totalAmount || 0;
    }

    // 清空调整原因
    adjustmentForm.reason = '';

    console.log('设置调整表单:', adjustmentForm);
  } else {
    console.warn('未找到对应的账单数据');
    // 重置表单
    resetAdjustmentForm();
  }
}

// 重置调整表单
function resetAdjustmentForm() {
  adjustmentForm.billId = undefined;
  adjustmentForm.currentConsumption = 0;
  adjustmentForm.adjustedConsumption = 0;
  adjustmentForm.previousReadingValue = 0;
  adjustmentForm.currentReadingValue = 0;
  adjustmentForm.adjustedCurrentReadingValue = 0;
  adjustmentForm.baseChargeAmount = 0;
  adjustmentForm.adjustedBaseChargeAmount = 0;
  adjustmentForm.additionalChargeAmount = 0;
  adjustmentForm.adjustedAdditionalChargeAmount = 0;
  adjustmentForm.surchargeAmount = 0;
  adjustmentForm.adjustedSurchargeAmount = 0;
  adjustmentForm.totalAmount = 0;
  adjustmentForm.adjustedTotalAmount = 0;
  // 重置阶梯水价相关字段
  adjustmentForm.priceType = 'STANDARD';
  adjustmentForm.tier1 = 0;
  adjustmentForm.tier1Amount = 0;
  adjustmentForm.adjustedTier1 = 0;
  adjustmentForm.adjustedTier1Amount = 0;
  adjustmentForm.tier2 = 0;
  adjustmentForm.tier2Amount = 0;
  adjustmentForm.adjustedTier2 = 0;
  adjustmentForm.adjustedTier2Amount = 0;
  adjustmentForm.tier3 = 0;
  adjustmentForm.tier3Amount = 0;
  adjustmentForm.adjustedTier3 = 0;
  adjustmentForm.adjustedTier3Amount = 0;
  adjustmentForm.reason = '';
  adjustmentForm.adjustmentType = 'consumption';
}

// 处理读数变更，调用后端计算
async function handleReadingChange(value: number) {
  const previousReadingValue = Number(adjustmentForm.previousReadingValue) || 0;
  const currentReadingValue = Number(adjustmentForm.currentReadingValue) || 0;

  if (value < previousReadingValue) {
    message.warning('调整后读数不能小于上期读数');
    adjustmentForm.adjustedCurrentReadingValue = currentReadingValue;
    return;
  }

  // 调用后端计算接口
  try {
    // 创建一个简单的请求对象，包含所有可能的调整项
    const requestData: Record<string, any> = {
      billId: Number(adjustmentForm.billId), // 确保 billId 是数字
      adjustmentReading: value,
      adjustmentAdditionalCharge: adjustmentForm.adjustedAdditionalChargeAmount,
      adjustmentSurcharge: adjustmentForm.adjustedSurchargeAmount
    };
    console.log('读数调整请求参数:', requestData);
    const response = await calculateAdjustment(requestData);
    console.log('读数调整计算结果:', response);
    if (response) {
      const result = response;
      console.log('读数调整计算结果:', result);
      console.log('读数调整计算结果 - adjustedConsumption:', result.adjustedConsumption);
      console.log('读数调整计算结果 - adjustedBaseCharge:', result.adjustedBaseCharge);
      console.log('读数调整计算结果 - adjustedTotalAmount:', result.adjustedTotalAmount);

      // 更新表单数据
      console.log('更新前的表单数据:', {
        adjustedConsumption: adjustmentForm.adjustedConsumption,
        adjustedBaseChargeAmount: adjustmentForm.adjustedBaseChargeAmount,
        adjustedTotalAmount: adjustmentForm.adjustedTotalAmount
      });

      // 创建一个临时对象，用于一次性更新所有字段
      const tempForm = {
        // 更新调整后用水量（红色箭头指向的第一个字段）
        adjustedConsumption: parseFloat(result.adjustedConsumption) || 0,
        // 更新调整后基础费用（红色箭头指向的第二个字段）
        adjustedBaseChargeAmount: parseFloat(result.adjustedBaseCharge) || 0,
        // 保持附加费的值不变，因为这是用户输入的
        adjustedAdditionalChargeAmount: !isNaN(parseFloat(result.adjustedAdditionalCharge))
          ? parseFloat(result.adjustedAdditionalCharge)
          : adjustmentForm.adjustedAdditionalChargeAmount,
        // 保持违约金的值不变，因为这是用户输入的
        adjustedSurchargeAmount: !isNaN(parseFloat(result.adjustedSurcharge))
          ? parseFloat(result.adjustedSurcharge)
          : adjustmentForm.adjustedSurchargeAmount,
        // 更新调整后总金额（红色箭头指向的第三个字段）
        adjustedTotalAmount: parseFloat(result.adjustedTotalAmount) || 0,
      };

      console.log('新的调整后用水量:', tempForm.adjustedConsumption);
      console.log('新的调整后基础费用:', tempForm.adjustedBaseChargeAmount);
      console.log('新的调整后总金额:', tempForm.adjustedTotalAmount);

      // 一次性更新所有字段
      Object.assign(adjustmentForm, tempForm);

      // 使用nextTick确保Vue能够正确更新视图
      nextTick(() => {
        console.log('视图更新后的表单数据:', {
          adjustedConsumption: adjustmentForm.adjustedConsumption,
          adjustedBaseChargeAmount: adjustmentForm.adjustedBaseChargeAmount,
          adjustedTotalAmount: adjustmentForm.adjustedTotalAmount
        });
      });

      console.log('更新后的表单数据:', {
        adjustedConsumption: adjustmentForm.adjustedConsumption,
        adjustedBaseChargeAmount: adjustmentForm.adjustedBaseChargeAmount,
        adjustedTotalAmount: adjustmentForm.adjustedTotalAmount
      });

      // 如果是阶梯水价，更新阶梯水价相关数据
      if (adjustmentForm.priceType === 'LADDER') {
        // 更新阶梯1相关数据
        adjustmentForm.adjustedTier1 = parseFloat(result.adjustedTier1) || 0;
        adjustmentForm.adjustedTier1Amount = parseFloat(result.adjustedTier1Amount) || 0;

        // 更新阶梯2相关数据
        adjustmentForm.adjustedTier2 = parseFloat(result.adjustedTier2) || 0;
        adjustmentForm.adjustedTier2Amount = parseFloat(result.adjustedTier2Amount) || 0;

        // 更新阶梯3相关数据
        adjustmentForm.adjustedTier3 = parseFloat(result.adjustedTier3) || 0;
        adjustmentForm.adjustedTier3Amount = parseFloat(result.adjustedTier3Amount) || 0;
      }

      console.log('读数调整后的表单数据:', {
        adjustedConsumption: adjustmentForm.adjustedConsumption,
        adjustedBaseChargeAmount: adjustmentForm.adjustedBaseChargeAmount,
        adjustedTotalAmount: adjustmentForm.adjustedTotalAmount,
        priceType: adjustmentForm.priceType,
        ...(adjustmentForm.priceType === 'LADDER' ? {
          adjustedTier1: adjustmentForm.adjustedTier1,
          adjustedTier1Amount: adjustmentForm.adjustedTier1Amount,
          adjustedTier2: adjustmentForm.adjustedTier2,
          adjustedTier2Amount: adjustmentForm.adjustedTier2Amount,
          adjustedTier3: adjustmentForm.adjustedTier3,
          adjustedTier3Amount: adjustmentForm.adjustedTier3Amount,
        } : {})
      });
    }
  } catch (error: any) {
    console.error('计算调整结果失败:', error);
    message.error(`计算调整结果失败: ${error.message || '请稍后重试'}`);

    // 恢复原值
    adjustmentForm.adjustedCurrentReadingValue = currentReadingValue;
  }
}

// 注意：不再需要处理用量变更，因为用量是根据读数自动计算的

// 处理附加费变更，调用后端计算
async function handleAdditionalChargeChange(value: number) {
  const additionalChargeAmount = Number(adjustmentForm.additionalChargeAmount) || 0;

  if (value < 0) {
    message.warning('调整后附加费不能小于0');
    adjustmentForm.adjustedAdditionalChargeAmount = 0;
    return;
  }

  // 调用后端计算接口
  try {
    // 创建一个简单的请求对象，包含所有可能的调整项
    const requestData: Record<string, any> = {
      billId: Number(adjustmentForm.billId), // 确保 billId 是数字
      adjustmentReading: adjustmentForm.adjustedCurrentReadingValue,
      adjustmentAdditionalCharge: value,
      adjustmentSurcharge: adjustmentForm.adjustedSurchargeAmount
    };
    const response = await calculateAdjustment(requestData);

    if (response) {
      const result = response;
      console.log('附加费调整计算结果:', result);

      // 更新表单数据
      console.log('更新前的表单数据:', {
        adjustedConsumption: adjustmentForm.adjustedConsumption,
        adjustedBaseChargeAmount: adjustmentForm.adjustedBaseChargeAmount,
        adjustedTotalAmount: adjustmentForm.adjustedTotalAmount
      });

      // 创建一个临时对象，用于一次性更新所有字段
      const tempForm = {
        // 更新调整后用水量（红色箭头指向的第一个字段）
        adjustedConsumption: parseFloat(result.adjustedConsumption) || 0,
        // 更新调整后基础费用（红色箭头指向的第二个字段）
        adjustedBaseChargeAmount: parseFloat(result.adjustedBaseCharge) || 0,
        // 保持附加费的值为用户输入的值
        adjustedAdditionalChargeAmount: value,
        // 保持违约金的值不变
        adjustedSurchargeAmount: !isNaN(parseFloat(result.adjustedSurcharge))
          ? parseFloat(result.adjustedSurcharge)
          : adjustmentForm.adjustedSurchargeAmount,
        // 更新调整后总金额（红色箭头指向的第三个字段）
        adjustedTotalAmount: parseFloat(result.adjustedTotalAmount) || 0,
      };

      console.log('新的调整后用水量:', tempForm.adjustedConsumption);
      console.log('新的调整后基础费用:', tempForm.adjustedBaseChargeAmount);
      console.log('新的调整后总金额:', tempForm.adjustedTotalAmount);

      // 一次性更新所有字段
      Object.assign(adjustmentForm, tempForm);

      // 使用nextTick确保Vue能够正确更新视图
      nextTick(() => {
        console.log('视图更新后的表单数据:', {
          adjustedConsumption: adjustmentForm.adjustedConsumption,
          adjustedBaseChargeAmount: adjustmentForm.adjustedBaseChargeAmount,
          adjustedTotalAmount: adjustmentForm.adjustedTotalAmount
        });
      });

      // 如果是阶梯水价，更新阶梯水价相关数据
      if (adjustmentForm.priceType === 'LADDER') {
        // 更新阶梯1相关数据
        adjustmentForm.adjustedTier1 = parseFloat(result.adjustedTier1) || 0;
        adjustmentForm.adjustedTier1Amount = parseFloat(result.adjustedTier1Amount) || 0;

        // 更新阶梯2相关数据
        adjustmentForm.adjustedTier2 = parseFloat(result.adjustedTier2) || 0;
        adjustmentForm.adjustedTier2Amount = parseFloat(result.adjustedTier2Amount) || 0;

        // 更新阶梯3相关数据
        adjustmentForm.adjustedTier3 = parseFloat(result.adjustedTier3) || 0;
        adjustmentForm.adjustedTier3Amount = parseFloat(result.adjustedTier3Amount) || 0;
      }

      console.log('附加费调整后的表单数据:', {
        adjustedConsumption: adjustmentForm.adjustedConsumption,
        adjustedBaseChargeAmount: adjustmentForm.adjustedBaseChargeAmount,
        adjustedTotalAmount: adjustmentForm.adjustedTotalAmount,
        priceType: adjustmentForm.priceType,
        ...(adjustmentForm.priceType === 'LADDER' ? {
          adjustedTier1: adjustmentForm.adjustedTier1,
          adjustedTier1Amount: adjustmentForm.adjustedTier1Amount,
          adjustedTier2: adjustmentForm.adjustedTier2,
          adjustedTier2Amount: adjustmentForm.adjustedTier2Amount,
          adjustedTier3: adjustmentForm.adjustedTier3,
          adjustedTier3Amount: adjustmentForm.adjustedTier3Amount,
        } : {})
      });
    }
  } catch (error: any) {
    console.error('计算调整结果失败:', error);
    message.error(`计算调整结果失败: ${error.message || '请稍后重试'}`);

    // 恢复原值
    adjustmentForm.adjustedAdditionalChargeAmount = additionalChargeAmount;
  }
}

// 处理违约金变更，调用后端计算
async function handleSurchargeChange(value: number) {
  const surchargeAmount = Number(adjustmentForm.surchargeAmount) || 0;

  if (value < 0) {
    message.warning('调整后违约金不能小于0');
    adjustmentForm.adjustedSurchargeAmount = 0;
    return;
  }

  // 调用后端计算接口
  try {
    // 创建一个简单的请求对象，包含所有可能的调整项
    const requestData: Record<string, any> = {
      billId: Number(adjustmentForm.billId), // 确保 billId 是数字
      adjustmentReading: adjustmentForm.adjustedCurrentReadingValue,
      adjustmentAdditionalCharge: adjustmentForm.adjustedAdditionalChargeAmount,
      adjustmentSurcharge: value
    };
    const response = await calculateAdjustment(requestData);

    if (response) {
      const result = response;
      console.log('违约金调整计算结果:', result);

      // 更新表单数据
      console.log('更新前的表单数据:', {
        adjustedConsumption: adjustmentForm.adjustedConsumption,
        adjustedBaseChargeAmount: adjustmentForm.adjustedBaseChargeAmount,
        adjustedTotalAmount: adjustmentForm.adjustedTotalAmount
      });

      // 创建一个临时对象，用于一次性更新所有字段
      const tempForm = {
        // 更新调整后用水量（红色箭头指向的第一个字段）
        adjustedConsumption: parseFloat(result.adjustedConsumption) || 0,
        // 更新调整后基础费用（红色箭头指向的第二个字段）
        adjustedBaseChargeAmount: parseFloat(result.adjustedBaseCharge) || 0,
        // 保持附加费的值不变
        adjustedAdditionalChargeAmount: !isNaN(parseFloat(result.adjustedAdditionalCharge))
          ? parseFloat(result.adjustedAdditionalCharge)
          : adjustmentForm.adjustedAdditionalChargeAmount,
        // 保持违约金的值为用户输入的值
        adjustedSurchargeAmount: value,
        // 更新调整后总金额（红色箭头指向的第三个字段）
        adjustedTotalAmount: parseFloat(result.adjustedTotalAmount) || 0,
      };

      console.log('新的调整后用水量:', tempForm.adjustedConsumption);
      console.log('新的调整后基础费用:', tempForm.adjustedBaseChargeAmount);
      console.log('新的调整后总金额:', tempForm.adjustedTotalAmount);

      // 一次性更新所有字段
      Object.assign(adjustmentForm, tempForm);

      // 使用nextTick确保Vue能够正确更新视图
      nextTick(() => {
        console.log('视图更新后的表单数据:', {
          adjustedConsumption: adjustmentForm.adjustedConsumption,
          adjustedBaseChargeAmount: adjustmentForm.adjustedBaseChargeAmount,
          adjustedTotalAmount: adjustmentForm.adjustedTotalAmount
        });
      });

      // 如果是阶梯水价，更新阶梯水价相关数据
      if (adjustmentForm.priceType === 'LADDER') {
        // 更新阶梯1相关数据
        adjustmentForm.adjustedTier1 = parseFloat(result.adjustedTier1) || 0;
        adjustmentForm.adjustedTier1Amount = parseFloat(result.adjustedTier1Amount) || 0;

        // 更新阶梯2相关数据
        adjustmentForm.adjustedTier2 = parseFloat(result.adjustedTier2) || 0;
        adjustmentForm.adjustedTier2Amount = parseFloat(result.adjustedTier2Amount) || 0;

        // 更新阶梯3相关数据
        adjustmentForm.adjustedTier3 = parseFloat(result.adjustedTier3) || 0;
        adjustmentForm.adjustedTier3Amount = parseFloat(result.adjustedTier3Amount) || 0;
      }

      console.log('违约金调整后的表单数据:', {
        adjustedConsumption: adjustmentForm.adjustedConsumption,
        adjustedBaseChargeAmount: adjustmentForm.adjustedBaseChargeAmount,
        adjustedTotalAmount: adjustmentForm.adjustedTotalAmount,
        priceType: adjustmentForm.priceType,
        ...(adjustmentForm.priceType === 'LADDER' ? {
          adjustedTier1: adjustmentForm.adjustedTier1,
          adjustedTier1Amount: adjustmentForm.adjustedTier1Amount,
          adjustedTier2: adjustmentForm.adjustedTier2,
          adjustedTier2Amount: adjustmentForm.adjustedTier2Amount,
          adjustedTier3: adjustmentForm.adjustedTier3,
          adjustedTier3Amount: adjustmentForm.adjustedTier3Amount,
        } : {})
      });
    }
  } catch (error: any) {
    console.error('计算调整结果失败:', error);
    message.error(`计算调整结果失败: ${error.message || '请稍后重试'}`);

    // 恢复原值
    adjustmentForm.adjustedSurchargeAmount = surchargeAmount;
  }
}

// 注意：不再需要处理调整类型变更，因为所有调整项都在同一个页面

// 注意：前端不再计算总金额，所有计算都由后端完成

// 处理账单调整提交
async function handleAdjustConsumption() {
  if (!isAdjustmentFormValid.value) {
    message.warning('请填写完整的调整信息');
    return;
  }

  try {
    // 构建请求参数，包含所有可能的调整项
    let requestParams: any = {
      billId: Number(adjustmentForm.billId), // 确保 billId 是数字
      adjustmentReason: adjustmentForm.reason,
      // 读数调整优先于用量调整
      adjustmentReading: adjustmentForm.adjustedCurrentReadingValue,
      adjustmentAdditionalCharge: adjustmentForm.adjustedAdditionalChargeAmount,
      adjustmentSurcharge: adjustmentForm.adjustedSurchargeAmount
    };

    // 调用后端接口
    await adjustConsumption(requestParams);

    message.success('账单调整成功');

    // 重置表单
    resetAdjustmentForm();

    // 重新加载数据
    if (userInfo.value.userId) {
      loadUserData(userInfo.value.userId);
    }
  } catch (error: any) {
    console.error('账单调整失败:', error);
    message.error(`账单调整失败: ${error.message || '请稍后重试'}`);
  }
}

// 处理退费账单选择变化
async function handleRefundBillChange(billId: any) {
  // 检查账单列表是否已加载
  if (!allBillList.value || allBillList.value.length === 0) {
    console.warn('账单数据未加载，正在加载数据...');
    message.warning('正在加载账单数据，请稍后再试');

    // 尝试加载账单数据
    if (userInfo.value.userId) {
      await loadBillInfoData();
      // 数据加载完成后重新尝试选择账单
      if (allBillList.value && allBillList.value.length > 0) {
        handleRefundBillChange(billId);
      }
      return;
    }
    return;
  }

  const selectedBill = allBillList.value.find(bill => bill.billId === billId);
  if (selectedBill) {
    refundForm.paidAmount = selectedBill.amountPaid || 0;

    try {
      // 查询该账单的退费记录
      const paymentDetails = await getUserPaymentDetails(userInfo.value.userId);

      // 计算已退费金额
      let totalRefunded = 0;
      if (paymentDetails && paymentDetails.length > 0) {
        for (const payment of paymentDetails) {
          if (payment.billId === billId && payment.remark && payment.remark.includes('退费')) {
            // 退费记录的金额是正数，需要累加
            totalRefunded += parseFloat(payment.paymentAmount || 0);
          }
        }
      }

      refundForm.refundedAmount = totalRefunded;
      refundForm.refundableAmount = Math.max(0, refundForm.paidAmount - refundForm.refundedAmount);

      // 如果没有可退费金额，显示提示
      if (refundForm.refundableAmount <= 0) {
        message.warning('该账单已无可退费金额');
      }
    } catch (error) {
      console.error('获取退费记录失败:', error);
      refundForm.refundedAmount = 0;
      refundForm.refundableAmount = refundForm.paidAmount;
    }

    refundForm.refundAmount = 0;
    refundForm.reason = '';
    refundForm.refundMethod = 'CASH';
  } else {
    console.warn('未找到对应的账单数据');
    // 重置退费表单
    refundForm.billId = undefined;
    refundForm.paidAmount = 0;
    refundForm.refundedAmount = 0;
    refundForm.refundableAmount = 0;
    refundForm.refundAmount = 0;
    refundForm.reason = '';
    refundForm.refundMethod = 'CASH';
  }
}

// 处理退费
async function handleRefund() {
  if (!isRefundFormValid.value) {
    message.warning('请填写完整的退费信息');
    return;
  }

  try {
    await refundBill({
      billId: Number(refundForm.billId), // 确保 billId 是数字
      refundAmount: refundForm.refundAmount,
      refundReason: refundForm.reason,
      refundMethod: refundForm.refundMethod,
    });

    message.success('退费成功');

    // 重置表单
    refundForm.billId = null;
    refundForm.paidAmount = 0;
    refundForm.refundedAmount = 0;
    refundForm.refundableAmount = 0;
    refundForm.refundAmount = 0;
    refundForm.reason = '';
    refundForm.refundMethod = 'CASH';

    // 重新加载数据
    if (userInfo.value.userId) {
      loadUserData(userInfo.value.userId);
    }
  } catch (error: any) {
    console.error('退费失败:', error);
    message.error(`退费失败: ${error.message || '请稍后重试'}`);
  }
}
</script>

<style lang="less" scoped>
.counter-payment-container {
  .search-container {
    display: flex;
    align-items: center;
    gap: 16px;

    .search-input-wrapper {
      flex: 1;
      max-width: 500px;
    }

    .search-buttons {
      display: flex;
      gap: 8px;
    }
  }

  .search-option {
    padding: 4px 0;

    .search-option-title {
      font-weight: 500;
    }

    .search-option-desc {
      font-size: 12px;
      color: rgba(0, 0, 0, 0.45);
    }
  }

  .empty-tip {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    color: #999;
    font-size: 16px;
  }

  .bill-table-container {
    margin-bottom: 16px;

    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      .table-title {
        font-size: 16px;
        font-weight: 500;
      }
    }
  }

  .bill-filter-container {
    margin-bottom: 16px;
    display: flex;
    align-items: center;

    .filter-item {
      display: flex;
      align-items: center;
      margin-right: 16px;

      .filter-label {
        margin-right: 8px;
        white-space: nowrap;
      }
    }
  }

  .payment-summary {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-top: 16px;
    padding: 12px 16px;
    background-color: #f5f5f5;
    border-radius: 4px;

    .summary-item {
      margin-left: 24px;

      .label {
        margin-right: 8px;
        color: #666;
      }

      .value {
        font-weight: 500;

        &.amount {
          color: #f5222d;
          font-size: 16px;
        }
      }
    }
  }

  .deposit-container,
  .adjustment-container,
  .refund-container {
    padding: 16px;

    .deposit-info {
      margin-bottom: 24px;
      padding: 16px;
      background-color: #f5f5f5;
      border-radius: 4px;

      .info-item {
        .label {
          margin-right: 8px;
          color: #666;
        }

        .value {
          font-weight: 500;

          &.amount {
            color: #52c41a;
            font-size: 16px;
          }
        }
      }
    }

    .deposit-form,
    .adjustment-form,
    .refund-form {
      max-width: 800px;
    }

    .bill-info-card {
      margin-bottom: 20px;
      border: 1px solid #f0f0f0;
      border-radius: 4px;
      padding: 16px;
      background-color: #fafafa;
    }

    .adjustment-section {
      margin-bottom: 24px;
      border: 1px solid #f0f0f0;
      border-radius: 4px;
      padding: 16px;
      background-color: #fafafa;

      h3 {
        margin-top: 0;
        margin-bottom: 16px;
        padding-bottom: 8px;
        border-bottom: 1px solid #e8e8e8;
        font-size: 16px;
        font-weight: 500;
        color: #333;
      }
    }
  }
}

.payment-modal-content {
  .payment-bills-summary {
    margin-bottom: 24px;
    padding: 16px;
    background-color: #f5f5f5;
    border-radius: 4px;

    .summary-item {
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        margin-right: 8px;
        color: #666;
      }

      .value {
        font-weight: 500;

        &.amount {
          color: #f5222d;
          font-size: 16px;
        }
      }
    }
  }
}

.empty-data {
  padding: 32px 0;
  text-align: center;
  color: #999;
}

.ladder-price-detail {
  margin-top: 16px;

  .ladder-tier {
    margin-bottom: 16px;
    border: 1px solid #f0f0f0;
    border-radius: 4px;
    overflow: hidden;

    &:last-child {
      margin-bottom: 0;
    }

    .ladder-tier-header {
      padding: 8px 12px;
      background-color: #f5f5f5;
      font-weight: 500;
      border-bottom: 1px solid #f0f0f0;
    }

    .ladder-tier-content {
      padding: 12px;
      display: flex;
      flex-wrap: wrap;

      .ladder-tier-item {
        width: 50%;
        padding: 4px 0;

        .label {
          margin-right: 8px;
          color: #666;
        }

        .value {
          font-weight: 500;
        }
      }
    }
  }
}
</style>
