<script lang="ts" setup>
import type { ID } from '#/api/common';
import type { MeterModel } from '#/api/waterfee/model/meter/meterModel';

import { computed, onMounted, ref } from 'vue';

import { Page, useVbenDrawer } from '@vben/common-ui';

import { DownloadOutlined } from '@ant-design/icons-vue';
import { Button, message, Popconfirm, Space } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { meterExport, meterList, meterRemove } from '#/api/waterfee/meter';
import { ensureMeterIdString } from '#/api/waterfee/meter/index';
import { useRouter } from 'vue-router';
import { getDictOptions } from '#/utils/dict';
import { commonDownloadExcel } from '#/utils/file/download';

import { preserveBigInt } from '../../../utils/json-bigint';
import MeterDrawer from './meter-drawer.vue';
import MeterImportModal from './meter-import-modal.vue';
import { columns, querySchema } from './meter.data';

// 使用ref定义水表类型字典，便于初始化完成后更新
const meterTypeDict = ref({
  MECHANICAL: 1, // 机械表
  INTELLIGENT: 2, // 智能表
});

// 批量导入相关功能
const importOpen = ref(false);
const importType = ref(1); // 默认机械表

// 计算属性：获取机械表类型值
const mechanicalType = computed(() => meterTypeDict.value.MECHANICAL);
// 计算属性：获取智能表类型值
const intelligentType = computed(() => meterTypeDict.value.INTELLIGENT);

// 初始化字典值
async function initDicts() {
  try {
    const dictList = await getDictOptions('waterfee_meter_type');
    const mechanicalDict = dictList.find(
      (item) => item.dictLabel === '机械表' || item.dictLabel.includes('机械'),
    );
    const intelligentDict = dictList.find(
      (item) => item.dictLabel === '智能表' || item.dictLabel.includes('智能'),
    );

    if (mechanicalDict) {
      meterTypeDict.value.MECHANICAL = Number(mechanicalDict.value);
    }

    if (intelligentDict) {
      meterTypeDict.value.INTELLIGENT = Number(intelligentDict.value);
    }

    // 更新默认导入类型为机械表
    importType.value = meterTypeDict.value.MECHANICAL;
  } catch (error) {
    console.error('获取水表类型字典失败:', error);
  }
}

const formOptions = {
  commonConfig: {
    labelWidth: 80,
    componentProps: {
      allowClear: true,
    },
  },
  schema: querySchema(),
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
};

const gridOptions = {
  columns,
  height: 'auto',
  width: '100%',
  fit: true,
  autoResize: true,
  tableLayout: 'auto',
  scrollX: {
    enabled: true,
    gt: 10,
  },
  resizable: true,
  resizeConfig: {
    minWidth: 80,
  },
  tooltipConfig: {
    showAll: true,
  },
  // 序号配置
  seqConfig: {
    startIndex: 1,
  },
  proxyConfig: {
    ajax: {
      query: async (
        { page }: { page?: { currentPage: number; pageSize: number } },
        formValues: Record<string, any> = {},
      ) => {
        // 只传递必要的查询参数，避免请求过长
        const resp = await meterList({
          pageNum: page?.currentPage,
          pageSize: page?.pageSize,
          ...formValues,
        });
        return resp;
      },
    },
  },
  id: 'waterfee-meter-index',
};

const [BasicTable, tableApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

const [MeterDrawerComp, drawerApi] = useVbenDrawer({
  connectedComponent: MeterDrawer,
});

// 路由实例
const router = useRouter();

// 打开导入模态框，可以指定水表类型
function openImportModal(type = null) {
  // 如果指定了类型，使用指定类型，否则使用机械表类型
  importType.value = type || mechanicalType.value;
  importOpen.value = true;
}

// 添加机械表
function handleAddMechanical() {
  try {
    drawerApi.setData({ meterType: mechanicalType.value });
    // 使用setTimeout避免可能的DOM渲染问题
    setTimeout(() => {
      drawerApi.open();
    }, 10);
  } catch (error) {
    console.error('Error opening mechanical meter drawer:', error);
  }
}

// 添加智能表
function handleAddIntelligent() {
  try {
    drawerApi.setData({ meterType: intelligentType.value });
    // 使用setTimeout避免可能的DOM渲染问题
    setTimeout(() => {
      drawerApi.open();
    }, 10);
  } catch (error) {
    console.error('Error opening intelligent meter drawer:', error);
  }
}

// 编辑水表
function handleEdit(record: MeterModel) {
  try {
    // 确保ID不会丢失精度
    const safeRecord = preserveBigInt(record);
    const meterId = ensureMeterIdString(safeRecord.meterId);

    console.log('编辑水表, 原始ID:', record.meterId, '处理后ID:', meterId);

    drawerApi.setData({ id: meterId });

    // 使用setTimeout避免可能的DOM渲染问题
    setTimeout(() => {
      drawerApi.open();
    }, 10);
  } catch (error) {
    console.error('Error opening edit meter drawer:', error);
  }
}

// 查看水表详情
function handleDetail(record: MeterModel) {
  try {
    // 确保ID不会丢失精度
    const safeRecord = preserveBigInt(record);
    const meterId = ensureMeterIdString(safeRecord.meterId);

    console.log('查看水表详情, ID:', meterId);

    // 传递readonly标识符，表示只读模式
    drawerApi.setData({
      id: meterId,
      readonly: true,
    });

    // 使用setTimeout避免可能的DOM渲染问题
    setTimeout(() => {
      drawerApi.open();
    }, 10);
  } catch (error) {
    console.error('Error opening meter detail drawer:', error);
  }
}

// 删除水表
async function handleDelete(record: MeterModel) {
  if (record.meterId) {
    // 确保ID不会丢失精度
    const safeRecord = preserveBigInt(record);
    const meterId = ensureMeterIdString(safeRecord.meterId);

    console.log('删除水表, 原始ID:', record.meterId, '处理后ID:', meterId);

    await meterRemove(meterId as ID);
    await tableApi.query();
  }
}

// 导出水表数据
async function handleDownloadExcel() {
  try {
    // 获取当前查询条件
    const formValues = tableApi.formApi.form.values;

    // 获取字典数据
    const [manufacturerDict, caliberDict, accuracyDict] = await Promise.all([
      getDictOptions('meter_factory'),
      getDictOptions('dnmm'),
      getDictOptions('water_meter_accuracy'),
    ]);

    // 构建字典映射
    const createDictMap = (dict) => {
      return dict.reduce((map, item) => {
        map[item.value] = item.dictLabel || item.label;
        return map;
      }, {});
    };

    // 创建字典映射对象
    const dictMap = {
      manufacturer: createDictMap(manufacturerDict),
      caliber: createDictMap(caliberDict),
      accuracy: createDictMap(accuracyDict),
    };

    // 创建额外的表头映射
    const dictHeaders = [
      { label: '厂家翻译', prop: 'manufacturerName' },
      { label: '口径翻译', prop: 'caliberName' },
      { label: '精度翻译', prop: 'accuracyName' },
    ];

    // 调用导出接口
    await commonDownloadExcel(meterExport, '水表数据', {
      ...formValues,
      dictMap,
      headers: dictHeaders,
    });

    message.success('导出成功');
  } catch (error) {
    console.error('导出水表数据失败:', error);
    message.error(`导出失败：${error.message || '未知错误'}`);
  }
}

// 跳转到换表页面
function handleGoToMeterChange() {
  router.push('/waterfee/meter/change');
}

// 组件挂载后加载数据和初始化字典
onMounted(async () => {
  await initDicts();
  tableApi.query();
});
</script>

<template>
  <Page :auto-content-height="true">
    <BasicTable @reload="tableApi.query()" table-title="水表报装管理">
      <template #toolbar-tools>
        <Space>
          <Button type="primary" @click="handleAddMechanical">
            添加机械表
          </Button>
          <Button type="primary" @click="handleAddIntelligent">
            添加智能表
          </Button>
          <Button type="primary" ghost @click="openImportModal()">
            批量导入
          </Button>
          <Button type="primary" ghost @click="handleGoToMeterChange()">
            换表管理
          </Button>
          <Button type="primary" @click="handleDownloadExcel">
            <template #icon><DownloadOutlined /></template>
            导出
          </Button>
        </Space>
      </template>
      <template #action="{ row }">
        <Space>
          <Button type="link" @click="handleEdit(row)"> 编辑 </Button>
          <Button type="link" @click="handleDetail(row)"> 详情 </Button>
          <Popconfirm
            placement="left"
            title="确认删除?"
            @confirm="handleDelete(row)"
          >
            <Button type="link" danger @click.stop=""> 删除 </Button>
          </Popconfirm>
        </Space>
      </template>
    </BasicTable>
    <MeterDrawerComp @reload="tableApi.query()" />
    <MeterImportModal
      v-model:open="importOpen"
      :meter-type="importType"
      @reload="tableApi.query()"
    />
  </Page>
</template>

<style scoped>
/* 增强表格自适应能力 */
:deep(.vben-vxe-grid-toolbar) {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

:deep(.vben-vxe-grid-toolbar-left) {
  flex: 1;
}

/* 确保表格容器能够撑满可用宽度 */
:deep(.vxe-table--main-wrapper),
:deep(.vxe-grid--main-wrapper) {
  width: 100% !important;
}

/* 表格内容区自适应 */
:deep(.vxe-table--body-wrapper) {
  width: 100% !important;
}

/* 表格头部自适应 */
:deep(.vxe-table--header-wrapper) {
  width: 100% !important;
}

/* 确保表格行内容能够正确显示 */
:deep(.vxe-body--row) {
  width: 100%;
}

/* 文本溢出处理 */
:deep(.vxe-cell--label),
:deep(.vxe-cell--title) {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 允许单元格内容溢出显示工具提示 */
:deep(.vxe-cell) {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 响应式处理 */
@media (max-width: 1200px) {
  :deep(.vxe-table--fixed-left-wrapper),
  :deep(.vxe-table--fixed-right-wrapper) {
    /* 确保固定列在小屏上正确显示 */
    z-index: 10;
  }
}

/* 导入弹窗样式 */
.import-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.import-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.import-content {
  padding: 16px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.upload-tip {
  margin-top: 8px;
  color: #888;
}

.import-result {
  background-color: #f9f9f9;
  padding: 16px;
  border-radius: 4px;
  border: 1px solid #eee;
}

.result-summary {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.success {
  color: #52c41a;
}

.fail {
  color: #ff4d4f;
}

.fail-details {
  margin-top: 16px;
}

.import-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 16px;
}
</style>
