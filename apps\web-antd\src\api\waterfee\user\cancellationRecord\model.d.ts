export interface UserCancellationRecordVO {
  /**
   * 主键
   */
  cancellationId: number | string;

  /**
   * 用户ID
   */
  userId: number | string;

  /**
   * 销户原因
   */
  cancellationReason: string;

  /**
   * 销户时间
   */
  cancellationTime: string;
}

export interface UserCancellationRecordForm extends BaseEntity {
  /**
   * 主键
   */
  cancellationId?: number | string;

  /**
   * 用户ID
   */
  userIds: Array<number | string>;

  /**
   * 销户原因
   */
  cancellationReason?: string;

  /**
   * 销户时间
   */
  cancellationTime?: string;
}

export interface UserCancellationRecordQuery extends PageQuery {
  /**
   * 用户编号或用户名
   */
  userNoOrUserName?: string;

  /**
   * 查询开始时间
   */
  startTime?: string;

  /**
   * 查询结束时间
   */
  endTime?: string;
}
