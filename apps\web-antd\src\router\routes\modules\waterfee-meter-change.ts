import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    path: '/waterfee/meter/change',
    name: 'WaterfeeMeterChange',
    component: () => import('#/views/waterfee/meter/change/index.vue'),
    meta: {
      title: '水表换表管理',
    },
  },
  {
    path: '/waterfee/meter/change/add',
    name: 'WaterfeeMeterChangeAdd',
    component: () => import('#/views/waterfee/meter/change/add/index.vue'),
    meta: {
      title: '添加换表记录',
      hideMenu: true,
    },
  },
];

export default routes;
