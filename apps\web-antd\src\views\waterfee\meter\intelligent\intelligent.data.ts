import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { getPopupContainer } from '@vben/utils';
import { getDictOptions } from '#/utils/dict';
import { renderDict } from '#/utils/render';
import { h } from 'vue';
import { Tag } from 'ant-design-vue';

// 查询表单架构
export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入水表编号',
    },
    fieldName: 'meterNo',
    label: '水表编号',
  },
  {
    component: 'Select',
    componentProps: {
      getPopupContainer,
      options: getDictOptions('meter_factory'),
      placeholder: '请选择厂家',
    },
    fieldName: 'manufacturer',
    label: '厂家',
  },
  {
    component: 'Select',
    componentProps: {
      getPopupContainer,
      options: getDictOptions('dnmm'),
      placeholder: '请选择口径',
    },
    fieldName: 'caliber',
    label: '口径',
  },
  {
    component: 'Select',
    componentProps: {
      getPopupContainer,
      options: getDictOptions('water_meter_accuracy'),
      placeholder: '请选择精度',
    },
    fieldName: 'accuracy',
    label: '精度',
  },
];

// 表格列定义
export const columns: VxeGridProps['columns'] = [
  {
    title: '序号',
    width: 60,
    cellRender: { name: 'SafeIndex' },
  },
  {
    title: '水表编号',
    field: 'meterNo',
    minWidth: 120,
    align: 'center',
  },
  {
    title: '用户编号',
    field: 'userNo',
    minWidth: 120,
    align: 'center',
    slots: {
      default: ({ row }) => {
        if (!row.userNo) {
          return h('span', { style: { color: '#ff4d4f' } }, '未关联');
        }
        // return row.userName ? `${row.userName}(${row.userNo})` : row.userNo;
        return row.userNo;
      },
    },
  },
  {
    title: '用户名称',
    field: 'userName',
    minWidth: 120,
    align: 'center',
  },
  {
    title: '厂家',
    field: 'manufacturer',
    minWidth: 120,
    align: 'center',
    slots: {
      default: ({ row }) => {
        return renderDict(row.manufacturer, 'meter_factory');
      },
    },
  },
  {
    title: '口径',
    field: 'caliber',
    minWidth: 100,
    align: 'center',
    slots: {
      default: ({ row }) => {
        return renderDict(row.caliber, 'dnmm');
      },
    },
  },
  {
    title: 'IMEI号',
    field: 'imei',
    minWidth: 120,
    align: 'center',
  },
  {
    title: '精度',
    field: 'accuracy',
    minWidth: 100,
    align: 'center',
    slots: {
      default: ({ row }) => {
        return renderDict(row.accuracy, 'water_meter_accuracy');
      },
    },
  },
  {
    title: '读数',
    field: 'reading',
    minWidth: 120,
    align: 'center',
    slots: {
      default: ({ row }) => {
        return row.reading !== undefined && row.reading !== null
          ? `${row.reading} m³`
          : '- -';
      },
    },
  },
  {
    title: '信号强度',
    field: 'signalStrength',
    minWidth: 120,
    align: 'center',
    slots: {
      default: ({ row }) => {
        if (row.signalStrength === undefined || row.signalStrength === null) {
          return '- -';
        }
        const status = getSignalStatus(row.signalStrength);
        return h(Tag, { color: status.type }, () =>
          `${row.signalStrength} (${status.text})`
        );
      },
    },
  },
  {
    title: '电池电压',
    field: 'batteryVoltage',
    minWidth: 120,
    align: 'center',
    slots: {
      default: ({ row }) => {
        if (row.batteryVoltage === undefined || row.batteryVoltage === null) {
          return '- -';
        }
        const status = getBatteryStatus(row.batteryVoltage);
        return h(Tag, { color: status.type }, () =>
          `${row.batteryVoltage}V (${status.text})`
        );
      },
    },
  },
  {
    title: '通讯方式',
    field: 'communicationMode',
    minWidth: 120,
    align: 'center',
    slots: {
      default: ({ row }) => {
        return renderDict(row.communicationMode, 'waterfee_communication_mode');
      },
    },
  },
  {
    title: '更新时间',
    field: 'updateTime',
    minWidth: 120,
    align: 'center',
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    minWidth: 300,
  },
];

// 信号强度状态
function getSignalStatus(strength: number) {
  if (strength >= 20) return { text: '良好', type: 'success' };
  if (strength >= 15) return { text: '一般', type: 'warning' };
  return { text: '差', type: 'error' };
}

// 电池状态
function getBatteryStatus(voltage: number) {
  if (voltage >= 3.6) return { text: '正常', type: 'success' };
  if (voltage >= 3.3) return { text: '偏低', type: 'warning' };
  return { text: '低电量', type: 'error' };
}



