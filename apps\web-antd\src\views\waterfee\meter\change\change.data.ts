import type { FormSchemaGetter } from '@vben/common-ui';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { getPopupContainer, getVxePopupContainer } from '@vben/utils';

import { h } from 'vue';

import { getDictOptions } from '#/utils/dict';
import { renderDict } from '#/utils/render';

// 查询表单架构
export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入旧表编号',
    },
    fieldName: 'oldMeterNo',
    label: '旧表编号',
  },
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入新表编号',
    },
    fieldName: 'newMeterNo',
    label: '新表编号',
  },
];

// 表格列配置
export const columns: VxeGridProps['columns'] = [
  {
    title: '序号',
    width: 60,
    cellRender: { name: 'SafeIndex' },
  },
  {
    field: 'changeId',
    title: '换表ID',
    visible: false,
  },
  {
    field: 'oldMeterNo',
    title: '旧表编号',
    minWidth: 120,
    align: 'center',
  },
  {
    field: 'oldMeterReading',
    title: '旧表读数',
    minWidth: 100,
    align: 'center',
  },
  {
    field: 'newMeterNo',
    title: '新表编号',
    minWidth: 120,
    align: 'center',
  },
  {
    field: 'newMeterReading',
    title: '新表读数',
    minWidth: 100,
    align: 'center',
  },
  {
    field: 'changeTime',
    title: '换表日期',
    minWidth: 140,
    align: 'center',
  },
  {
    field: 'changeReason',
    title: '换表原因',
    minWidth: 120,
    align: 'center',
    slots: {
      default: ({ row }) => {
        return renderDict(row.changeReason, 'meter_change_reason');
      },
    },
  },
  {
    field: 'operator',
    title: '操作人',
    minWidth: 100,
    align: 'center',
  },  
];

// 换表表单架构
export const formSchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'oldMeterId',
    label: '旧表ID',
    show: false,
  },
  {
    component: 'Input',
    fieldName: 'newMeterId',/*  */
    label: '新表ID',
    show: false,
  },
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入旧表编号',
      allowClear: true,
    },
    fieldName: 'oldMeterNo',
    label: '旧表编号',
    required: true,
    formItemClass: 'col-span-2',
  },
  {
    component: 'InputNumber',
    componentProps: {
      placeholder: '请输入旧表读数',
      min: 0,
      precision: 2,
    },
    fieldName: 'oldMeterReading',
    label: '旧表读数',
    required: true,
    formItemClass: 'col-span-2',
  },
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入新表编号',
      allowClear: true,
    },
    fieldName: 'newMeterNo',
    label: '新表编号',
    required: true,
    formItemClass: 'col-span-2',
  },
  {
    component: 'InputNumber',
    componentProps: {
      placeholder: '请输入新表读数',
      min: 0,
      precision: 2,
    },
    fieldName: 'newMeterReading',
    label: '新表读数',
    required: true,
    formItemClass: 'col-span-2',
  },
  {
    component: 'DatePicker',
    componentProps: {
      placeholder: '请选择换表日期',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      showTime: true,
    },
    fieldName: 'changeTime',
    label: '换表日期',
    required: true,
    formItemClass: 'col-span-2',
  },
  {
    component: 'Select',
    componentProps: {
      placeholder: '请选择换表类型',
      options: getDictOptions('meter_change_type'),
    },
    fieldName: 'changeType',
    label: '换表类型',
    required: true,
    formItemClass: 'col-span-2',
  },
  {
    component: 'Select',
    componentProps: {
      placeholder: '请选择换表原因',
      options: getDictOptions('meter_change_reason'),
    },
    fieldName: 'changeReason',
    label: '换表原因',
    required: true,
    formItemClass: 'col-span-2',
  },
  {
    component: 'TextArea',
    componentProps: {
      placeholder: '请输入备注信息',
      rows: 4,
    },
    fieldName: 'remark',
    label: '备注',
    formItemClass: 'col-span-2',
  },
];
