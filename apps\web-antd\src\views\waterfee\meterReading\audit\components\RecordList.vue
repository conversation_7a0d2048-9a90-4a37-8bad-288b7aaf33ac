<template>
  <div>
    <div class="mb-4">
      <Space>
        <Button type="primary" :disabled="!hasSelectedRecords" @click="handleAudit">
          <template #icon><CheckOutlined /></template>
          审核
        </Button>
        <Button @click="handleSave">
          <template #icon><SaveOutlined /></template>
          保存
        </Button>
      </Space>
    </div>

    <VxeGrid
      ref="gridRef"
      v-bind="gridOptions"
      :loading="loading"
      :data="recordList"
      :edit-config="{ trigger: 'click', mode: 'cell', showStatus: true, showUpdateStatus: true, beforeEditMethod: beforeEditMethod }"
      :row-class-name="rowClassName"
      @checkbox-all="handleSelectionChange"
      @checkbox-change="handleSelectionChange"
      @edit-closed="handleEditClosed"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, reactive, nextTick } from 'vue';
import { Button, Space, message } from 'ant-design-vue';
import { CheckOutlined, SaveOutlined } from '@ant-design/icons-vue';
import { recordColumns } from '../audit.data';
import VXETable from 'vxe-table';
const VxeGrid = VXETable.Grid;
import { auditRecord } from '#/api/waterfee/meterReadingRecord';
import {
  getMeterReadingDetailList,
  getMeterReadingDetail,
  updateMeterReading,
  getIntelligentMeterReadingList
} from '#/api/waterfee/meterReadingRecord/audit';
import { meterInfoByNo } from '#/api/waterfee/meter';
import { getReadingTaskInfo } from '#/api/waterfee/meterReading';
import { meterBookInfo } from '#/api/waterfee/meterbook';
import { areaInfo } from '#/api/waterfee/area';
import { getUser } from '#/api/waterfee/user/archivesManage';

const props = defineProps({
  meterBookId: {
    type: String,
    required: true
  },
  taskId: {
    type: String,
    required: true
  },
  bookData: {
    type: Object,
    default: () => ({})
  },
  searchMeterNo: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['reload']);

// 表格引用
const gridRef = ref();
// 加载状态
const loading = ref(false);
// 内部加载状态标志（避免重复加载）
const isLoading = ref(false);
// 记录列表
const recordList = ref([]);
// 原始记录列表（未过滤）
const originalRecordList = ref([]);
// 选中的记录ID
const selectedRecordIds = ref([]);

// 计算是否有选中记录
const hasSelectedRecords = computed(() => {
  return selectedRecordIds.value.length > 0;
});

// 修改过的记录
const modifiedRecords = ref([]);

// 计算是否有修改过的记录
const hasModifiedRecords = computed(() => {
  return modifiedRecords.value.length > 0;
});

// 缓存对象
const cache = reactive({
  taskInfo: {}, // 缓存任务信息，key为taskId
  meterInfo: {}, // 缓存水表信息，key为meterNo
  areaInfo: {}, // 缓存区域信息，key为businessAreaId
  bookInfo: {}, // 缓存表册信息，key为meterBookId
  userInfo: {}, // 缓存用户信息，key为userId
});

// 表格配置
const gridOptions = {
  border: true,
  showOverflow: true,
  height: 'auto', // 使用自动高度
  minHeight: 400, // 设置更大的最小高度
  maxHeight: 600, // 设置最大高度，避免无限增长
  autoResize: true, // 启用自动调整大小
  width: '100%', // 设置宽度为100%
  fit: true, // 启用列宽自适应
  resizable: true, // 允许调整列宽
  tableLayout: 'fixed', // 使用固定表格布局
  scrollX: {
    enabled: true, // 启用横向滚动
    gt: 10, // 超过10列启用横向滚动
  },
  scrollY: {
    enabled: true, // 启用纵向虚拟滚动
    gt: 30, // 超过30条数据启用虚拟滚动
  },
  columnConfig: {
    resizable: true, // 允许调整列宽
    minWidth: 100, // 最小列宽
  },
  rowConfig: {
    isHover: true,
    isCurrent: true,
    height: 48, // 增加行高
  },
  checkboxConfig: {
    reserve: true,
    highlight: true,
    trigger: 'row'
  },
  columns: recordColumns,
  editRules: {
    currentReading: [
      { required: true, message: '本期抄表读数不能为空' },
      {
        validator: (rule, value) => {
          return value >= 0;
        },
        message: '本期抄表读数不能小于0'
      }
    ]
  },
  // 空数据显示配置
  emptyRender: {
    name: 'EmptyRender',
  }
};

// 防抖定时器和状态变量
let loadTimer = null;
let lastLoadTime = 0; // 上次加载时间

// 监听属性变化
watch(
  [() => props.meterBookId, () => props.taskId],
  async ([newBookId, newTaskId], [oldBookId, oldTaskId]) => {
    // 只有当两个值都存在且至少有一个值发生变化时才加载数据
    if (newBookId && newTaskId && (newBookId !== oldBookId || newTaskId !== oldTaskId)) {
      // 防止重复加载
      if (loading.value) return;

      // 使用字符串比较，避免引用比较问题
      const newBookIdStr = String(newBookId);
      const oldBookIdStr = oldBookId ? String(oldBookId) : '';
      const newTaskIdStr = String(newTaskId);
      const oldTaskIdStr = oldTaskId ? String(oldTaskId) : '';

      if (newBookIdStr === oldBookIdStr && newTaskIdStr === oldTaskIdStr) {
        console.log('属性值相同，不重新加载');
        return;
      }

      console.log(`属性变化: meterBookId ${oldBookIdStr} -> ${newBookIdStr}, taskId ${oldTaskIdStr} -> ${newTaskIdStr}`);
      await loadRecordList();
    }
  },
  { immediate: true }
);

// 监听搜索水表编号变化
watch(
  () => props.searchMeterNo,
  (newValue, oldValue) => {
    if (newValue !== oldValue) {
      console.log(`搜索水表编号变化: ${oldValue} -> ${newValue}`);
      filterRecordsByMeterNo();
    }
  }
);

// 加载抄表记录列表
async function loadRecordList() {
  if (!props.meterBookId || !props.taskId) {
    message.error('表册信息不完整，无法加载抄表记录');
    return;
  }

  // 如果已经在加载中，则不重复加载
  if (loading.value || isLoading.value) return;

  // 如果距离上次加载时间小于1秒，则不重复加载
  const now = Date.now();
  if (now - lastLoadTime < 1000) {
    console.log('加载频率过高，忽略本次加载请求');
    return;
  }

  // 防抖处理，避免短时间内多次调用
  if (loadTimer) clearTimeout(loadTimer);

  loadTimer = setTimeout(async () => {
    isLoading.value = true;
    loading.value = true;
    lastLoadTime = Date.now();

    // 使用 requestAnimationFrame 延迟处理，等待浏览器渲染完成
    await new Promise(resolve => requestAnimationFrame(resolve));
    // 使用 nextTick 延迟处理，等待 Vue DOM 更新完成
    await nextTick();
  try {
    let res;
    const isIntelligent = props.bookData.meterType === '1'; // 1-智能表, 2-机械表
    console.log('水表类型:', props.bookData.meterType, '是否智能表:', isIntelligent);

    // 根据水表类型选择不同的API
    if (isIntelligent) {
      // 如果是智能表，使用智能表专用API
      console.log('使用智能表API获取抄表记录');
      res = await getIntelligentMeterReadingList(props.meterBookId, {
        isAudited: props.bookData.isAudited,
        pageNum: 1,
        pageSize: 100
      });
    } else {
      // 否则使用通用API
      console.log('使用通用API获取抄表记录');
      res = await getMeterReadingDetailList({
        meterBookId: props.meterBookId,
        taskId: props.taskId,
        pageNum: 1,
        pageSize: 100
      });
    }

    if (res && res.rows) {
      originalRecordList.value = res.rows;
      // 应用过滤
      filterRecordsByMeterNo();
      console.log(`成功加载 ${originalRecordList.value.length} 条抄表记录`);

      // 收集需要查询的ID
      const meterNos = new Set();
      const areaIds = new Set();
      const bookIds = new Set();
      const userIds = new Set();

      // 收集所有需要查询的ID
      for (const record of recordList.value) {
        if (record.meterNo && !cache.meterInfo[record.meterNo]) {
          meterNos.add(record.meterNo);
        }
      }

      // 批量获取水表信息
      if (meterNos.size > 0) {
        try {
          const meterPromises = Array.from(meterNos).map(meterNo =>
            meterInfoByNo(meterNo).then(info => {
              if (info) {
                cache.meterInfo[meterNo] = info;

                // 收集需要查询的区域ID和表册ID
                if (info.businessAreaId && !cache.areaInfo[info.businessAreaId]) {
                  areaIds.add(info.businessAreaId);
                }
                if (info.meterBookId && !cache.bookInfo[info.meterBookId]) {
                  bookIds.add(info.meterBookId);
                }
                // 收集需要查询的用户ID
                if (info.userId && !cache.userInfo[info.userId]) {
                  userIds.add(info.userId);
                }
              }
              return { meterNo, info };
            }).catch(error => {
              console.error(`获取水表 ${meterNo} 信息失败:`, error);
              return { meterNo, info: null };
            })
          );

          await Promise.all(meterPromises);
        } catch (error) {
          console.error('批量获取水表信息失败:', error);
        }
      }

      // 批量获取用户信息
      if (userIds.size > 0) {
        try {
          const userPromises = Array.from(userIds).map(userId =>
            getUser(userId).then(info => {
              if (info) {
                cache.userInfo[userId] = info;
              }
              return { userId, info };
            }).catch(error => {
              console.error(`获取用户 ${userId} 信息失败:`, error);
              return { userId, info: null };
            })
          );

          await Promise.all(userPromises);
        } catch (error) {
          console.error('批量获取用户信息失败:', error);
        }
      }

      // 批量获取区域信息
      if (areaIds.size > 0) {
        try {
          const areaPromises = Array.from(areaIds).map(areaId =>
            areaInfo(areaId).then(info => {
              if (info) {
                cache.areaInfo[areaId] = info;
              }
              return { areaId, info };
            }).catch(error => {
              console.error(`获取区域 ${areaId} 信息失败:`, error);
              return { areaId, info: null };
            })
          );

          await Promise.all(areaPromises);
        } catch (error) {
          console.error('批量获取区域信息失败:', error);
        }
      }

      // 批量获取表册信息
      if (bookIds.size > 0) {
        try {
          const bookPromises = Array.from(bookIds).map(bookId =>
            meterBookInfo(bookId).then(info => {
              if (info) {
                cache.bookInfo[bookId] = info;
              }
              return { bookId, info };
            }).catch(error => {
              console.error(`获取表册 ${bookId} 信息失败:`, error);
              return { bookId, info: null };
            })
          );

          await Promise.all(bookPromises);
        } catch (error) {
          console.error('批量获取表册信息失败:', error);
        }
      }

      // 为每条记录填充额外信息
      for (const record of recordList.value) {
        if (record.meterNo) {
          const meterInfo = cache.meterInfo[record.meterNo];
          if (meterInfo) {
            // 更新记录中的字段
            record.businessAreaId = meterInfo.businessAreaId;
            record.meterBookId = meterInfo.meterBookId;
            record.userId = meterInfo.userId;

            // 填充区域信息
            if (meterInfo.businessAreaId && cache.areaInfo[meterInfo.businessAreaId]) {
              record.businessAreaName = cache.areaInfo[meterInfo.businessAreaId].areaName;
            } else {
              record.businessAreaName = meterInfo.businessAreaName;
            }

            // 填充表册信息
            if (meterInfo.meterBookId && cache.bookInfo[meterInfo.meterBookId]) {
              record.meterBookName = cache.bookInfo[meterInfo.meterBookId].bookName;
            } else {
              record.meterBookName = meterInfo.meterBookName;
            }

            // 填充用户信息
            if (meterInfo.userId && cache.userInfo[meterInfo.userId]) {
              const userInfo = cache.userInfo[meterInfo.userId];
              record.userNo = userInfo.userNo || '';
              record.userName = userInfo.userName || '';
            }
          }
        }
      }
    } else {
      recordList.value = [];
      message.warning('没有找到抄表记录');
    }

    console.log('抄表记录列表:', recordList.value);
  } catch (error) {
    console.error('加载抄表记录列表失败:', error);
    message.error('加载抄表记录列表失败，请刷新重试');

    // 如果API调用失败，使用模拟数据（仅用于开发测试）
    if (process.env.NODE_ENV !== 'production') {
      console.log('API调用失败' );
    }
  } finally {
    loading.value = false;
    // 延迟重置状态，避免短时间内重复加载
    setTimeout(() => {
      isLoading.value = false;
    }, 500);
  }
  }, 100); // 100ms 防抖延迟
}

// // 生成模拟数据
// function generateMockData() {
//   const data = [];
//   for (let i = 1; i <= 10; i++) {
//     const lastReading = Math.floor(Math.random() * 1000);
//     const currentReading = lastReading + Math.floor(Math.random() * 100);
//     const oldMeterStopReading = 0;
//     const waterUsage = currentReading - lastReading + oldMeterStopReading;

//     data.push({
//       recordId: `${i}`,
//       meterNo: `M${100000 + i}`,
//       userNo: `U${200000 + i}`,
//       userName: `用户${i}`,
//       lastReading: lastReading,
//       lastReadingTime: '2023-01-01 00:00:00',
//       currentReading: currentReading,
//       oldMeterStopReading: oldMeterStopReading,
//       waterUsage: waterUsage,
//       readingTime: '2023-02-01 00:00:00',
//       isAudited: i % 3 === 0 ? '1' : '0', // 部分记录已审核
//       meterType: i % 2 === 0 ? '1' : '2', // 1-智能表, 2-机械表
//       // 新增字段
//       businessAreaId: '1',
//       businessAreaName: '默认区域',
//       meterBookId: '1',
//       meterBookName: '默认表册'
//     });
//   }
//   return data;
// }

// 处理选择变化
function handleSelectionChange(params) {
  // VXE表格的checkbox-change事件传递的是一个对象，包含records属性
  const records = params && params.records ? params.records : [];
  selectedRecordIds.value = records.map(item => item.recordId);
  console.log('选中的记录ID:', selectedRecordIds.value);
}

// 处理审核
async function handleAudit() {
  if (!hasSelectedRecords.value) {
    message.warning('请选择要审核的记录');
    return;
  }

  try {
    loading.value = true;

    // 获取选中的行
    const selectRecords = gridRef.value.getCheckboxRecords();
    if (!selectRecords || selectRecords.length === 0) {
      message.warning('请选择要审核的记录');
      return;
    }

    // 获取选中记录的ID
    const recordIds = selectRecords.map(record => record.recordId);

    // 批量审核选中的记录
    for (const recordId of recordIds) {
      await auditRecord(recordId);
    }

    message.success('审核成功');

    // 重新加载数据
    await loadRecordList();

    // 通知父组件刷新
    emit('reload');
  } catch (error) {
    console.error('审核失败:', error);
    message.error('审核失败，请重试');
  } finally {
    loading.value = false;
  }
}

// 处理单元格编辑完成事件
function handleEditClosed({ row, column }) {
  console.log('编辑完成:', row, column);

  // 如果是本期抄表读数列
  if (column && column.property === 'currentReading') {
    // 检查该记录是否已在修改列表中
    const existingIndex = modifiedRecords.value.findIndex(r => r.recordId === row.recordId);

    if (existingIndex >= 0) {
      // 更新已存在的记录
      modifiedRecords.value[existingIndex] = { ...row };
    } else {
      // 添加新的修改记录
      modifiedRecords.value.push({ ...row });
    }

    console.log('修改后的记录列表:', modifiedRecords.value);
  }
}

// 处理保存
async function handleSave() {
  // 检查是否有修改的记录
  if (!hasModifiedRecords.value) {
    message.info('没有需要保存的修改');
    return;
  }

  try {
    loading.value = true;

    // 保存每条修改的记录
    for (const record of modifiedRecords.value) {
      console.log('保存记录:', record);
      await updateMeterReading({
        recordId: record.recordId,
        currentReading: record.currentReading
      });
    }

    message.success('保存成功');

    // 清空修改记录列表
    modifiedRecords.value = [];

    // 重新加载数据
    await loadRecordList();

    // 通知父组件刷新
    emit('reload');
  } catch (error) {
    console.error('保存失败:', error);
    message.error('保存失败，请重试');
  } finally {
    loading.value = false;
  }
}

// 根据水表编号过滤记录
function filterRecordsByMeterNo() {
  if (!props.searchMeterNo || !props.searchMeterNo.trim()) {
    // 如果搜索条件为空，显示所有记录
    recordList.value = [...originalRecordList.value];
  } else {
    // 否则根据水表编号过滤
    const searchTerm = props.searchMeterNo.trim().toLowerCase();
    recordList.value = originalRecordList.value.filter(record => {
      return record.meterNo && record.meterNo.toLowerCase().includes(searchTerm);
    });
  }
  console.log(`过滤后的记录数: ${recordList.value.length}`);
}

// 根据行数据设置行的样式类名
function rowClassName({ row }) {
  // 根据审核状态返回不同的类名
  if (row.isAudited === '1') {
    return 'audited-row';
  }
  return 'unaudited-row';
}

// 编辑前的检查方法
function beforeEditMethod({ row, column }) {
  // 如果是本期抄表读数列且记录已审核，则不允许编辑
  if (column.property === 'currentReading' && row.isAudited === '1') {
    message.warning('已审核的记录不可编辑');
    return false;
  }
  return true;
}

// 组件挂载时
onMounted(() => {
  console.log('组件挂载完成');
  // 不使用 connect 方法，避免 $xeToolbar.syncUpdate 错误
});
</script>

<style scoped>
.mb-4 {
  margin-bottom: 16px;
}
:deep(.vxe-table--render-default .vxe-body--row) {
  height: 48px;
}
:deep(.vxe-table--render-default .vxe-body--column) {
  padding: 12px 8px;
}
:deep(.vxe-table--render-default .vxe-header--column) {
  padding: 12px 8px;
  font-weight: 600;
}
:deep(.vxe-input--inner) {
  padding: 8px;
}
:deep(.vxe-table--render-default) {
  font-size: 14px;
}

/* 加深选中行的背景色 */
:deep(.vxe-table--render-default .vxe-body--row.row--checked) {
  background-color: #e6f7ff !important;
}

:deep(.vxe-table--render-default .vxe-body--row.row--checked:hover) {
  background-color: #bae7ff !important;
}

/* 普通行样式 - 白底 */
:deep(.audited-row),
:deep(.unaudited-row) {
  background-color: #ffffff;
}

:deep(.audited-row:hover),
:deep(.unaudited-row:hover) {
  background-color: #f5f5f5 !important;
}

/* 已审核记录的样式 */
:deep(.vxe-table--render-default .vxe-body--row.audited-row) {
  background-color: #fafafa;
}

:deep(.vxe-table--render-default .vxe-body--row.audited-row:hover) {
  background-color: #f0f0f0 !important;
}

/* 已审核记录中不可编辑字段的样式 */
:deep(.vxe-table--render-default .vxe-body--row.audited-row .vxe-body--column) {
  color: #999;
}

/* 确保表格占满整个宽度 */
:deep(.vxe-table--main-wrapper) {
  width: 100%;
}
:deep(.vxe-table--body-wrapper) {
  width: 100%;
}
:deep(.vxe-table--header-wrapper) {
  width: 100%;
}
:deep(.vxe-table) {
  width: 100% !important;
}
:deep(.vxe-table--body) {
  width: 100% !important;
}
</style>
