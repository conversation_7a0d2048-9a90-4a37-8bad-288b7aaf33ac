import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { h } from 'vue';

import { getPopupContainer } from '@vben/utils';

import { getDictOptions } from '#/utils/dict';
import { renderDict } from '#/utils/render';

// 查询表单架构
export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入水表编号',
    },
    fieldName: 'meterNo',
    label: '水表编号',
  },
  {
    component: 'Select',
    componentProps: {
      getPopupContainer,
      options: getDictOptions('meter_factory'),
      placeholder: '请选择厂家',
    },
    fieldName: 'manufacturer',
    label: '厂家',
  },
  {
    component: 'Select',
    componentProps: {
      getPopupContainer,
      options: getDictOptions('dnmm'),
      placeholder: '请选择口径',
    },
    fieldName: 'caliber',
    label: '口径',
  },
  {
    component: 'Select',
    componentProps: {
      getPopupContainer,
      options: getDictOptions('water_meter_accuracy'),
      placeholder: '请选择精度',
    },
    fieldName: 'accuracy',
    label: '精度',
  },
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入用户编号',
    },
    fieldName: 'userNo',
    label: '用户编号',
  },
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入用户名称',
    },
    fieldName: 'userName',
    label: '用户名称',
  },
];

// 表格列配置
export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 40 },
  {
    title: '序号',
    width: 50,
    cellRender: { name: 'SafeIndex' },
  },
  {
    field: 'meterId',
    title: '水表ID',
    visible: false,
  },
  {
    field: 'meterNo',
    title: '水表编号',
    minWidth: 110,
    align: 'center',
  },
  {
    field: 'userNo',
    title: '用户编号',
    minWidth: 100,
    align: 'center',
    slots: {
      default: ({ row }) => {
        if (!row.userNo) {
          return h('span', { style: { color: '#ff4d4f' } }, '未关联');
        }
        return row.userNo;
      },
    },
  },
  {
    field: 'userName',
    title: '用户名称',
    minWidth: 100,
    align: 'center',
  },
  {
    field: 'manufacturer',
    title: '水表厂家',
    minWidth: 100,
    align: 'center',
    slots: {
      default: ({ row }) => {
        return renderDict(row.manufacturer, 'meter_factory');
      },
    },
  },
  {
    field: 'caliber',
    title: '水表口径',
    minWidth: 80,
    align: 'center',
    slots: {
      default: ({ row }) => {
        return renderDict(row.caliber, 'dnmm');
      },
    },
  },
  {
    title: '营业区域',
    field: 'businessAreaName',
    minWidth: 120,
    align: 'left',
    slots: {
      default: ({ row }) => {
        return row.businessAreaName || '未分配';
      },
    },
  },
  {
    title: '抄表手册',
    field: 'meterBookName',
    minWidth: 120,
    align: 'left',
    slots: {
      default: ({ row }) => {
        return row.meterBookName || '未分配';
      },
    },
  },
  {
    field: 'reading',
    title: '上期读数',
    minWidth: 80,
    align: 'center',
  },
  {
    field: 'lastReadDate',
    title: '上次抄表时间',
    minWidth: 140,
    align: 'center',
  },
  {
    field: 'installAddress',
    title: '安装地址',
    minWidth: 160,
    align: 'center',
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 120,
    align: 'center',
  },
];
