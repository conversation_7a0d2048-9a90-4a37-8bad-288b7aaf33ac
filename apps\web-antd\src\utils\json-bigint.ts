/**
 * 专门处理大整数的JSON工具函数
 * 解决JavaScript中大整数精度丢失的问题
 */

// 保存原始的JSON.parse方法
const nativeJSONParse = JSON.parse;

/**
 * 安全解析JSON，保留大整数精度
 * @param jsonString JSON字符串
 * @returns 解析后的对象，大整数会被保留为字符串
 */
export function parseWithBigInt<T>(jsonString: string): T {
  if (!jsonString || typeof jsonString !== 'string') {
    return jsonString as unknown as T;
  }

  return nativeJSONParse(jsonString, (key, value) => {
    // 检查值是否是大整数字符串
    if (typeof value === 'string' && /^\d{16,}$/.test(value)) {
      return value; // 保持字符串格式
    }

    // 检查值是否是数字且可能会丢失精度
    if (
      typeof value === 'number' &&
      value.toString().length > 15 &&
      Math.abs(value) > Number.MAX_SAFE_INTEGER
    ) {
      return value.toString(); // 转换为字符串
    }

    // 检查ID字段
    if (
      (key === 'id' || key.endsWith('Id')) &&
      value !== null &&
      value !== undefined
    ) {
      return String(value); // 确保ID字段始终为字符串
    }

    return value;
  });
}

/**
 * 安全序列化对象，大整数会被转为字符串
 * @param obj 要序列化的对象
 * @returns JSON字符串
 */
export function stringifyWithBigInt<T>(obj: T): string {
  return JSON.stringify(obj, (key, value) => {
    // ID字段特殊处理
    if (
      (key === 'id' || key.endsWith('Id')) &&
      value !== null &&
      value !== undefined
    ) {
      return String(value);
    }

    // 检查值是否是数字且可能会丢失精度
    if (
      typeof value === 'number' &&
      value.toString().length > 15 &&
      Math.abs(value) > Number.MAX_SAFE_INTEGER
    ) {
      return value.toString(); // 转换为字符串
    }
    return value;
  });
}

/**
 * 对对象进行安全序列化和解析，确保大整数不会丢失精度
 * @param obj 要处理的对象
 * @returns 处理后的对象，大整数会被保留为字符串
 */
export function preserveBigInt<T>(obj: T): T {
  if (!obj) return obj;

  // 先进行一次深拷贝，处理已经丢失精度的对象
  const processedObj = JSON.parse(JSON.stringify(obj), (key, value) => {
    // 检查ID字段
    if (
      (key === 'id' || key.endsWith('Id')) &&
      value !== null &&
      value !== undefined
    ) {
      // 先转为字符串，可能已经丢失精度，但保留现有格式
      return String(value);
    }
    return value;
  });

  // 然后再进行一次标准的序列化/解析处理
  const jsonStr = stringifyWithBigInt(processedObj);
  return parseWithBigInt(jsonStr);
}

/**
 * 智能检测大整数ID，尝试从后端原始ID恢复已丢失精度的ID
 * @param id 前端ID (可能已经丢失精度)
 * @param originalIds 后端返回的原始ID列表
 * @returns 修复后的ID
 */
export function repairBigIntId(
  id: string | number,
  originalIds: (string | number)[],
): string {
  // 如果ID不是大整数，直接返回字符串形式
  const idStr = String(id);
  if (idStr.length < 16) {
    return idStr;
  }

  // 将ID转为字符串进行前缀匹配
  const prefix = idStr.substring(0, idStr.length - 3);

  // 在原始ID列表中寻找匹配的ID
  for (const origId of originalIds) {
    const origIdStr = String(origId);
    // 检查前缀是否匹配
    if (origIdStr.startsWith(prefix)) {
      console.log(`修复ID: ${idStr} -> ${origIdStr}`);
      return origIdStr;
    }
  }

  // 未找到匹配，返回原始ID
  return idStr;
}
