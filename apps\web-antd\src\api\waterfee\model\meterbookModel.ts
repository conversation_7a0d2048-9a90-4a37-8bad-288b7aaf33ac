import type { BasicPageParams, BasicFetchResult } from '#/api/common';

export interface MeterBookModel {
  id?: string;
  areaId: string;
  areaName?: string;
  bookNo: string;
  bookName: string;
  readType: string;
  readCycle: string;
  readDay: number;
  readBaseDay: number;
  reader: string;
  readerName?: string;
  readerLeader: string;
  readerLeaderName?: string;
  createBy?: string;
  createTime?: string;
  updateBy?: string;
  updateTime?: string;
  remark?: string;
}

export type MeterBookParams = BasicPageParams & {
  bookNo?: string;
  bookName?: string;
  readType?: string;
  readCycle?: string;
  areaId?: string;
  reader?: string;
  readerLeader?: string;
};

export type MeterBookListGetResultModel = BasicFetchResult<MeterBookModel>;
