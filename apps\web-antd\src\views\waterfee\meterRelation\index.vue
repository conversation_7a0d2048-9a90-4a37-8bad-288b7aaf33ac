<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { Page } from '@vben/common-ui';
import { Button, Space, message, Tabs, Card, Spin, Tree as ATree, Tag, Tooltip } from 'ant-design-vue';
import { PlusOutlined } from '@ant-design/icons-vue';
import { requestClient } from '#/api/request';
import { getDictOptions } from '#/utils/dict';
import { renderDict } from '#/utils/render';
import { getMeterRelationTree } from '#/api/waterfee/meterRelation';

import AddRelationDrawer from './components/AddRelationDrawer.vue';
import ThresholdConfig from './components/ThresholdConfig.vue';
import BalanceAnalysis from './components/BalanceAnalysis.vue';

// 标签页激活键
const activeKey = ref('tree');

// 树形数据
const treeData = ref([]);
const treeLoading = ref(false);

// 字典数据
const dictData = ref({
  manufacturer: [],
  caliber: [],
  accuracy: [],
  meterType: [],
});

// 抽屉状态
const drawerOpen = ref(false);

// 加载树形数据
async function loadTreeData() {
  treeLoading.value = true;
  try {
    // 使用API函数获取数据
    const data = await getMeterRelationTree();
    console.log('获取到的树形数据:', data);

    // 收集所有水表编号
    const allMeterNos = [];
    const collectMeterNos = (nodes) => {
      if (!nodes || !Array.isArray(nodes)) return;

      for (const node of nodes) {
        if (node.meterNo) {
          allMeterNos.push(node.meterNo);
        }
        if (Array.isArray(node.children)) {
          collectMeterNos(node.children);
        }
      }
    };

    collectMeterNos(data);
    console.log('收集到的水表编号:', allMeterNos);

    // 批量获取水表详情，分批处理
    const { meterInfoByNos } = await import('#/api/waterfee/meter');
    let allMeterDetails = [];

    // 分批处理的批次大小
    const BATCH_SIZE = 100;

    try {
      // 将水表编号分成多个批次
      const batches = [];
      for (let i = 0; i < allMeterNos.length; i += BATCH_SIZE) {
        batches.push(allMeterNos.slice(i, i + BATCH_SIZE));
      }

      console.log(`将 ${allMeterNos.length} 个水表编号分成 ${batches.length} 个批次处理`);

      // 逐批处理
      for (let i = 0; i < batches.length; i++) {
        const batch = batches[i];
        console.log(`处理第 ${i + 1}/${batches.length} 批水表编号，数量: ${batch.length}`);

        try {
          // 批量获取当前批次的水表详情
          const batchDetails = await meterInfoByNos(batch);

          if (Array.isArray(batchDetails)) {
            allMeterDetails = [...allMeterDetails, ...batchDetails];
          } else if (batchDetails) {
            // 如果只返回了一个对象
            allMeterDetails.push(batchDetails);
          }
        } catch (err) {
          console.error(`批量获取第 ${i + 1} 批水表详情失败:`, err);

          // 如果批量接口失败，使用单个接口
          const { meterInfoByNo } = await import('#/api/waterfee/meter');

          for (const meterNo of batch) {
            try {
              const detail = await meterInfoByNo(meterNo);
              if (detail) {
                allMeterDetails.push(detail);
              }
            } catch (detailErr) {
              console.error(`获取水表 ${meterNo} 详情失败:`, detailErr);
            }
          }
        }
      }

      console.log(`共获取到 ${allMeterDetails.length} 个水表详情`);
    } catch (error) {
      console.error('批量获取水表详情过程中发生错误:', error);

      // 如果整个批量处理失败，使用单个接口
      const { meterInfoByNo } = await import('#/api/waterfee/meter');

      // 限制并发请求数量
      const MAX_CONCURRENT = 10;
      for (let i = 0; i < allMeterNos.length; i += MAX_CONCURRENT) {
        const batchNos = allMeterNos.slice(i, i + MAX_CONCURRENT);
        const detailsPromises = batchNos.map(async (meterNo) => {
          try {
            return await meterInfoByNo(meterNo);
          } catch (err) {
            console.error(`获取水表 ${meterNo} 详情失败:`, err);
            return null;
          }
        });

        const batchDetails = (await Promise.all(detailsPromises)).filter(Boolean);
        allMeterDetails = [...allMeterDetails, ...batchDetails];
      }
    }

    // 创建水表编号到详情的映射
    const meterDetailsMap = new Map();
    allMeterDetails.forEach(detail => {
      if (detail && detail.meterNo) {
        meterDetailsMap.set(detail.meterNo, detail);
      }
    });

    // 收集所有区域ID和表册ID
    const areaIds = new Set();
    const bookIds = new Set();

    allMeterDetails.forEach(detail => {
      if (detail?.businessAreaId) {
        areaIds.add(detail.businessAreaId);
      }
      if (detail?.meterBookId) {
        bookIds.add(detail.meterBookId);
      }
    });

    // 批量获取区域信息，分批处理
    const areaInfoMap = new Map();
    if (areaIds.size > 0) {
      // 转换为数组
      const areaIdArray = [...areaIds];

      // 分批处理的批次大小
      const AREA_BATCH_SIZE = 50;

      try {
        // 将区域ID分成多个批次
        const areaBatches = [];
        for (let i = 0; i < areaIdArray.length; i += AREA_BATCH_SIZE) {
          areaBatches.push(areaIdArray.slice(i, i + AREA_BATCH_SIZE));
        }

        console.log(`将 ${areaIdArray.length} 个区域ID分成 ${areaBatches.length} 个批次处理`);

        const { areaInfoBatch } = await import('#/api/waterfee/area');

        // 逐批处理
        for (let i = 0; i < areaBatches.length; i++) {
          const batch = areaBatches[i];
          console.log(`处理第 ${i + 1}/${areaBatches.length} 批区域ID，数量: ${batch.length}`);

          try {
            // 批量获取当前批次的区域信息
            const areaInfos = await areaInfoBatch(batch);

            if (Array.isArray(areaInfos)) {
              areaInfos.forEach(area => {
                if (area && area.areaId) {
                  areaInfoMap.set(area.areaId, area);
                }
              });
            } else if (areaInfos && areaInfos.areaId) {
              // 如果只返回了一个对象
              areaInfoMap.set(areaInfos.areaId, areaInfos);
            }
          } catch (err) {
            console.error(`批量获取第 ${i + 1} 批区域信息失败:`, err);

            // 如果批量接口失败，使用单个接口
            const { areaInfo } = await import('#/api/waterfee/area');

            // 限制并发请求数量
            const MAX_CONCURRENT = 5;
            for (let j = 0; j < batch.length; j += MAX_CONCURRENT) {
              const concurrentBatch = batch.slice(j, j + MAX_CONCURRENT);
              const areaPromises = concurrentBatch.map(async (areaId) => {
                try {
                  const area = await areaInfo(areaId);
                  if (area && area.areaId) {
                    areaInfoMap.set(areaId, area);
                  }
                  return area;
                } catch (detailErr) {
                  console.error(`获取区域 ${areaId} 信息失败:`, detailErr);
                  return null;
                }
              });

              await Promise.all(areaPromises);
            }
          }
        }
      } catch (error) {
        console.error('批量获取区域信息过程中发生错误:', error);

        // 如果整个批量处理失败，使用单个接口
        const { areaInfo } = await import('#/api/waterfee/area');

        // 限制并发请求数量
        const MAX_CONCURRENT = 5;
        for (let i = 0; i < areaIdArray.length; i += MAX_CONCURRENT) {
          const batchIds = areaIdArray.slice(i, i + MAX_CONCURRENT);
          const areaPromises = batchIds.map(async (areaId) => {
            try {
              const area = await areaInfo(areaId);
              if (area && area.areaId) {
                areaInfoMap.set(areaId, area);
              }
              return area;
            } catch (err) {
              console.error(`获取区域 ${areaId} 信息失败:`, err);
              return null;
            }
          });

          await Promise.all(areaPromises);
        }
      }

      console.log(`共获取到 ${areaInfoMap.size} 个区域信息`);
    }

    // 批量获取表册信息，分批处理
    const bookInfoMap = new Map();
    if (bookIds.size > 0) {
      // 转换为数组
      const bookIdArray = [...bookIds];

      // 分批处理的批次大小
      const BOOK_BATCH_SIZE = 50;

      try {
        // 将表册ID分成多个批次
        const bookBatches = [];
        for (let i = 0; i < bookIdArray.length; i += BOOK_BATCH_SIZE) {
          bookBatches.push(bookIdArray.slice(i, i + BOOK_BATCH_SIZE));
        }

        console.log(`将 ${bookIdArray.length} 个表册ID分成 ${bookBatches.length} 个批次处理`);

        const { meterBookInfoBatch } = await import('#/api/waterfee/meterbook');

        // 逐批处理
        for (let i = 0; i < bookBatches.length; i++) {
          const batch = bookBatches[i];
          console.log(`处理第 ${i + 1}/${bookBatches.length} 批表册ID，数量: ${batch.length}`);

          try {
            // 批量获取当前批次的表册信息
            const bookInfos = await meterBookInfoBatch(batch);

            if (Array.isArray(bookInfos)) {
              bookInfos.forEach(book => {
                if (book && book.bookId) {
                  bookInfoMap.set(book.bookId, book);
                }
              });
            } else if (bookInfos && bookInfos.bookId) {
              // 如果只返回了一个对象
              bookInfoMap.set(bookInfos.bookId, bookInfos);
            }
          } catch (err) {
            console.error(`批量获取第 ${i + 1} 批表册信息失败:`, err);

            // 如果批量接口失败，使用单个接口
            const { meterBookInfo } = await import('#/api/waterfee/meterbook');

            // 限制并发请求数量
            const MAX_CONCURRENT = 5;
            for (let j = 0; j < batch.length; j += MAX_CONCURRENT) {
              const concurrentBatch = batch.slice(j, j + MAX_CONCURRENT);
              const bookPromises = concurrentBatch.map(async (bookId) => {
                try {
                  const book = await meterBookInfo(bookId);
                  if (book && book.bookId) {
                    bookInfoMap.set(bookId, book);
                  }
                  return book;
                } catch (detailErr) {
                  console.error(`获取表册 ${bookId} 信息失败:`, detailErr);
                  return null;
                }
              });

              await Promise.all(bookPromises);
            }
          }
        }
      } catch (error) {
        console.error('批量获取表册信息过程中发生错误:', error);

        // 如果整个批量处理失败，使用单个接口
        const { meterBookInfo } = await import('#/api/waterfee/meterbook');

        // 限制并发请求数量
        const MAX_CONCURRENT = 5;
        for (let i = 0; i < bookIdArray.length; i += MAX_CONCURRENT) {
          const batchIds = bookIdArray.slice(i, i + MAX_CONCURRENT);
          const bookPromises = batchIds.map(async (bookId) => {
            try {
              const book = await meterBookInfo(bookId);
              if (book && book.bookId) {
                bookInfoMap.set(bookId, book);
              }
              return book;
            } catch (err) {
              console.error(`获取表册 ${bookId} 信息失败:`, err);
              return null;
            }
          });

          await Promise.all(bookPromises);
        }
      }

      console.log(`共获取到 ${bookInfoMap.size} 个表册信息`);
    }

    // 处理数据，确保ID是字符串并获取额外信息
    const processNode = (node) => {
      if (!node) return null;

      // 获取水表详细信息
      const meterDetails = meterDetailsMap.get(node.meterNo);

      // 获取区域和表册信息
      let businessAreaName = '';
      let meterBookName = '';

      if (meterDetails?.businessAreaId) {
        const areaInfo = areaInfoMap.get(meterDetails.businessAreaId);
        businessAreaName = areaInfo?.areaName || '';
      }

      if (meterDetails?.meterBookId) {
        const bookInfo = bookInfoMap.get(meterDetails.meterBookId);
        meterBookName = bookInfo?.bookName || '';
      }

      const processedNode = {
        ...node,
        meterId: String(node.meterId || ''),
        meterNo: String(node.meterNo || ''),
        title: String(node.meterNo || ''), // 用于显示的标题
        key: String(node.meterId || ''), // 树节点的唯一标识
        isLeaf: !Array.isArray(node.children) || node.children.length === 0,
        children: [],
        // 添加水表详细信息
        manufacturer: meterDetails?.manufacturer || '',
        manufacturerName: getDictLabel('manufacturer', meterDetails?.manufacturer),
        caliber: meterDetails?.caliber || '',
        caliberName: getDictLabel('caliber', meterDetails?.caliber),
        installAddress: meterDetails?.installAddress || '',
        meterType: meterDetails?.meterType || '',
        meterTypeName: getDictLabel('meterType', meterDetails?.meterType),
        accuracy: meterDetails?.accuracy || '',
        accuracyName: getDictLabel('accuracy', meterDetails?.accuracy),
        businessAreaId: meterDetails?.businessAreaId || '',
        businessAreaName: businessAreaName || meterDetails?.businessAreaName || '',
        meterBookId: meterDetails?.meterBookId || '',
        meterBookName: meterBookName || meterDetails?.meterBookName || '',
        userNo: meterDetails?.userNo || '',
        userName: meterDetails?.userName || ''
      };

      // 如果有子节点，递归处理
      if (Array.isArray(node.children) && node.children.length > 0) {
        processedNode.children = node.children.map(child => processNode(child)).filter(Boolean);
      }

      return processedNode;
    };

    // 处理所有节点
    treeData.value = data.map(item => processNode(item)).filter(Boolean);

    console.log('处理后的树形数据:', treeData.value);
  } catch (error) {
    console.error('获取水表总分树结构失败:', error);
    message.error('获取水表总分树结构失败');
    treeData.value = [];
  } finally {
    treeLoading.value = false;
  }
}

// 获取水表详细信息
async function getMeterDetails(meterNo) {
  try {
    if (!meterNo) return null;

    const { meterInfoByNo } = await import('#/api/waterfee/meter');
    const meterInfo = await meterInfoByNo(meterNo);
    return meterInfo;
  } catch (error) {
    console.error(`获取水表 ${meterNo} 详细信息失败:`, error);
    return null;
  }
}

// 添加总分表关系
function handleAddRelation() {
  drawerOpen.value = true;
}

// 处理添加成功
function handleSuccess() {
  loadTreeData();
}



// 加载字典数据
async function loadDictData() {
  try {
    const [manufacturerDict, caliberDict, accuracyDict, meterTypeDict] = await Promise.all([
      getDictOptions('meter_factory'),
      getDictOptions('dnmm'),
      getDictOptions('water_meter_accuracy'),
      getDictOptions('waterfee_meter_type'),
    ]);

    dictData.value = {
      manufacturer: manufacturerDict,
      caliber: caliberDict,
      accuracy: accuracyDict,
      meterType: meterTypeDict,
    };

    console.log('字典数据加载成功:', dictData.value);
  } catch (error) {
    console.error('加载字典数据失败:', error);
    message.error('加载字典数据失败');
  }
}

// 获取字典标签
function getDictLabel(dictType, value) {
  if (!value) return '';

  const dict = dictData.value[dictType];
  if (!dict || !Array.isArray(dict)) return value;

  const item = dict.find(d => d.value === value);
  return item?.label || value;
}

// 初始化
onMounted(() => {
  loadDictData();
  loadTreeData();
});
</script>

<template>
  <Page :auto-content-height="true">
    <Card title="水表总分关系管理">
      <Tabs v-model:activeKey="activeKey">
        <template #tabBarExtraContent>
          <Button v-if="activeKey === 'tree'" type="primary" @click="handleAddRelation">
            <template #icon><PlusOutlined /></template>
            添加总分关系
          </Button>
        </template>

        <!-- 总分表树形结构 -->
        <Tabs.TabPane key="tree" tab="总分表结构">
          <Spin :spinning="treeLoading">
            <div class="tree-container">
              <a-tree
                v-if="treeData.length > 0"
                :tree-data="treeData"
                :defaultExpandAll="true"
                blockNode
                showLine
              >
                <template #title="{ dataRef }">
                  <div class="meter-node">
                    <div class="meter-node-title">
                      <span class="meter-no">{{ dataRef.meterNo }}</span>
                      <a-tag v-if="!dataRef.isLeaf" color="blue">总表</a-tag>
                      <a-tag v-else color="green">分表</a-tag>
                      <a-tag v-if="dataRef.meterType" :color="dataRef.meterType === '1' ? 'orange' : 'purple'">
                        {{ dataRef.meterTypeName || (dataRef.meterType === '1' ? '机械表' : '智能表') }}
                      </a-tag>
                    </div>
                    <div class="meter-node-info">
                      <a-tooltip v-if="dataRef.userName" placement="top">
                        <template #title>用户信息</template>
                        <span class="info-item">用户: {{ dataRef.userName }}</span>
                      </a-tooltip>
                      <a-tooltip v-if="dataRef.businessAreaName" placement="top">
                        <template #title>所属区域</template>
                        <span class="info-item">区域: {{ dataRef.businessAreaName }}</span>
                      </a-tooltip>
                      <a-tooltip v-if="dataRef.meterBookName" placement="top">
                        <template #title>所属表册</template>
                        <span class="info-item">表册: {{ dataRef.meterBookName }}</span>
                      </a-tooltip>
                      <a-tooltip v-if="dataRef.manufacturerName || dataRef.manufacturer" placement="top">
                        <template #title>生产厂家</template>
                        <span class="info-item">厂家: {{ dataRef.manufacturerName || dataRef.manufacturer }}</span>
                      </a-tooltip>
                      <a-tooltip v-if="dataRef.caliberName || dataRef.caliber" placement="top">
                        <template #title>口径</template>
                        <span class="info-item">口径: {{ dataRef.caliberName || dataRef.caliber }}</span>
                      </a-tooltip>
                      <a-tooltip v-if="dataRef.accuracyName || dataRef.accuracy" placement="top">
                        <template #title>精度</template>
                        <span class="info-item">精度: {{ dataRef.accuracyName || dataRef.accuracy }}</span>
                      </a-tooltip>
                      <a-tooltip v-if="dataRef.installAddress" placement="top">
                        <template #title>安装地址</template>
                        <span class="info-item">地址: {{ dataRef.installAddress }}</span>
                      </a-tooltip>
                    </div>
                  </div>
                </template>
              </a-tree>
              <div v-else class="empty-data">
                暂无总分表关系数据
              </div>
            </div>
          </Spin>
        </Tabs.TabPane>

        <!-- 总分表平衡分析 -->
        <Tabs.TabPane key="balance" tab="平衡分析">
          <BalanceAnalysis />
        </Tabs.TabPane>

        <!-- 总分表预警阈值配置 -->
        <Tabs.TabPane key="threshold" tab="预警阈值配置">
          <ThresholdConfig />
        </Tabs.TabPane>
      </Tabs>
    </Card>

    <!-- 添加总分表关系抽屉 -->
    <AddRelationDrawer
      v-if="drawerOpen"
      v-model:open="drawerOpen"
      @reload="handleSuccess"
    />
  </Page>
</template>

<style scoped>
.tree-container {
  min-height: 400px;
  padding: 16px;
}

.empty-data {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
  color: #999;
  font-size: 14px;
}

.meter-node {
  padding: 8px 0;
}

.meter-node-title {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.meter-no {
  font-weight: bold;
  font-size: 15px;
  margin-right: 8px;
}

.meter-node-info {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  font-size: 12px;
  color: #666;
}

.info-item {
  background-color: #f5f5f5;
  padding: 2px 6px;
  border-radius: 4px;
}

/* 自定义树节点样式 */
:deep(.ant-tree-node-content-wrapper) {
  width: 100%;
}

:deep(.ant-tree-treenode) {
  width: 100%;
  padding: 2px 0;
}

:deep(.ant-tree-node-selected) {
  background-color: #e6f7ff !important;
}
</style>
