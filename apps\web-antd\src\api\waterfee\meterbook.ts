import type {
  MeterBookModel,
  MeterBookParams,
  MeterBookListGetResultModel,
} from './model/meterbookModel';
import type { ID } from '#/api/common';
import { requestClient } from '#/api/request';

enum Api {
  meterBookList = '/waterfee/meterbook/list',
  root = '/waterfee/meterbook',
}

/**
 * 抄表手册列表
 * @param params 查询参数
 * @returns 抄表手册列表
 */
export function meterBookList(params?: MeterBookParams) {
  return requestClient.get<MeterBookListGetResultModel>(Api.meterBookList, {
    params,
  });
}

/**
 * 抄表手册详情
 * @param id 抄表手册ID
 * @returns 抄表手册详情
 */
export function meterBookInfo(id: ID) {
  return requestClient.get<MeterBookModel>(`${Api.root}/${id}`);
}

/**
 * 批量获取表册信息
 * @param bookIds 表册ID数组
 * @returns 表册信息列表
 */
export function meterBookInfoBatch(bookIds: ID[]) {
  // 使用 POST 请求传数组，避免 URL 太长
  return requestClient.post<MeterBookModel[]>('/waterfee/meter/books', bookIds);
}

/**
 * 抄表手册新增
 * @param data 参数
 */
export function meterBookAdd(data: Partial<MeterBookModel>) {
  return requestClient.postWithMsg<void>(Api.root, data);
}

/**
 * 抄表手册更新
 * @param data 参数
 */
export function meterBookUpdate(data: Partial<MeterBookModel>) {
  return requestClient.putWithMsg<void>(Api.root, data);
}

/**
 * 抄表手册删除
 * @param id 抄表手册ID
 * @returns void
 */
export function meterBookRemove(id: ID) {
  return requestClient.deleteWithMsg<void>(`${Api.root}/${id}`);
}
