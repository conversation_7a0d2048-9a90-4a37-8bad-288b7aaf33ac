<script setup lang="ts">
import type { UserBasicInfoChangeRecordForm } from '#/api/waterfee/user/basicInfoChangeRecord/model.d';

import { useVbenDrawer } from '@vben/common-ui';
import { cloneDeep } from '@vben/utils';

import { useVbenForm } from '#/adapter/form';
import {
  changeBasicInfoUser,
  getUser,
} from '#/api/waterfee/user/archivesManage';
import { getDictOptions } from '#/utils/dict';

const emit = defineEmits<{ reload: [] }>();

const [BasicForm, formApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'col-span-1',
  },
  layout: 'vertical',
  schema: [
    {
      component: 'Input',
      dependencies: {
        show: () => false,
        triggerFields: [''],
      },
      fieldName: 'userId',
      label: '用户ID',
    },
    {
      component: 'Select',
      fieldName: 'beforeCustomerNature',
      label: '原客户性质',
      componentProps: {
        disabled: true,
        options: getDictOptions('waterfee_user_customer_nature'),
      },
    },
    {
      component: 'Select',
      fieldName: 'beforeUseWaterNature',
      label: '原用水性质',
      componentProps: {
        disabled: true,
        options: getDictOptions('waterfee_user_use_water_nature'),
      },
    },
    {
      component: 'InputNumber',
      fieldName: 'beforeUseWaterNumber',
      label: '原用水人数',
      componentProps: {
        disabled: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'beforePhoneNumber',
      label: '原手机号码',
      componentProps: {
        disabled: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'beforeAddress',
      label: '原用水地址',
      componentProps: {
        disabled: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'beforeEmail',
      label: '原电子邮箱',
      componentProps: {
        disabled: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'beforeTaxpayerIdentificationNumber',
      label: '原纳税人识别号',
      componentProps: {
        disabled: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'beforeInvoiceName',
      label: '原开票名称',
      componentProps: {
        disabled: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'beforeInvoiceType',
      label: '原发票类型',
      componentProps: {
        disabled: true,
        options: getDictOptions('waterfee_user_invoice_type'),
      },
    },
    {
      component: 'Select',
      fieldName: 'afterCustomerNature',
      label: '新客户性质',
      componentProps: {
        options: getDictOptions('waterfee_user_customer_nature'),
      },
      rules: 'required',
    },
    {
      component: 'Select',
      fieldName: 'afterUseWaterNature',
      label: '新用水性质',
      componentProps: {
        options: getDictOptions('waterfee_user_use_water_nature'),
      },
      rules: 'required',
    },
    {
      component: 'InputNumber',
      fieldName: 'afterUseWaterNumber',
      label: '新用水人数',
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'afterPhoneNumber',
      label: '新手机号码',
      rules: 'required|phone',
    },
    {
      component: 'Input',
      fieldName: 'afterAddress',
      label: '新用水地址',
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'afterEmail',
      label: '新电子邮箱',
      componentProps: {
        type: 'email',
      },
    },
    {
      component: 'Input',
      fieldName: 'afterTaxpayerIdentificationNumber',
      label: '新纳税人识别号',
    },
    {
      component: 'Input',
      fieldName: 'afterInvoiceName',
      label: '新开票名称',
    },
    {
      component: 'Select',
      fieldName: 'afterInvoiceType',
      label: '新发票类型',
      componentProps: {
        options: getDictOptions('waterfee_user_invoice_type'),
      },
    },
    // {
    //   component: 'Textarea',
    //   fieldName: 'changeContent',
    //   label: '变更内容',
    //   componentProps: {
    //     rows: 4,
    //     placeholder: '请输入变更内容说明',
    //   },
    //   formItemClass: 'col-span-2',
    //   rules: 'required',
    // },
  ],
  showDefaultActions: false,
  wrapperClass: 'grid-cols-2 gap-x-4',
});

const [BasicDrawer, drawerApi] = useVbenDrawer({
  title: '基础信息变更',
  onClosed: handleCancel,
  onConfirm: handleConfirm,
  onOpenChange: async (isOpen) => {
    if (!isOpen) {
      return null;
    }
    drawerApi.drawerLoading(true);
    const { row } = drawerApi.getData() as { row?: any };
    if (row && row.userId) {
      const record = await getUser(row.userId);
      if (!record) {
        drawerApi.drawerLoading(false);
        return;
      }
      // 设置原用户信息
      await formApi.setValues({
        userId: row.userId,
        beforeCustomerNature: record.customerNature,
        beforeUseWaterNature: record.useWaterNature,
        beforeUseWaterNumber: record.useWaterNumber,
        beforePhoneNumber: record.phoneNumber,
        beforeAddress: record.address,
        beforeEmail: record.email,
        beforeTaxpayerIdentificationNumber: record.taxpayerIdentificationNumber,
        beforeInvoiceName: record.invoiceName,
        beforeInvoiceType: record.invoiceType,
        // 默认新信息与原信息相同
        afterCustomerNature: record.customerNature,
        afterUseWaterNature: record.useWaterNature,
        afterUseWaterNumber: record.useWaterNumber,
        afterPhoneNumber: record.phoneNumber,
        afterAddress: record.address,
        afterEmail: record.email,
        afterTaxpayerIdentificationNumber: record.taxpayerIdentificationNumber,
        afterInvoiceName: record.invoiceName,
        afterInvoiceType: record.invoiceType,
      });
    }
    drawerApi.drawerLoading(false);
  },
});

async function handleConfirm() {
  try {
    drawerApi.drawerLoading(true);
    const { valid } = await formApi.validate();
    if (!valid) {
      return;
    }
    // 可能会做数据处理 使用cloneDeep深拷贝
    const data = cloneDeep(
      await formApi.getValues(),
    ) as UserBasicInfoChangeRecordForm;
    await changeBasicInfoUser(data);
    emit('reload');
    await handleCancel();
  } catch (error) {
    console.error(error);
  } finally {
    drawerApi.drawerLoading(false);
  }
}

async function handleCancel() {
  drawerApi.close();
  await formApi.resetForm();
}
</script>

<template>
  <BasicDrawer :close-on-click-modal="false" class="w-[800px]">
    <BasicForm />
  </BasicDrawer>
</template>
