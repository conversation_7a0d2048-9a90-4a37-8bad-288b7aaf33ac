import type {
  SurchargeListGetResultModel,
  SurchargeModel,
  SurchargeParams,
} from './model/surchargeModel';

import type { ID } from '#/api/common';

import { requestClient } from '#/api/request';

enum Api {
  root = '/waterfee/surcharge',
  surchargeList = '/waterfee/surcharge/list',
}

/**
 * 附加费列表
 * @param params 查询参数
 * @returns 附加费列表
 */
export function surchargeList(params?: SurchargeParams) {
  return requestClient.get<SurchargeListGetResultModel>(Api.surchargeList, {
    params,
  });
}

/**
 * 附加费详情
 * @param id 附加费ID
 * @returns 附加费详情
 */
export function surchargeInfo(id: ID) {
  return requestClient.get<SurchargeModel>(`${Api.root}/${id}`);
}

/**
 * 附加费新增
 * @param data 参数
 */
export function surchargeAdd(data: Partial<SurchargeModel>) {
  return requestClient.postWithMsg<void>(Api.root, data);
}

/**
 * 附加费更新
 * @param data 参数
 */
export function surchargeUpdate(data: Partial<SurchargeModel>) {
  return requestClient.putWithMsg<void>(Api.root, data);
}

/**
 * 附加费删除
 * @param id 附加费ID
 * @returns void
 */
export function surchargeRemove(id: ID) {
  return requestClient.deleteWithMsg<void>(`${Api.root}/${id}`);
}
