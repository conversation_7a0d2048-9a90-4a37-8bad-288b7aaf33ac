import type { PageQuery } from '#/api/common'; // 假设 ID, IDS 在 PageQuery 或其他地方引入
import type {
  LadderPriceForm,
  LadderPriceQuery,
  LadderPriceRes,
} from '#/api/waterfee/price/ladderPrice/model.d';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

// 从后端控制器 WaterfeePriceConfigController.java 推断 API 路径
enum Api {
  export = '/waterfee/priceConfig/export', // 导出路径
  list = '/waterfee/priceConfig/list',
  // 注意：后端 @RequestMapping 是 "/priceConfig"，需要确认是否需要模块前缀如 /waterfee
  // 假设需要 /waterfee 前缀
  root = '/waterfee/priceConfig',
}

/**
 * 阶梯价格导出
 * @param data 查询参数，与列表查询一致
 * @returns void
 */
export function ladderPriceExport(data: Partial<LadderPriceQuery>) {
  // 注意：后端导出方法接受 Bo 对象，前端查询对象可能需要调整以匹配
  return commonExport(Api.export, data);
}

/**
 * 查询阶梯价格配置列表
 * @param params 查询参数
 * @returns 阶梯价格配置列表
 */
export function listLadderPrice(params?: LadderPriceQuery & PageQuery) {
  // 后端 list 方法接受 Bo 和 PageQuery
  return requestClient.get<LadderPriceRes>(Api.list, { params }); // 假设返回 LadderPriceVO 数组
}

/**
 * 查询阶梯价格配置详细
 * @param id 阶梯价格配置ID
 * @returns 阶梯价格配置信息
 */
export function getLadderPrice(id: number | string) {
  // 后端 getInfo 方法路径为 /{id}
  return requestClient.get<LadderPriceForm>(`${Api.root}/${id}`); // 假设返回 LadderPriceForm 用于编辑
}

/**
 * 新增阶梯价格配置
 * @param data 新增数据 (LadderPriceForm)
 * @returns void
 */
export function addLadderPrice(data: LadderPriceForm) {
  // 后端 add 方法接受 Bo 对象
  return requestClient.postWithMsg<void>(Api.root, data);
}

/**
 * 修改阶梯价格配置
 * @param data 修改数据 (LadderPriceForm)
 * @returns void
 */
export function updateLadderPrice(data: LadderPriceForm) {
  // 后端 edit 方法接受 Bo 对象
  return requestClient.putWithMsg<void>(Api.root, data);
}

/**
 * 删除阶梯价格配置
 * @param id 阶梯价格配置ID或ID数组
 * @returns void
 */
export function deleteLadderPrice(
  ids: Array<number | string> | number | string,
) {
  // 后端 remove 方法路径为 /{ids}，接受 Long[]
  // 如果传递数组，需要确保 requestClient 能正确处理
  const idString = Array.isArray(ids) ? ids.join(',') : ids; // 将数组转为逗号分隔字符串，如果需要
  return requestClient.deleteWithMsg<void>(`${Api.root}/${idString}`);
}
