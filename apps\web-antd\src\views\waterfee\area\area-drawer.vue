<script setup lang="ts">
import type { Area } from '#/api/waterfee/area/model';

import { computed, ref } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { addFullName, cloneDeep, listToTree } from '@vben/utils';

import { useVbenForm } from '#/adapter/form';
import {
  areaAdd,
  areaInfo,
  areaList,
  areaNodeList,
  areaUpdate,
} from '#/api/waterfee/area';
// import { listUserByAreaId } from '#/api/waterfee/user';

import { drawerSchema } from './data';

const emit = defineEmits<{ reload: [] }>();

interface DrawerProps {
  id?: number | string;
  update: boolean;
}

const isUpdate = ref(false);
const title = computed(() => {
  return isUpdate.value ? $t('pages.common.edit') : $t('pages.common.add');
});

const [BasicForm, formApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'col-span-2',
    labelWidth: 80,
  },
  schema: drawerSchema(),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-2',
});

async function getAreaTree(areaId?: number | string, exclude = false) {
  let ret: Area[] = [];
  ret = await (!areaId || exclude ? areaList({}) : areaNodeList(areaId));
  const treeData = listToTree(ret, { id: 'areaId', pid: 'parentId' });
  // 添加营业区域名称 如 xx-xx-xx
  addFullName(treeData, 'areaName', ' / ');
  return treeData;
}

async function initAreaSelect(areaId?: number | string) {
  // 需要动态更新TreeSelect组件 这里允许为空
  const treeData = await getAreaTree(areaId, !isUpdate.value);
  formApi.updateSchema([
    {
      componentProps: {
        fieldNames: { label: 'areaName', value: 'areaId' },
        showSearch: true,
        treeData,
        treeDefaultExpandAll: true,
        treeLine: { showLeafIcon: false },
        // 选中后显示在输入框的值
        treeNodeLabelProp: 'fullName',
      },
      fieldName: 'parentId',
    },
  ]);
}

/**
 * 营业区域管理员下拉框 更新时才会enable
 * @param areaId
 */
// async function initAreaUsers(areaId: number | string) {
//   const ret = await listUserByAreaId(areaId);
//   const options = ret.map((user) => ({
//     label: `${user.userName} | ${user.nickName}`,
//     value: user.userId,
//   }));
//   formApi.updateSchema([
//     {
//       componentProps: {
//         disabled: ret.length === 0,
//         options,
//         placeholder: ret.length === 0 ? '该营业区域暂无用户' : '请选择营业区域负责人',
//       },
//       fieldName: 'leader',
//     },
//   ]);
// }

async function setLeaderOptions() {
  formApi.updateSchema([
    {
      componentProps: {
        disabled: true,
        options: [],
        placeholder: '仅在更新时可选营业区域负责人',
      },
      fieldName: 'leader',
    },
  ]);
}

const [BasicDrawer, drawerApi] = useVbenDrawer({
  onCancel: handleCancel,
  onConfirm: handleConfirm,
  async onOpenChange(isOpen) {
    if (!isOpen) {
      return null;
    }
    drawerApi.drawerLoading(true);

    const { id, update } = drawerApi.getData() as DrawerProps;
    isUpdate.value = update;

    if (id) {
      await formApi.setFieldValue('parentId', id);
      if (update) {
        const record = await areaInfo(id);
        await formApi.setValues(record);
      }
    }

    // await (update && id ? initAreaUsers(id) : setLeaderOptions());
    /** 营业区域选择 下拉框 */
    await initAreaSelect(id);

    drawerApi.drawerLoading(false);
  },
});

async function handleConfirm() {
  try {
    drawerApi.drawerLoading(true);
    const { valid } = await formApi.validate();
    if (!valid) {
      return;
    }
    const data = cloneDeep(await formApi.getValues());
    await (isUpdate.value ? areaUpdate(data) : areaAdd(data));
    emit('reload');
    await handleCancel();
  } catch (error) {
    console.error(error);
  } finally {
    drawerApi.drawerLoading(false);
  }
}

async function handleCancel() {
  drawerApi.close();
  await formApi.resetForm();
}
</script>

<template>
  <BasicDrawer :close-on-click-modal="false" :title="title" class="w-[600px]">
    <BasicForm />
  </BasicDrawer>
</template>
