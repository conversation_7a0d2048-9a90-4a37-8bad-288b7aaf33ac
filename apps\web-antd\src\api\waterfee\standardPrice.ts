import type {
  StandardPriceListGetResultModel,
  StandardPriceModel,
  StandardPriceParams,
} from './model/standardPriceModel';

import type { ID } from '#/api/common';

import { requestClient } from '#/api/request';

enum Api {
  root = '/waterfee/standardPrice',
  standardPriceList = '/waterfee/standardPrice/list',
}

/**
 * 标准价格列表
 * @param params 查询参数
 * @returns 标准价格列表
 */
export function standardPriceList(params?: StandardPriceParams) {
  return requestClient.get<StandardPriceListGetResultModel>(
    Api.standardPriceList,
    { params },
  );
}

/**
 * 标准价格详情
 * @param id 标准价格ID
 * @returns 标准价格详情
 */
export function standardPriceInfo(id: ID) {
  return requestClient.get<StandardPriceModel>(`${Api.root}/${id}`);
}

/**
 * 标准价格新增
 * @param data 参数
 */
export function standardPriceAdd(data: Partial<StandardPriceModel>) {
  return requestClient.postWithMsg<void>(Api.root, data);
}

/**
 * 标准价格更新
 * @param data 参数
 */
export function standardPriceUpdate(data: Partial<StandardPriceModel>) {
  return requestClient.putWithMsg<void>(Api.root, data);
}

/**
 * 标准价格删除
 * @param id 标准价格ID
 * @returns void
 */
export function standardPriceRemove(id: ID) {
  return requestClient.deleteWithMsg<void>(`${Api.root}/${id}`);
}
