<script setup lang="ts">
import type { Area } from '#/api/waterfee/area/model';
import type { CommunityModel } from '#/api/waterfee/model/communityModel';

import { computed, ref } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';
import { addFullName, cloneDeep, listToTree } from '@vben/utils';

import { useVbenForm } from '#/adapter/form';
import { areaNodeList, areaOptions } from '#/api/waterfee/area';
import {
  communityAdd,
  communityInfo,
  communityUpdate,
} from '#/api/waterfee/community';

import { formSchema } from './community.data';

const emit = defineEmits<{ reload: [] }>();

const isUpdate = ref(false);
const title = computed(() => {
  return isUpdate.value ? '编辑小区' : '新增小区';
});

const [BasicForm, formApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'col-span-2',
    labelWidth: 100,
  },
  schema: formSchema(),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-2',
});

/**
 * 获取区域树状数据
 * @param areaId 区域ID
 * @param exclude 是否排除当前区域
 */
async function getAreaTree(areaId?: number | string, exclude = false) {
  let ret: Area[] = [];
  ret = await (!areaId || exclude ? areaOptions({}) : areaNodeList(areaId));
  const treeData = listToTree(ret, { id: 'areaId', pid: 'parentId' });
  // 添加营业区域名称 如 xx-xx-xx
  addFullName(treeData, 'areaName', ' / ');

  // 递归设置节点的可选状态
  const setSelectable = (nodes: any[]) => {
    nodes.forEach((node) => {
      // 如果有子节点，则设置为不可选
      node.selectable = !node.children?.length;
      if (node.children?.length) {
        setSelectable(node.children);
      }
    });
  };

  setSelectable(treeData);
  return treeData;
}

// 获取抄表员列表
async function loadAreaOptions() {
  try {
    const areaData = await getAreaTree();

    formApi.updateSchema([
      {
        componentProps: {
          treeData: areaData,
        },
        fieldName: 'areaId',
      },
    ]);
  } catch (error) {
    console.error('Error loading area:', error);
  }
}

const [BasicDrawer, drawerApi] = useVbenDrawer({
  onCancel: handleCancel,
  onConfirm: handleSubmit,
  async onOpenChange(isOpen) {
    if (!isOpen) {
      return null;
    }
    drawerApi.drawerLoading(true);

    try {
      const { id } = drawerApi.getData() as { id?: number | string };
      isUpdate.value = !!id;

      await loadAreaOptions();

      if (isUpdate.value && id) {
        const record = await communityInfo(id);
        await formApi.setValues(record);
      } else {
        await formApi.resetForm();
      }
    } catch (error) {
      console.error('Error in drawer open:', error);
    } finally {
      drawerApi.drawerLoading(false);
    }
  },
});

async function handleSubmit() {
  try {
    drawerApi.drawerLoading(true);
    const { valid } = await formApi.validate();
    if (!valid) {
      return;
    }
    const values = cloneDeep(await formApi.getValues());

    // 过滤不必要的字段，防止请求过长
    const necessaryFields = [
      'id',
      'communityNo',
      'communityName',
      'areaId',
      'address',
      'description',
    ];

    const filteredValues: Partial<CommunityModel> = {};
    necessaryFields.forEach((field) => {
      if (values[field as keyof typeof values] !== undefined) {
        filteredValues[field as keyof CommunityModel] =
          values[field as keyof typeof values];
      }
    });

    console.log('表单提交数据:', filteredValues);

    await (isUpdate.value
      ? communityUpdate(filteredValues)
      : communityAdd(filteredValues));

    emit('reload');
    await handleCancel();
  } catch (error) {
    console.error('表单提交错误:', error);
  } finally {
    drawerApi.drawerLoading(false);
  }
}

async function handleCancel() {
  drawerApi.close();
  await formApi.resetForm();
}
</script>

<template>
  <BasicDrawer :close-on-click-modal="false" :title="title" class="w-[600px]">
    <BasicForm />
  </BasicDrawer>
</template>
