<script setup lang="ts">
// import type { LeaveVO } from './api/model';
// import type { UserVO } from '#/api/waterfee/user/archivesManage/model';
import type { DescItem } from '#/components/description';

import { computed } from 'vue';

import { Description, useDescription } from '#/components/description';
import { renderDict } from '#/utils/render';

import { flowSchema, meterDescSchema, priceDescSchema } from './data';

// import dayjs from 'dayjs';

defineOptions({
  name: 'UserDescription',
  inheritAttrs: false,
});

const props = defineProps<{ data: any }>();

// 获取字典名称
const getDictName = (fieldName: string) => {
  const dictMap: Record<string, string> = {
    customerNature: 'waterfee_user_customer_nature',
    useWaterNature: 'waterfee_user_use_water_nature',
    userStatus: 'waterfee_user_user_status',
    auditStatus: 'audit_status',
    certificateType: 'waterfee_user_certificate_type',
    invoiceType: 'waterfee_user_invoice_type',
    billingMethod: 'waterfee_user_billing_method',
    ifPenalty: 'yes_no',
    ifExtraCharge: 'yes_no',
    penaltyType: 'waterfee_user_penalty_type',
    priceUseWaterNature: 'waterfee_user_use_water_nature',
  };
  return dictMap[fieldName] || '';
};

// 构建描述项
const descSchema = computed<DescItem[]>(() => {
  return flowSchema()
    .filter((item) => item.fieldName !== 'userId') // 过滤掉不需要显示的字段
    .map((item) => {
      return {
        field: item.fieldName,
        label: item.label as string,
        span: 1,
        render: (val: any) => {
          const dictName = getDictName(item.fieldName);
          if (dictName) {
            return renderDict(val, dictName);
          }
          if (item.fieldName === 'supplyDate' && val) {
            return val;
          }
          return val || '-';
        },
      } satisfies DescItem;
    });
});

// 使用Description组件
const [registerDescription] = useDescription({
  column: 2,
  bordered: true,
  labelStyle: {
    fontWeight: 'bold',
    minWidth: '120px',
  },
  schema: descSchema.value,
  data: props.data.userBasicInfo,
});
const [meterDescription] = useDescription({
  column: 2,
  bordered: true,
  labelStyle: {
    fontWeight: 'bold',
    minWidth: '120px',
  },
  schema: meterDescSchema,
  data: props.data.waterfeeMeterBo,
});
const [priceDescription] = useDescription({
  column: 2,
  bordered: true,
  labelStyle: {
    fontWeight: 'bold',
    minWidth: '120px',
  },
  schema: priceDescSchema,
  data: {
    ...props.data.userPrice,
    extraChargeType: props.data.userPrice.extraChargeType?.split(','),
  },
});
</script>

<template>
  <div>
    <h3>基本信息</h3>
    <Description @register="registerDescription" />

    <h3 class="mt-4">水表信息</h3>
    <Description @register="meterDescription" />

    <h3 class="mt-4">价格信息</h3>
    <Description @register="priceDescription" />
  </div>
</template>

<style scoped>
h3 {
  padding-left: 8px;
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: bold;
  border-left: 3px solid #1890ff;
}
</style>
