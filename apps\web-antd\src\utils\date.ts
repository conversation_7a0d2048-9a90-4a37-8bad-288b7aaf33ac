import dayjs from 'dayjs';

/**
 * 格式化日期字符串
 * @param time 日期字符串或时间戳
 * @param format 格式化模式
 * @returns 格式化后的日期字符串
 */
export function formatDate(time: number | string, format = 'YYYY-MM-DD') {
  try {
    const date = dayjs(time);
    if (!date.isValid()) {
      throw new Error('Invalid date');
    }
    return date.format(format);
  } catch (error) {
    console.error(`Error formatting date: ${error}`);
    return time;
  }
}

/**
 * 格式化日期时间字符串（含时分秒）
 * @param time 日期字符串或时间戳
 * @returns 格式化后的日期时间字符串 YYYY-MM-DD HH:mm:ss
 */
export function formatDateTime(time: number | string) {
  return formatDate(time, 'YYYY-MM-DD HH:mm:ss');
}

/**
 * 判断值是否为 Date 对象
 * @param value 要检查的值
 * @returns 是否为 Date 对象
 */
export function isDate(value: any): value is Date {
  return value instanceof Date;
}

/**
 * 判断值是否为 dayjs 对象
 * @param value 要检查的值
 * @returns 是否为 dayjs 对象
 */
export function isDayjsObject(value: any): value is dayjs.Dayjs {
  return dayjs.isDayjs(value);
}
