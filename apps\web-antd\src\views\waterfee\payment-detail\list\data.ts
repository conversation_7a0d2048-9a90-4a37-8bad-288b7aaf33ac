import type { VxeGridProps } from 'vxe-table';
import type { FormSchemaGetter } from '#/adapter/form';
import type { VbenFormSchema } from '@vben/vbencomponents';
import { getDictOptions } from '#/utils/dict';
import { optionsToEnum } from '@vben/utils';
import { renderDict } from '#/utils/render';

// 使用 getDictOptions 函数从 utils/dict 中获取字典选项

/**
 * @description 表格列
 */
export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 50, fixed: 'left' },
  { type: 'seq', width: 60, title: '序号', fixed: 'left' },
  {
    field: 'paymentDetailId',
    title: '缴费明细ID',
    minWidth: 180,
    visible: false, // 隐藏该列
  },
  {
    field: 'billId',
    title: '关联账单ID',
    minWidth: 180,
    visible: false, // 隐藏该列
  },
  {
    field: 'userNo', // 用户编号
    title: '用户编号',
    minWidth: 120,
  },
  {
    field: 'userName', // 用户名称
    title: '用户名',
    minWidth: 120,
    align: 'center', // 居中对齐
  },
  {
    field: 'paymentAmount',
    title: '缴费金额',
    minWidth: 120,
    align: 'center', // 居中对齐
    formatter: ({ cellValue }) => cellValue != null ? `${cellValue}` : '0.00',
  },
  {
    field: 'paymentMethod',
    title: '缴费方式',
    minWidth: 120,
    align: 'center', // 居中对齐
    cellRender: {
      name: 'VbenDictLabel',
      props: {
        dictType: 'waterfee_payment_method',
      },
    },
  },
  {
    field: 'paymentTime',
    title: '缴费时间',
    minWidth: 160,
    align: 'center', // 居中对齐
    formatter: 'formatDateTime',
  },
  {
    field: 'createBy',
    title: '收款人',
    minWidth: 120,
    align: 'center', // 居中对齐
  },
  {
    field: 'balanceDue',
    title: '未缴余额',
    minWidth: 120,
    align: 'center', // 居中对齐
    formatter: ({ cellValue }) => cellValue != null ? `${cellValue}` : '0.00',
  },
  {
    title: '操作',
    width: 100,
    align: 'center', // 居中对齐
    slots: { default: 'action' },
    fixed: 'right',
  },
];

/**
 * @description 查询表单
 */
export const querySchema: FormSchemaGetter = () => [
    {
      fieldName: 'userNo',
      label: '用户编号',
      component: 'Input',
      componentProps: {
        placeholder: '请输入用户编号',
      },
    },
    {
        fieldName: 'userName',
      label: '用户名',
      component: 'Input',
      componentProps: {
        placeholder: '请输入用户名',
      },
    },

    {
        fieldName: 'paymentMethod',
      label: '缴费方式',
      component: 'Select',
      componentProps: {
        placeholder: '请选择缴费方式',
        options: getDictOptions('waterfee_payment_method'),
      },
    },

    {
        fieldName: 'paymentTimeRange',
      label: '缴费时间',
      component: 'RangePicker',
      componentProps: {
        valueFormat: 'YYYY-MM-DD HH:mm:ss',
        placeholder: ['开始时间', '结束时间'],
      },
    },
  ];