<script setup lang="ts">
import { ref, onMounted, watch, onUnmounted } from 'vue';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { Button, Space, message } from 'ant-design-vue';
import { Page } from '@vben/common-ui';
import { EditOutlined } from '@ant-design/icons-vue';
import { getMeterAdjustmentList } from '#/api/waterfee/meterAdjustment';
import { columns, querySchema } from './adjustment.data';
import { preserveBigInt } from '#/utils/json-bigint';
import { initAllOptions, businessAreaOptions, meterBookOptions } from './utils/options';
import { areaList } from '#/api/waterfee/area';
import { meterBookList } from '#/api/waterfee/meterbook';
import { listUser } from '#/api/waterfee/user/archivesManage';
import eventBus, { EventType } from './utils/eventBus';

// 引入组件
import MeterAdjustmentDrawer from './components/MeterAdjustmentDrawer.vue';

// 表单配置
const formOptions = {
  commonConfig: {
    labelWidth: 80,
    componentProps: {
      allowClear: true,
    },
  },
  schema: querySchema(),
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
  submitOnChange: true, // 表单值变化时自动提交
};

// 当前选择的表册ID
const currentBookId = ref('');
// 当前选择的区域ID
const currentAreaId = ref('');

// 表格配置
const gridOptions = {
  columns,
  height: 'auto',
  width: '100%',
  fit: true,
  autoResize: true,
  tableLayout: 'auto',
  scrollX: {
    enabled: true,
    gt: 10,
  },
  resizable: true,
  resizeConfig: {
    minWidth: 80,
  },
  tooltipConfig: {
    showAll: true,
  },
  rowConfig: {
    isHover: true,
    // 设置行索引起始值为0，这样序号列显示的值就是从1开始
    // 因为我们在序号列中使用了 rowIndex + 1
    indexMethod: (row) => row._XID,
  },
  checkboxConfig: {
    // 高亮
    highlight: true,
    // 翻页时保留选中状态
    reserve: true,
  },
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues = {}) => {
        // 获取表册ID
        const bookId = formValues.bookId;
        if (!bookId) {
          return { rows: [], total: 0 };
        }

        // 保存当前表册ID
        currentBookId.value = bookId;

        try {
          // 查询数据
          const resp = await getMeterAdjustmentList(bookId, {
            pageNum: page?.currentPage,
            pageSize: page?.pageSize,
          });

          console.log('获取到的数据:', resp);

          // 检查响应数据格式
          if (resp && resp.rows) {
            // 标准格式，处理数据
            resp.rows = await Promise.all(resp.rows.map(async item => {
              // 处理营业区域
              let businessAreaName = item.businessAreaName;
              if (!businessAreaName && item.businessAreaId) {
                try {
                  // 从选项中查找区域名称
                  const findAreaName = (options, id) => {
                    for (const option of options) {
                      if (option.value === id) {
                        return option.label;
                      }
                      if (option.children && option.children.length > 0) {
                        const found = findAreaName(option.children, id);
                        if (found) return found;
                      }
                    }
                    return null;
                  };

                  // 从缓存的选项中查找
                  const areaName = findAreaName(businessAreaOptions.value, item.businessAreaId);
                  if (areaName) {
                    businessAreaName = areaName;
                  } else {
                    // 如果在选项中找不到，尝试从API获取
                    const areaInfo = await areaList();
                    const area = areaInfo.find(a => a.areaId === item.businessAreaId);
                    if (area) {
                      businessAreaName = area.areaName;
                    }
                  }
                } catch (error) {
                  console.error('获取营业区域名称失败:', error);
                }
              }

              // 处理抄表手册
              let meterBookName = item.meterBookName;
              if (!meterBookName && item.meterBookId) {
                try {
                  // 从选项中查找表册名称
                  const bookOption = meterBookOptions.value.find(option => option.value === item.meterBookId);
                  if (bookOption) {
                    meterBookName = bookOption.label;
                  } else {
                    // 如果在选项中找不到，尝试从API获取
                    const bookInfo = await meterBookList();
                    const book = bookInfo.rows.find(b => b.id === item.meterBookId);
                    if (book) {
                      meterBookName = book.bookName;
                    }
                  }
                } catch (error) {
                  console.error('获取抄表手册名称失败:', error);
                }
              }

              // 处理用户名
              let userName = item.userName;
              if (!userName && item.userId) {
                try {
                  // 从 API 获取用户名
                  const userInfo = await listUser();
                  const user = userInfo.rows.find(u => u.userId === item.userId);
                  if (user) {
                    userName = user.userName;
                  }
                } catch (error) {
                  console.error('获取用户名失败:', error);
                }
              }

              return {
                ...item,
                // 确保这些字段存在，如果不存在则提供默认值
                businessAreaName: businessAreaName || '未知区域',
                meterBookName: meterBookName || '未知表册',
                userName: userName || '未知用户',
                userNo: item.userNo || '未知编号',
                communityName: item.communityName || '未知小区',
                waterNature: item.waterNature || '未知',
              };
            }));
            return resp;
          } else if (resp && Array.isArray(resp)) {
            // 如果响应是数组，转换为标准格式
            console.log('响应是数组，转换为标准格式');
            const processedRows = await Promise.all(resp.map(async item => {
              // 处理营业区域
              let businessAreaName = item.businessAreaName;
              if (!businessAreaName && item.businessAreaId) {
                try {
                  // 从选项中查找区域名称
                  const findAreaName = (options, id) => {
                    for (const option of options) {
                      if (option.value === id) {
                        return option.label;
                      }
                      if (option.children && option.children.length > 0) {
                        const found = findAreaName(option.children, id);
                        if (found) return found;
                      }
                    }
                    return null;
                  };

                  // 从缓存的选项中查找
                  const areaName = findAreaName(businessAreaOptions.value, item.businessAreaId);
                  if (areaName) {
                    businessAreaName = areaName;
                  } else {
                    // 如果在选项中找不到，尝试从API获取
                    const areaInfo = await areaList();
                    const area = areaInfo.find(a => a.areaId === item.businessAreaId);
                    if (area) {
                      businessAreaName = area.areaName;
                    }
                  }
                } catch (error) {
                  console.error('获取营业区域名称失败:', error);
                }
              }

              // 处理抄表手册
              let meterBookName = item.meterBookName;
              if (!meterBookName && item.meterBookId) {
                try {
                  // 从选项中查找表册名称
                  const bookOption = meterBookOptions.value.find(option => option.value === item.meterBookId);
                  if (bookOption) {
                    meterBookName = bookOption.label;
                  } else {
                    // 如果在选项中找不到，尝试从API获取
                    const bookInfo = await meterBookList();
                    const book = bookInfo.rows.find(b => b.id === item.meterBookId);
                    if (book) {
                      meterBookName = book.bookName;
                    }
                  }
                } catch (error) {
                  console.error('获取抄表手册名称失败:', error);
                }
              }

              // 处理用户名
              let userName = item.userName;
              if (!userName && item.userId) {
                try {
                  // 从 API 获取用户名
                  const userInfo = await listUser();
                  const user = userInfo.rows.find(u => u.userId === item.userId);
                  if (user) {
                    userName = user.userName;
                  }
                } catch (error) {
                  console.error('获取用户名失败:', error);
                }
              }

              return {
                ...item,
                // 确保这些字段存在，如果不存在则提供默认值
                businessAreaName: businessAreaName || '未知区域',
                meterBookName: meterBookName || '未知表册',
                userName: userName || '未知用户',
                userNo: item.userNo || '未知编号',
                communityName: item.communityName || '未知小区',
                waterNature: item.waterNature || '未知',
              };
            }));

            return {
              rows: processedRows,
              total: resp.length
            };
          } else if (resp && resp.code === 200 && resp.data) {
            // 如果响应是 { code: 200, data: [...] } 格式
            console.log('响应是 { code: 200, data: [...] } 格式，转换为标准格式');
            const data = Array.isArray(resp.data) ? resp.data : [resp.data];
            const processedData = await Promise.all(data.map(async item => {
              // 处理营业区域
              let businessAreaName = item.businessAreaName;
              if (!businessAreaName && item.businessAreaId) {
                try {
                  // 从选项中查找区域名称
                  const findAreaName = (options, id) => {
                    for (const option of options) {
                      if (option.value === id) {
                        return option.label;
                      }
                      if (option.children && option.children.length > 0) {
                        const found = findAreaName(option.children, id);
                        if (found) return found;
                      }
                    }
                    return null;
                  };

                  // 从缓存的选项中查找
                  const areaName = findAreaName(businessAreaOptions.value, item.businessAreaId);
                  if (areaName) {
                    businessAreaName = areaName;
                  } else {
                    // 如果在选项中找不到，尝试从API获取
                    const areaInfo = await areaList();
                    const area = areaInfo.find(a => a.areaId === item.businessAreaId);
                    if (area) {
                      businessAreaName = area.areaName;
                    }
                  }
                } catch (error) {
                  console.error('获取营业区域名称失败:', error);
                }
              }

              // 处理抄表手册
              let meterBookName = item.meterBookName;
              if (!meterBookName && item.meterBookId) {
                try {
                  // 从选项中查找表册名称
                  const bookOption = meterBookOptions.value.find(option => option.value === item.meterBookId);
                  if (bookOption) {
                    meterBookName = bookOption.label;
                  } else {
                    // 如果在选项中找不到，尝试从API获取
                    const bookInfo = await meterBookList();
                    const book = bookInfo.rows.find(b => b.id === item.meterBookId);
                    if (book) {
                      meterBookName = book.bookName;
                    }
                  }
                } catch (error) {
                  console.error('获取抄表手册名称失败:', error);
                }
              }

              // 处理用户名
              let userName = item.userName;
              if (!userName && item.userId) {
                try {
                  // 从 API 获取用户名
                  const userInfo = await listUser();
                  const user = userInfo.rows.find(u => u.userId === item.userId);
                  if (user) {
                    userName = user.userName;
                  }
                } catch (error) {
                  console.error('获取用户名失败:', error);
                }
              }

              return {
                ...item,
                // 确保这些字段存在，如果不存在则提供默认值
                businessAreaName: businessAreaName || '未知区域',
                meterBookName: meterBookName || '未知表册',
                userName: userName || '未知用户',
                userNo: item.userNo || '未知编号',
                communityName: item.communityName || '未知小区',
                waterNature: item.waterNature || '未知',
              };
            }));

            return {
              rows: processedData,
              total: data.length
            };
          }

          // 如果没有匹配的格式，返回原始响应
          return resp;
        } catch (error) {
          console.error('获取册本调整列表失败:', error);
          message.error(`获取数据失败：${error.message || '未知错误'}`);
          return { rows: [], total: 0 };
        }
      },
    },
  },
  id: 'waterfee-meter-adjustment-index',
};

// 初始化表格
const [BasicTable, tableApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

// 抽屉状态
const drawerOpen = ref(false);
const selectedMeterIds = ref([]);

// 批量调整水表所属区域和表册
function handleBatchAdjust() {
  try {
    // 获取选中的行
    const selectedRows = tableApi.grid.getCheckboxRecords();

    if (!selectedRows || selectedRows.length === 0) {
      message.warning('请选择要调整的水表');
      return;
    }

    console.log('选中的行:', selectedRows);

    // 确保ID不会丢失精度
    const meterIds = selectedRows.map(row => {
      // 检查行数据格式
      if (!row.meterId) {
        console.warn('行数据中没有 meterId 字段:', row);
        // 尝试从其他可能的字段中获取 ID
        const possibleIdFields = ['id', 'ID', 'meter_id', 'METER_ID'];
        for (const field of possibleIdFields) {
          if (row[field]) {
            console.log(`使用 ${field} 字段作为 meterId:`, row[field]);
            return String(row[field]);
          }
        }
        // 如果没有找到 ID，使用一个唯一标识符
        return String(Date.now() + Math.random());
      }

      const safeRecord = preserveBigInt(row);
      return String(safeRecord.meterId);
    });

    console.log('批量调整水表所属区域和表册, IDs:', meterIds);

    // 设置选中的水表IDs
    selectedMeterIds.value = meterIds;

    // 获取第一个选中行的区域ID
    if (selectedRows.length > 0 && selectedRows[0].businessAreaId) {
      currentAreaId.value = selectedRows[0].businessAreaId;
    } else {
      currentAreaId.value = '';
    }

    console.log('当前区域ID:', currentAreaId.value);
    console.log('当前表册ID:', currentBookId.value);

    // 打开抽屉
    drawerOpen.value = true;
  } catch (error) {
    console.error('批量调整水表所属区域和表册失败:', error);
    message.error(`操作失败：${error.message || '未知错误'}`);
  }
}

// 操作成功后刷新表格
function handleSuccess() {
  tableApi.query();
}

// 监听表单提交事件
function handleFormSubmit(formValues) {
  console.log('表单提交:', formValues);
  // 如果表册ID变化，重新查询数据
  if (formValues && formValues.bookId !== currentBookId.value) {
    tableApi.query();
  }
}

// 处理单个水表调整
function handleAdjustMeter(row) {
  console.log('处理单个水表调整:', row);

  // 确保ID不会丢失精度
  let meterId;
  if (!row.meterId) {
    console.warn('行数据中没有 meterId 字段:', row);
    // 尝试从其他可能的字段中获取 ID
    const possibleIdFields = ['id', 'ID', 'meter_id', 'METER_ID'];
    for (const field of possibleIdFields) {
      if (row[field]) {
        console.log(`使用 ${field} 字段作为 meterId:`, row[field]);
        meterId = String(row[field]);
        break;
      }
    }
    // 如果没有找到 ID，使用一个唯一标识符
    if (!meterId) {
      meterId = String(Date.now() + Math.random());
    }
  } else {
    const safeRecord = preserveBigInt(row);
    meterId = String(safeRecord.meterId);
  }

  // 设置选中的水表IDs
  selectedMeterIds.value = [meterId];

  // 获取区域ID
  if (row.businessAreaId) {
    currentAreaId.value = row.businessAreaId;
  } else {
    currentAreaId.value = '';
  }

  console.log('当前区域ID:', currentAreaId.value);
  console.log('当前表册ID:', currentBookId.value);

  // 打开抽屉
  drawerOpen.value = true;
}

// 组件挂载时初始化所有选项
onMounted(async () => {
  try {
    // 初始化所有选项
    await initAllOptions();
    console.log('册本调整页面选项初始化完成');

    // 添加事件监听器
    eventBus.on(EventType.ADJUST_METER, handleAdjustMeter);

    // 确保区域选项已加载
    if (businessAreaOptions.value.length === 0) {
      await loadBusinessAreaOptions();
      console.log('区域选项加载完成:', businessAreaOptions.value);
    }

    // 确保表册选项已加载
    if (meterBookOptions.value.length === 0) {
      await loadMeterBookOptions();
      console.log('表册选项加载完成:', meterBookOptions.value);
    }
  } catch (error) {
    console.error('初始化选项失败:', error);
    message.error('加载选项数据失败，请刷新页面重试');
  }
});

// 组件卸载时移除事件监听器
onUnmounted(() => {
  eventBus.off(EventType.ADJUST_METER, handleAdjustMeter);
});
</script>

<template>
  <Page :auto-content-height="true">
    <BasicTable
      @reload="tableApi.query()"
      @form-submit="handleFormSubmit"
      table-title="册本调整"
    >
      <template #toolbar-tools>
        <Space>
          <Button type="primary" @click="handleBatchAdjust">
            <template #icon><EditOutlined /></template>
            批量调整
          </Button>
        </Space>
      </template>
    </BasicTable>

    <!-- 册本调整抽屉 -->
    <MeterAdjustmentDrawer
      :open="drawerOpen"
      @update:open="drawerOpen = $event"
      :selectedMeterIds="selectedMeterIds"
      :currentBookId="currentBookId"
      :currentAreaId="currentAreaId"
      @reload="handleSuccess"
    />
  </Page>
</template>

<style scoped>
/* 自定义样式 */
</style>

