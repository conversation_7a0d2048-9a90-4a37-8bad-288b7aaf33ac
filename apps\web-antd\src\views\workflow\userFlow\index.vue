<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';

import type { VxeGridProps } from '#/adapter/vxe-table';
// import type { LeaveForm } from './api/model';
import type { UserVO } from '#/api/waterfee/user/archivesManage/model';

import { useRouter } from 'vue-router';

import { Page, useVbenModal } from '@vben/common-ui';
import { getVxePopupContainer } from '@vben/utils';

import { Modal, Popconfirm, Space } from 'ant-design-vue';

import { useVbenVxeGrid, vxeCheckboxChecked } from '#/adapter/vxe-table';
// import { leaveExport, leaveList, leaveRemove } from './api';
import {
  delUser,
  listUser,
  UserExport,
} from '#/api/waterfee/user/archivesManage';
import { cancelProcessApply } from '#/api/workflow/instance';
import { commonDownloadExcel } from '#/utils/file/download';

import { flowInfoModal } from '../components';
import { columns, querySchema } from './data';

const formOptions: VbenFormProps = {
  commonConfig: {
    labelWidth: 80,
    componentProps: {
      allowClear: true,
    },
  },
  schema: querySchema(),
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
};

const gridOptions: VxeGridProps = {
  checkboxConfig: {
    // 高亮
    highlight: true,
    // 翻页时保留选中状态
    reserve: true,
    // 点击行选中
    // trigger: 'row',
  },
  columns,
  height: 'auto',
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues = {}) => {
        return await listUser({
          pageNum: page.currentPage,
          pageSize: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  rowConfig: {
    keyField: 'userId',
  },
  // 表格全局唯一表示 保存列配置需要用到
  id: 'workflow-userFlow-index',
};

const [BasicTable, tableApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

const router = useRouter();
function handleAdd() {
  router.push('/register/user-flow/edit');
}

async function handleEdit(row: Required<UserVO>) {
  router.push({ path: '/register/user-flow/edit', query: { id: row.userId } });
}

async function handleDelete(row: Required<UserVO>) {
  await delUser([row.userId]);
  await tableApi.query();
}

async function handleRevoke(row: Required<UserVO>) {
  await cancelProcessApply({
    businessId: row.userId,
    message: '申请人撤销流程！',
  });
  await tableApi.query();
}

function handleMultiDelete() {
  const rows = tableApi.grid.getCheckboxRecords();
  const ids = rows.map((row: Required<UserVO>) => row.userId);
  Modal.confirm({
    title: '提示',
    okType: 'danger',
    content: `确认删除选中的${ids.length}条记录吗？`,
    onOk: async () => {
      await delUser(ids);
      await tableApi.query();
    },
  });
}

function handleDownloadExcel() {
  commonDownloadExcel(
    UserExport,
    '用水用户管理数据',
    tableApi.formApi.form.values,
  );
}
const [FlowInfoModal, flowInfoModalApi] = useVbenModal({
  connectedComponent: flowInfoModal,
});
function handleInfo(row: Required<UserVO>) {
  flowInfoModalApi.setData({ businessId: row.userId });
  flowInfoModalApi.open();
}
</script>

<template>
  <Page :auto-content-height="true">
    <BasicTable table-title="用水用户管理列表">
      <template #toolbar-tools>
        <Space>
          <a-button
            v-access:code="['workflow:leave:export']"
            @click="handleDownloadExcel"
          >
            {{ $t('pages.common.export') }}
          </a-button>
          <a-button
            :disabled="!vxeCheckboxChecked(tableApi)"
            danger
            type="primary"
            v-access:code="['workflow:leave:remove']"
            @click="handleMultiDelete"
          >
            {{ $t('pages.common.delete') }}
          </a-button>
          <a-button
            type="primary"
            v-access:code="['workflow:leave:add']"
            @click="handleAdd"
          >
            {{ $t('pages.common.add') }}
          </a-button>
        </Space>
      </template>
      <template #action="{ row }">
        <Space>
          <ghost-button
            v-if="['draft', 'cancel', 'back'].includes(row.auditStatus)"
            v-access:code="['workflow:leave:edit']"
            @click.stop="handleEdit(row)"
          >
            {{ $t('pages.common.edit') }}
          </ghost-button>
          <Popconfirm
            :get-popup-container="getVxePopupContainer"
            placement="left"
            title="确认撤销？"
            @confirm="handleRevoke(row)"
          >
            <ghost-button
              v-if="['waiting'].includes(row.auditStatus)"
              v-access:code="['workflow:leave:edit']"
              @click.stop=""
            >
              撤销
            </ghost-button>
          </Popconfirm>
          <ghost-button
            v-if="row.auditStatus !== 'draft'"
            @click="handleInfo(row)"
          >
            详情
          </ghost-button>
          <Popconfirm
            :get-popup-container="getVxePopupContainer"
            placement="left"
            title="确认删除？"
            @confirm="handleDelete(row)"
          >
            <ghost-button
              v-if="['draft', 'cancel', 'back'].includes(row.auditStatus)"
              danger
              v-access:code="['workflow:leave:remove']"
              @click.stop=""
            >
              {{ $t('pages.common.delete') }}
            </ghost-button>
          </Popconfirm>
        </Space>
      </template>
    </BasicTable>
    <FlowInfoModal />
  </Page>
</template>
