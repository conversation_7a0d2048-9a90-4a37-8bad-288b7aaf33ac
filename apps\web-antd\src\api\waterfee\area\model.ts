import type { BasicFetchResult, BasicPageParams } from '#/api/common';

// 检查区域模型是否存在，并修改ID字段类型
export interface Area {
  areaId?: string; // 区域ID，改为string类型
  areaName?: string; // 区域名称
  areaType?: string; // 区域类型
  parentId?: string; // 父级区域ID，改为string类型
  ancestors?: string; // 祖级列表
  orderNum?: number; // 显示顺序
  children?: Area[]; // 子区域数组
  createBy?: string;
  createTime?: string;
  updateBy?: string;
  updateTime?: string;
  remark?: string;
}
