<script setup lang="ts">
import type { SelectProps } from 'ant-design-vue';

import type { UserForm } from '#/api/waterfee/user/archivesManage/model.d';

import { computed, onMounted, reactive, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import {
  Button as AButton,
  Card as ACard,
  Col as ACol,
  DatePicker as ADatePicker,
  Form as AForm,
  FormItem as AFormItem,
  Input as AInput,
  InputNumber as AInputNumber,
  Row as ARow,
  Select as ASelect,
  message,
} from 'ant-design-vue';

import {
  addUser,
  getUser,
  updateUser,
} from '#/api/waterfee/user/archivesManage';
import { getDictOptions } from '#/utils/dict';

// 路由相关
const route = useRoute();
const router = useRouter();

// 表单状态
const isUpdate = ref(false);
const loading = ref(false);
const formRef = ref();
const title = computed(() => {
  return isUpdate.value ? '流程-编辑用水用户' : '流程-新增用水用户';
});

// 表单数据
const formData = reactive<UserForm>({
  userNo: '',
  customerNature: '',
  useWaterNature: '',
  useWaterNumber: undefined,
  userName: '',
  phoneNumber: '',
  certificateNumber: '',
  certificateType: '',
  userStatus: '',
  areaId: '',
  communityId: '',
  unitRoomNumber: '',
  address: '',
  email: '',
  taxpayerIdentificationNumber: '',
  supplyDate: '',
  invoiceName: '',
  invoiceType: '',
  // 默认审核状态为未审核
  auditStatus: '0',
});

// 字典选项
const customerNatureOptions = ref<SelectProps['options']>([]);
const useWaterNatureOptions = ref<SelectProps['options']>([]);
const certificateTypeOptions = ref<SelectProps['options']>([]);
const userStatusOptions = ref<SelectProps['options']>([]);
const invoiceTypeOptions = ref<SelectProps['options']>([]);

/**
 * 初始化表单数据
 */
const initData = async () => {
  const { id } = route.query;
  isUpdate.value = !!id;

  // 加载字典数据
  try {
    [
      customerNatureOptions.value,
      useWaterNatureOptions.value,
      certificateTypeOptions.value,
      userStatusOptions.value,
      invoiceTypeOptions.value,
    ] = await Promise.all([
      getDictOptions('waterfee_user_customer_nature'),
      getDictOptions('waterfee_user_use_water_nature'),
      getDictOptions('waterfee_user_certificate_type'),
      getDictOptions('waterfee_user_user_status'),
      getDictOptions('waterfee_user_invoice_type'),
    ]);
  } catch (error) {
    console.error('获取字典数据失败:', error);
    message.error('获取字典数据失败');
  }

  // 如果是编辑模式，加载用户数据
  if (isUpdate.value && id) {
    loading.value = true;
    try {
      const record = await getUser(id as string);
      console.log('获取到的用户数据:', record);

      // 处理数据，避免可能的特殊类型问题
      // 处理日期字段
      if (record.supplyDate) {
        formData.supplyDate = String(record.supplyDate);
      }

      // 处理数字字段
      if (record.useWaterNumber !== undefined) {
        formData.useWaterNumber = Number(record.useWaterNumber);
      }

      // 其他字段直接赋值
      formData.userId = record.userId;
      formData.userNo = record.userNo;
      formData.customerNature = record.customerNature;
      formData.useWaterNature = record.useWaterNature;
      formData.userName = record.userName;
      formData.phoneNumber = record.phoneNumber;
      formData.certificateType = record.certificateType;
      formData.certificateNumber = record.certificateNumber;
      formData.userStatus = record.userStatus;
      formData.areaId = record.areaId;
      formData.communityId = record.communityId;
      formData.unitRoomNumber = record.unitRoomNumber;
      formData.address = record.address;
      formData.email = record.email;
      formData.taxpayerIdentificationNumber =
        record.taxpayerIdentificationNumber;
      formData.invoiceName = record.invoiceName;
      formData.invoiceType = record.invoiceType;
      formData.auditStatus = record.auditStatus;
    } catch (error) {
      console.error('获取用户数据失败:', error);
      message.error('获取用户数据失败');
    } finally {
      loading.value = false;
    }
  }
};

/**
 * 表单提交处理
 */
const handleSubmit = async () => {
  try {
    // 表单验证
    // 手动验证必填字段
    if (!formData.customerNature) {
      message.error('请选择客户性质');
      return;
    }
    if (!formData.useWaterNature) {
      message.error('请选择用水性质');
      return;
    }
    if (!formData.useWaterNumber) {
      message.error('请输入用水人数');
      return;
    }
    if (!formData.userName) {
      message.error('请输入用户姓名');
      return;
    }
    if (!formData.phoneNumber) {
      message.error('请输入手机号码');
      return;
    }
    if (!formData.certificateType) {
      message.error('请选择证件类型');
      return;
    }
    if (!formData.certificateNumber) {
      message.error('请输入证件号码');
      return;
    }
    if (!formData.userStatus) {
      message.error('请选择用户状态');
      return;
    }
    if (!formData.areaId) {
      message.error('请输入营业区域');
      return;
    }
    if (!formData.communityId) {
      message.error('请输入小区名称');
      return;
    }
    if (!formData.unitRoomNumber) {
      message.error('请输入单元房号');
      return;
    }
    if (!formData.address) {
      message.error('请输入用水地址');
      return;
    }
    if (!formData.taxpayerIdentificationNumber) {
      message.error('请输入纳税人识别号');
      return;
    }
    if (!formData.supplyDate) {
      message.error('请选择供水日期');
      return;
    }

    loading.value = true;
    // 准备提交数据
    const submitData = { ...formData };

    // 设置默认审核状态
    if (!isUpdate.value) {
      submitData.auditStatus = '0';
    }

    // 提交数据
    await (isUpdate.value ? updateUser(submitData) : addUser(submitData));
    message.success(isUpdate.value ? '更新成功' : '添加成功');

    // 返回列表页
    router.back();
  } catch (error) {
    console.error('提交表单失败:', error);
    message.error('提交失败，请检查表单');
  } finally {
    loading.value = false;
  }
};

/**
 * 返回列表页
 */
const handleBack = () => {
  router.back();
};

onMounted(() => {
  // 初始化数据
  initData();
});
</script>

<template>
  <div class="p-4">
    <ACard :title="title" :loading="loading">
      <!-- 表单区域 -->
      <div class="form-container">
        <AForm
          ref="formRef"
          :model="formData"
          layout="horizontal"
          :label-col="{ span: 8 }"
          :wrapper-col="{ span: 16 }"
        >
          <ARow :gutter="24">
            <!-- 第一行 -->
            <ACol :span="8">
              <AFormItem label="用户编号" name="userNo">
                <AInput
                  v-model:value="formData.userNo"
                  placeholder="系统自动生成"
                  disabled
                />
              </AFormItem>
            </ACol>
            <ACol :span="8">
              <AFormItem label="客户性质" name="customerNature" required>
                <ASelect
                  v-model:value="formData.customerNature"
                  placeholder="请选择"
                  :options="customerNatureOptions"
                  allow-clear
                />
              </AFormItem>
            </ACol>
            <ACol :span="8">
              <AFormItem label="用水性质" name="useWaterNature" required>
                <ASelect
                  v-model:value="formData.useWaterNature"
                  placeholder="请选择"
                  :options="useWaterNatureOptions"
                  allow-clear
                />
              </AFormItem>
            </ACol>

            <!-- 第二行 -->
            <ACol :span="8">
              <AFormItem label="用水人数" name="useWaterNumber" required>
                <AInputNumber
                  v-model:value="formData.useWaterNumber"
                  placeholder="输入内容"
                  class="w-full"
                />
              </AFormItem>
            </ACol>
            <ACol :span="8">
              <AFormItem label="用户姓名" name="userName" required>
                <AInput
                  v-model:value="formData.userName"
                  placeholder="输入内容"
                />
              </AFormItem>
            </ACol>
            <ACol :span="8">
              <AFormItem label="手机号码" name="phoneNumber" required>
                <AInput
                  v-model:value="formData.phoneNumber"
                  placeholder="输入内容"
                />
              </AFormItem>
            </ACol>

            <!-- 第三行 -->
            <ACol :span="8">
              <AFormItem label="证件类型" name="certificateType" required>
                <ASelect
                  v-model:value="formData.certificateType"
                  placeholder="请选择"
                  :options="certificateTypeOptions"
                  allow-clear
                />
              </AFormItem>
            </ACol>
            <ACol :span="8">
              <AFormItem label="证件号码" name="certificateNumber" required>
                <AInput
                  v-model:value="formData.certificateNumber"
                  placeholder="输入内容"
                />
              </AFormItem>
            </ACol>
            <ACol :span="8">
              <AFormItem label="用户状态" name="userStatus" required>
                <ASelect
                  v-model:value="formData.userStatus"
                  placeholder="请选择"
                  :options="userStatusOptions"
                  allow-clear
                />
              </AFormItem>
            </ACol>

            <!-- 第四行 -->
            <ACol :span="8">
              <AFormItem label="营业区域" name="areaId" required>
                <AInput
                  v-model:value="formData.areaId"
                  placeholder="输入内容"
                />
              </AFormItem>
            </ACol>
            <ACol :span="8">
              <AFormItem label="小区名称" name="communityId" required>
                <AInput
                  v-model:value="formData.communityId"
                  placeholder="输入内容"
                />
              </AFormItem>
            </ACol>
            <ACol :span="8">
              <AFormItem label="单元房号" name="unitRoomNumber" required>
                <AInput
                  v-model:value="formData.unitRoomNumber"
                  placeholder="输入内容"
                />
              </AFormItem>
            </ACol>

            <!-- 第五行 -->
            <ACol :span="8">
              <AFormItem label="用水地址" name="address" required>
                <AInput
                  v-model:value="formData.address"
                  placeholder="输入内容"
                />
              </AFormItem>
            </ACol>
            <ACol :span="8">
              <AFormItem label="电子邮箱" name="email">
                <AInput v-model:value="formData.email" placeholder="输入内容" />
              </AFormItem>
            </ACol>
            <ACol :span="8">
              <AFormItem
                label="纳税人识别号"
                name="taxpayerIdentificationNumber"
                required
              >
                <AInput
                  v-model:value="formData.taxpayerIdentificationNumber"
                  placeholder="输入内容"
                />
              </AFormItem>
            </ACol>

            <!-- 第六行 -->
            <ACol :span="8">
              <AFormItem label="供水日期" name="supplyDate" required>
                <ADatePicker
                  v-model:value="formData.supplyDate"
                  placeholder="选择日期"
                  class="w-full"
                  value-format="YYYY-MM-DD"
                />
              </AFormItem>
            </ACol>
            <ACol :span="8">
              <AFormItem label="开票名称" name="invoiceName">
                <AInput
                  v-model:value="formData.invoiceName"
                  placeholder="输入内容"
                />
              </AFormItem>
            </ACol>
            <ACol :span="8">
              <AFormItem label="发票类型" name="invoiceType">
                <ASelect
                  v-model:value="formData.invoiceType"
                  placeholder="请选择"
                  :options="invoiceTypeOptions"
                  allow-clear
                />
              </AFormItem>
            </ACol>
          </ARow>
        </AForm>

        <!-- 操作按钮区域，放在表单内容下方 -->
        <div class="mt-4 flex justify-end">
          <AButton type="primary" @click="handleSubmit" :loading="loading">
            保存
          </AButton>
          <AButton class="ml-2" @click="handleBack">取消</AButton>
        </div>
      </div>
    </ACard>
  </div>
</template>

<style scoped>
/* 表单容器样式 */
.form-container {
  padding: 8px;
}

/* 表单项标签样式 */
:deep(.ant-form-item-label > label) {
  font-size: 14px;
  color: #666;
}

/* 表单项输入框样式 */
:deep(.ant-input),
:deep(.ant-select-selector),
:deep(.ant-picker) {
  border-radius: 2px;
}

/* 表单项间距 */
:deep(.ant-form-item) {
  margin-bottom: 16px;
}

/* 表单项必填标记颜色 */
:deep(.ant-form-item-required::before) {
  color: #ff4d4f;
}
</style>
