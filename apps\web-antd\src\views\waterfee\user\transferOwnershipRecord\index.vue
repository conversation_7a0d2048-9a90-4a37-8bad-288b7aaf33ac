<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';

import type { VxeGridProps } from '#/adapter/vxe-table';
import type { UserTransferOwnershipRecordVO } from '#/api/waterfee/user/transferOwnershipRecord/model.d';

import { Page, useVbenDrawer } from '@vben/common-ui';
import { getVxePopupContainer } from '@vben/utils';

import { Popconfirm, Space } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  delUserTransferOwnershipRecord,
  listUserTransferOwnershipRecord,
  UserTransferOwnershipRecordExport,
} from '#/api/waterfee/user/transferOwnershipRecord';
import { commonDownloadExcel } from '#/utils/file/download';

import { columns, querySchema } from './data';
import userTransferOwnershipRecordDrawer from './userTransferOwnershipRecord-drawer.vue';

const formOptions: VbenFormProps = {
  commonConfig: {
    labelWidth: 80,
    componentProps: {
      allowClear: true,
    },
  },
  schema: querySchema(),
  fieldMappingTime: [['timeRange', ['startTime', 'endTime'], 'YYYY-MM-DD']],
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
};

const gridOptions: VxeGridProps = {
  checkboxConfig: {
    // 高亮
    highlight: true,
    // 翻页时保留选中状态
    reserve: true,
  },
  columns,
  height: 'auto',
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues = {}) => {
        return await listUserTransferOwnershipRecord({
          pageNum: page.currentPage,
          pageSize: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  rowConfig: {
    keyField: 'transferId',
  },
  id: 'waterfee-userTransferOwnershipRecord-index',
};

const [BasicTable, tableApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

const [UserTransferOwnershipRecordDrawer, drawerApi] = useVbenDrawer({
  connectedComponent: userTransferOwnershipRecordDrawer,
});

// function handleAdd() {
//   drawerApi.setData({});
//   drawerApi.open();
// }

async function handleEdit(record: UserTransferOwnershipRecordVO) {
  drawerApi.setData({ id: record.transferId });
  drawerApi.open();
}

async function handleDelete(row: UserTransferOwnershipRecordVO) {
  await delUserTransferOwnershipRecord([row.transferId]);
  await tableApi.query();
}

// function handleMultiDelete() {
//   const rows = tableApi.grid.getCheckboxRecords();
//   const ids = rows.map((row: UserTransferOwnershipRecordVO) => row.transferId);
//   Modal.confirm({
//     title: '提示',
//     okType: 'danger',
//     content: `确认删除选中的${ids.length}条记录吗？`,
//     onOk: async () => {
//       await delUserTransferOwnershipRecord(ids);
//       await tableApi.query();
//     },
//   });
// }

function handleDownloadExcel() {
  commonDownloadExcel(
    UserTransferOwnershipRecordExport,
    '用水用户过户记录数据',
    tableApi.formApi.form.values,
  );
}
</script>

<template>
  <Page :auto-content-height="true">
    <BasicTable table-title="过户记录">
      <template #toolbar-tools>
        <Space>
          <!-- <a-button
            :disabled="!vxeCheckboxChecked(tableApi)"
            danger
            type="primary"
            v-access:code="['waterfee:userTransferOwnershipRecord:remove']"
            @click="handleMultiDelete"
          >
            {{ $t('pages.common.delete') }}
          </a-button>
          <a-button
            type="primary"
            v-access:code="['waterfee:userTransferOwnershipRecord:add']"
            @click="handleAdd"
          >
            {{ $t('pages.common.add') }}
          </a-button> -->
          <a-button
            type="primary"
            v-access:code="['waterfee:userTransferOwnershipRecord:export']"
            @click="handleDownloadExcel"
          >
            {{ $t('pages.common.export') }}
          </a-button>
        </Space>
      </template>
      <template #action="{ row }">
        <Space>
          <ghost-button
            v-access:code="['waterfee:userTransferOwnershipRecord:edit']"
            @click="handleEdit(row)"
          >
            {{ $t('pages.common.edit') }}
          </ghost-button>
          <Popconfirm
            :get-popup-container="getVxePopupContainer"
            placement="left"
            title="确认删除？"
            @confirm="handleDelete(row)"
          >
            <ghost-button
              danger
              v-access:code="['waterfee:userTransferOwnershipRecord:remove']"
              @click.stop=""
            >
              {{ $t('pages.common.delete') }}
            </ghost-button>
          </Popconfirm>
        </Space>
      </template>
    </BasicTable>
    <UserTransferOwnershipRecordDrawer @reload="tableApi.query()" />
  </Page>
</template>
