import type { ID, PageQuery, PageResult } from '#/api/common';

/**
 * 小区实体模型
 */
export interface CommunityModel {
  /** 小区ID */
  id: ID;
  /** 小区编号 */
  communityNo: string;
  /** 小区名称 */
  communityName: string;
  /** 所属区域ID */
  areaId: ID;
  /** 小区地址 */
  address: string;
  /** 小区描述 */
  description?: string;
  /** 创建时间 */
  createTime?: string;
  /** 创建人 */
  createBy?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 更新人 */
  updateBy?: string;
}

/**
 * 小区查询参数
 */
export interface CommunityParams extends PageQuery {
  /** 小区编号 */
  communityNo?: string;
  /** 小区名称 */
  communityName?: string;
  /** 所属区域ID */
  areaId?: ID;
}

/**
 * 小区列表返回结果
 */
export type CommunityListGetResultModel = PageResult<CommunityModel>;
