<script setup lang="ts">
import type { SelectProps } from 'ant-design-vue';

import { computed, onMounted, reactive, ref, watch } from 'vue';

import {
  Button as AButton,
  Col as ACol,
  Drawer as ADrawer,
  Form as AForm,
  FormItem as AFormItem,
  Input as AInput,
  InputNumber as AInputNumber,
  Row as ARow,
  Select as ASelect,
  Space as ASpace,
  Switch as ASwitch,
  Textarea as ATextarea,
} from 'ant-design-vue';
// 移除违约金和附加费API引用
// import { listLiquidatedDamagesConfigs } from '#/api/waterfee/price/liquidatedDamagesConfigs';
// import { listSurchargeConfigs } from '#/api/waterfee/price/surchargeConfigs';

import { getDictOptions } from '#/utils/dict';

// 阶梯价格层级接口 (对应后端 WaterfeePriceTier)
interface PriceTier {
  id?: number | string;
  priceConfigId?: number | string;
  startQuantity?: null | number;
  endQuantity?: null | number;
  price?: null | number;
}
// 表格数据行接口定义 (与 index.vue 同步或共享)
interface TableDataType {
  id: number | string;
  waterUseType: string;
  name: string;
  calculationMethod?: string; // Match model.d.ts
  isPopulation?: number; // Match model.d.ts (0/1)
  populationCount?: number;
  description?: string;
  priceTiers?: PriceTier[];
}

// 抽屉表单数据类型
// Ensure FormDataType also reflects the changes if needed, Omit might handle it
type FormDataType = Omit<TableDataType, 'createTime' | 'id'> & {
  id?: number | string;
}; // Omit base fields if necessary

const props = defineProps<{
  isEditing: boolean;
  record: null | TableDataType; // record 现在包含 ladderDetails
  visible: boolean;
}>();

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'save', data: FormDataType): void;
  (e: 'cancel'): void;
}>();

// 表单数据 (需要根据截图添加阶梯字段)
const formData = reactive<FormDataType>({
  waterUseType: '',
  name: '',
  calculationMethod: undefined, // Changed from billingCycle
  isPopulation: 0, // Changed from usePopulation, assuming 0 for false
  populationCount: undefined, // 添加人口数量
  priceTiers: [
    // Changed from ladderDetails, 初始化阶梯详情
    { startQuantity: 0, endQuantity: null, price: null },
    { startQuantity: null, endQuantity: null, price: null },
    { startQuantity: null, endQuantity: null, price: null },
  ],
  description: '', // Changed from remarks
});

// 下拉选项
const waterUseTypeOptions = ref<SelectProps['options']>([]);
const billingCycleOptions = ref<SelectProps['options']>([]); // 添加计算方式选项

// 获取下拉选项
onMounted(async () => {
  waterUseTypeOptions.value = await getDictOptions('water_use_type');
  // Assuming dict key for calculationMethod is 'cyc_price_calculation_method' as before, or adjust if different
  billingCycleOptions.value = await getDictOptions(
    'cyc_price_calculation_method',
  );
  // 移除获取违约金和附加费选项的逻辑
});

// 重置表单
const resetFormData = () => {
  formData.id = undefined;
  formData.waterUseType = '';
  formData.name = '';
  formData.calculationMethod = undefined; // Changed from billingCycle
  formData.isPopulation = 0; // Changed from usePopulation
  formData.populationCount = undefined;
  formData.priceTiers = [
    // Changed from ladderDetails, 重置阶梯
    { startQuantity: 0, endQuantity: null, price: null },
    { startQuantity: null, endQuantity: null, price: null },
    { startQuantity: null, endQuantity: null, price: null },
  ];
  formData.description = ''; // Changed from remarks
};

// 监听 props 更新表单
watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      if (props.isEditing && props.record) {
        // 编辑模式: 使用 record 数据填充表单
        Object.assign(formData, props.record);
        // 确保阶梯数据存在且至少有3个元素，不足则补充
        if (!formData.priceTiers || formData.priceTiers.length < 3) {
          const existingTiers = formData.priceTiers || [];
          formData.priceTiers = [
            // Changed from ladderDetails
            existingTiers[0] || {
              startQuantity: 0,
              endQuantity: null,
              price: null,
            },
            existingTiers[1] || {
              startQuantity: null,
              endQuantity: null,
              price: null,
            },
            existingTiers[2] || {
              startQuantity: null,
              endQuantity: null,
              price: null,
            },
          ];
        }
        // 确保布尔值正确
        // Convert backend 0/1 to boolean for Switch
        // Ensure props.record.isPopulation exists before comparison
        formData.isPopulation = props.record?.isPopulation === 1 ? 1 : 0;
      } else {
        // 添加模式: 重置表单
        resetFormData();
      }
    }
  },
);

// 抽屉标题
const drawerTitle = computed(
  () => (props.isEditing ? '编辑阶梯价格' : '创建阶梯价格'), // 修改标题
);

// 处理阶梯输入的逻辑
const updateLadder = (index: number, field: keyof PriceTier, value: any) => {
  // Changed LadderDetail to PriceTier
  if (formData.priceTiers && formData.priceTiers[index]) {
    (formData.priceTiers[index] as any)[field] = value; // Corrected property access

    // 自动更新下一阶梯的起始量
    if (field === 'endQuantity' && index < formData.priceTiers.length - 1) {
      // Changed ladderDetails
      const nextTier = formData.priceTiers[index + 1]; // Changed nextLadder
      if (nextTier && value !== null && value !== undefined) {
        // Changed nextLadder
        nextTier.startQuantity = Number(value) + 1; // 或根据业务规则调整 (+0.01?)
      } else if (nextTier) {
        // Changed nextLadder
        nextTier.startQuantity = null; // 如果当前止量为空，下一起始量也为空
      }
    }
    // 确保第三阶梯的止量为 null (代表以上)
    if (index === 2 && field === 'endQuantity' && formData.priceTiers?.[2]) {
      // Changed ladderDetails
      formData.priceTiers[2].endQuantity = null; // Changed ladderDetails
    }
  }
};

// 保存
const handleSave = () => {
  // 在保存前可以进行数据清理或转换
  const saveData = { ...formData };
  // Convert Switch boolean back to 0/1 for backend
  // Ensure saveData.isPopulation is treated as boolean before conversion
  saveData.isPopulation = saveData.isPopulation ? 1 : 0;
  // 移除空的阶梯 (如果需要)
  // saveData.priceTiers = saveData.priceTiers?.filter(l => l.price !== null); // Changed ladderDetails
  emit('save', saveData);
};

// 取消
const handleCancel = () => {
  emit('update:visible', false);
  emit('cancel');
};
</script>

<template>
  <ADrawer
    :open="props.visible"
    :title="drawerTitle"
    width="600"
    @close="handleCancel"
  >
    <AForm :model="formData" layout="vertical">
      <AFormItem label="用水性质" required>
        <ASelect
          v-model:value="formData.waterUseType"
          placeholder="请选择"
          :options="waterUseTypeOptions"
        />
      </AFormItem>
      <AFormItem label="价格名称" required>
        <AInput v-model:value="formData.name" placeholder="请输入" />
      </AFormItem>
      <AFormItem label="计算方式" required>
        <ASelect
          v-model:value="formData.calculationMethod"
          placeholder="请选择"
          :options="billingCycleOptions"
        />
      </AFormItem>

      <ARow :gutter="16">
        <ACol :span="12">
          <AFormItem label="是否按人口计算" required>
            <ASwitch
              :checked="formData.isPopulation === 1"
              @change="(checked) => (formData.isPopulation = checked ? 1 : 0)"
            />
          </AFormItem>
        </ACol>
        <ACol :span="12">
          <AFormItem v-if="formData.isPopulation" label="人口数量" required>
            <AInputNumber
              v-model:value="formData.populationCount"
              placeholder="请输入"
              style="width: 100%"
              :min="1"
            />
          </AFormItem>
        </ACol>
      </ARow>

      <!-- 阶梯价格输入 (需要根据截图详细实现) -->
      <h4>阶梯设置</h4>
      <div
        v-for="(tier, index) in formData.priceTiers"
        :key="index"
        class="mb-4 rounded border p-4"
      >
        <h5>阶梯 {{ index + 1 }}</h5>
        <ARow :gutter="16">
          <ACol :span="8">
            <AFormItem label="起量 (吨)" required>
              <!-- 第一阶梯起始固定为0，后续自动计算 -->
              <AInputNumber
                :value="tier.startQuantity ?? undefined"
                placeholder="起量"
                style="width: 100%"
                :min="0"
                :disabled="index > 0"
                @change="(value) => updateLadder(index, 'startQuantity', value)"
              />
            </AFormItem>
          </ACol>
          <ACol :span="8">
            <!-- 前两阶梯必填 -->
            <AFormItem label="止量 (吨)" :required="index < 2">
              <template v-if="index < 2">
                <!-- 前两阶梯使用 InputNumber -->
                <!-- 止量需大于起量 -->
                <AInputNumber
                  :value="tier.endQuantity ?? undefined"
                  placeholder="止量"
                  style="width: 100%"
                  :min="tier.startQuantity ?? 0"
                  @change="(value) => updateLadder(index, 'endQuantity', value)"
                />
              </template>
              <template v-else>
                <!-- 第三阶梯显示“以上” -->
                <AInput value="以上" disabled />
              </template>
            </AFormItem>
          </ACol>
          <ACol :span="8">
            <AFormItem label="价格 (元/吨)" required>
              <AInputNumber
                :value="tier.price ?? undefined"
                placeholder="价格"
                style="width: 100%"
                :min="0"
                :precision="2"
                @change="(value) => updateLadder(index, 'price', value)"
              />
            </AFormItem>
          </ACol>
        </ARow>
      </div>
      <!-- 移除违约金和附加费部分 -->

      <AFormItem label="描述">
        <ATextarea
          v-model:value="formData.description"
          placeholder="输入内容"
          :rows="4"
          :maxlength="100"
          show-count
        />
      </AFormItem>

      <!-- 计算规则说明 (从截图添加) -->
      <div class="mt-4 rounded bg-gray-100 p-4">
        <h5>计算规则：</h5>
        <p>
          1.
          按选择按年计算，则阶梯用量按照自然年度全年累计用量计算阶梯水价；选择按月计算，则阶梯用量按当月抄表量计算阶梯水价。
        </p>
        <p>
          2.
          选择按人口计算时，按照人口增量计算。如此处设置人口数量为3时，则计算阶梯起止量时按设置的3倍计算。
        </p>
      </div>
    </AForm>
    <template #footer>
      <ASpace>
        <AButton @click="handleCancel">取消</AButton>
        <AButton type="primary" @click="handleSave">保存</AButton>
      </ASpace>
    </template>
  </ADrawer>
</template>

<style scoped>
.border {
  border: 1px solid #d9d9d9;
}

.rounded {
  border-radius: 4px;
}

.mb-4 {
  margin-bottom: 16px;
}

.p-4 {
  padding: 16px;
}

.mt-4 {
  margin-top: 16px;
}

.bg-gray-100 {
  background-color: #f7f7f7;
}

h4 {
  margin-bottom: 16px;
  font-weight: bold;
}

h5 {
  margin-bottom: 12px;
  font-weight: bold;
}

p {
  margin-bottom: 8px;
  font-size: 12px; /* Adjust font size for rules */
  line-height: 1.6;
  color: #666; /* Adjust color for rules */
}
</style>
