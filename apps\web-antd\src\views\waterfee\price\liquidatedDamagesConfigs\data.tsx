import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { getPopupContainer } from '@vben/utils';

import { getDictOptions } from '#/utils/dict';
import { renderDict } from '#/utils/render';

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'name',
    label: '违约金名称',
  },
  {
    component: 'Select',
    componentProps: {
      getPopupContainer,
      options: getDictOptions('calculation_method'),
    },
    fieldName: 'calculationMethod',
    label: '计算方式',
  },
  {
    component: 'Select',
    componentProps: {
      getPopupContainer,
      options: getDictOptions('collection_method'),
    },
    fieldName: 'collectionMethod',
    label: '收取方式',
  },
];

export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
  {
    title: '序号',
    field: 'id',
  },
  {
    title: '违约金名称',
    field: 'name',
  },
  {
    title: '计算方式',
    field: 'calculationMethod',
    slots: {
      default: ({ row }) => {
        return renderDict(row.calculationMethod, 'calculation_method');
      },
    },
  },
  {
    title: '固定金额',
    field: 'fixedAmount',
  },
  {
    title: '利率(%)',
    field: 'interestRatePercent',
  },
  {
    title: '收取方式',
    field: 'collectionMethod',
    slots: {
      default: ({ row }) => {
        return renderDict(row.collectionMethod, 'collection_method');
      },
    },
  },
  {
    title: '开始日期',
    field: 'startDate',
  },
  {
    title: '是否大于本金',
    field: 'canExceedPrincipal',
    slots: {
      default: ({ row }) => {
        return renderDict(row.canExceedPrincipal, 'sys_on_off');
      },
    },
  },
  {
    title: '免除违约金启用状态',
    field: 'waiverEnabled',
    slots: {
      default: ({ row }) => {
        return renderDict(row.waiverEnabled, 'sys_on_off');
      },
    },
  },
  {
    title: '免除截止时间',
    field: 'waiverTime',
  },
  {
    title: '备注',
    field: 'remarks',
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 180,
  },
];

export const drawerSchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
    fieldName: 'id',
    label: '主键',
  },
  {
    component: 'Input',
    fieldName: 'name',
    label: '违约金名称',
    rules: 'required',
  },
  {
    component: 'Select',
    componentProps: {
      options: getDictOptions('calculation_method'),
    },
    fieldName: 'calculationMethod',
    label: '计算方式',
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'fixedAmount',
    label: '固定金额',
    // 当计算方式选择为固定金额选项的时候显示此输入框，否则隐藏
    dependencies: {
      if(values) {
        return !!values.calculationMethod && values.calculationMethod === '1';
      },
      // 只有指定的字段改变时，才会触发
      triggerFields: ['calculationMethod'],
    },
  },
  {
    component: 'Input',
    fieldName: 'interestRatePercent',
    label: '利率(%)',
    // 当计算方式选择为比例金额选项的时候显示此输入框，否则隐藏
    dependencies: {
      if(values) {
        return !!values.calculationMethod && values.calculationMethod === '2';
      },
      // 只有指定的字段改变时，才会触发
      triggerFields: ['calculationMethod'],
    },
  },
  {
    component: 'Select',
    componentProps: {
      options: getDictOptions('collection_method'),
    },
    fieldName: 'collectionMethod',
    label: '收取方式',
    rules: 'required',
  },
  {
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
    fieldName: 'startDate',
    label: '开始日期',
  },
  {
    component: 'RadioGroup',
    componentProps: {
      buttonStyle: 'solid',
      options: getDictOptions('sys_on_off'),
      optionType: 'button',
    },
    fieldName: 'canExceedPrincipal',
    label: '是否大于本金',
    rules: 'required',
  },
  {
    component: 'RadioGroup',
    componentProps: {
      buttonStyle: 'solid',
      options: getDictOptions('sys_on_off'),
      optionType: 'button',
    },
    fieldName: 'waiverEnabled',
    label: '免除违约金启用状态',
    rules: 'required',
  },
  {
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
    fieldName: 'waiverTime',
    label: '免除截止时间',
  },
  {
    component: 'Textarea',
    fieldName: 'remarks',
    label: '备注',
  },
];
