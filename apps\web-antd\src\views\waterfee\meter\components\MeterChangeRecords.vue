<script setup lang="ts">
import { onMounted, ref, watch } from 'vue';

import { Empty, message, Table } from 'ant-design-vue';

import { getMeterChangeByMeterId, getMeterChangeByMeterNo } from '#/api/waterfee/meter/change';
import { preserveBigInt } from '#/utils/json-bigint';
import { renderDict } from '#/utils/render';

const props = defineProps({
  meterId: {
    type: String,
    required: false,
    default: '',
  },
  meterNo: {
    type: String,
    required: false,
    default: '',
  },
});

const columns = [
  {
    title: '换表日期',
    dataIndex: 'changeTime',
    key: 'changeTime',
  },
  {
    title: '旧表编号',
    dataIndex: 'oldMeterNo',
    key: 'oldMeterNo',
  },
  {
    title: '旧表读数',
    dataIndex: 'oldMeterReading',
    key: 'oldMeterReading',
  },
  {
    title: '新表编号',
    dataIndex: 'newMeterNo',
    key: 'newMeterNo',
  },
  {
    title: '新表读数',
    dataIndex: 'newMeterReading',
    key: 'newMeterReading',
  },
  {
    title: '换表原因',
    dataIndex: 'changeReason',
    key: 'changeReason',
    customRender: ({ text }) => {
      return renderDict(text, 'meter_change_reason');
    },
  },
  {
    title: '操作人',
    dataIndex: 'operator',
    key: 'operator',
  },
  {
    title: '备注',
    dataIndex: 'remark',
    key: 'remark',
  },
];

const dataSource = ref([]);
const loading = ref(false);
const pagination = ref({
  pageSize: 10,
  showSizeChanger: true,
  showTotal: (total) => `共 ${total} 条记录`,
});

// 获取换表记录
async function fetchChangeRecords() {
  // 如果既没有meterId也没有meterNo，则不执行查询
  if (!props.meterId && !props.meterNo) return;

  loading.value = true;
  try {
    let records = [];

    // 优先使用水表编号查询
    if (props.meterNo) {
      console.log('使用水表编号查询换表记录:', props.meterNo);
      records = await getMeterChangeByMeterNo(props.meterNo);
    } else if (props.meterId) {
      console.log('使用水表ID查询换表记录:', props.meterId);
      records = await getMeterChangeByMeterId(props.meterId);
    }

    if (records && records.length > 0) {
      // 处理数据，确保大整数精度
      dataSource.value = records.map((item, index) => {
        const safeItem = preserveBigInt(item);
        // 直接使用后端返回的字段名称，不需要转换
        return {
          ...safeItem,
          key: index,
        };
      });

      console.log('处理后的换表记录数据:', dataSource.value);
    } else {
      dataSource.value = [];
    }
  } catch (error) {
    console.error('获取换表记录失败:', error);
    message.error('获取换表记录失败');
    dataSource.value = [];
  } finally {
    loading.value = false;
  }
}

// 监听meterId变化
watch(
  () => props.meterId,
  () => {
    fetchChangeRecords();
  },
  { immediate: true },
);

// 监听meterNo变化
watch(
  () => props.meterNo,
  () => {
    fetchChangeRecords();
  },
  { immediate: true },
);

onMounted(() => {
  fetchChangeRecords();
});
</script>

<template>
  <div>
    <Table
      :columns="columns"
      :data-source="dataSource"
      :loading="loading"
      :pagination="pagination"
      bordered
    >
      <template #emptyText>
        <Empty description="暂无换表记录" />
      </template>
    </Table>
  </div>
</template>

<style scoped>
/* 可以添加自定义样式 */
</style>
