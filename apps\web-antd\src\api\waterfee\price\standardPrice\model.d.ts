export interface StandardPriceVO {
  /**
   * 主键ID
   */
  id: string;

  /**
   * 价格名称
   */
  name: string;

  /**
   * 价格
   */
  price: number | string;

  /**
   * 违约金是否
   */
  penaltyEnabled?: boolean;

  /**
   * 违约金ID
   */
  penaltyId?: string;
  /**
   * 附加费是否
   */
  additionalEnabled?: boolean;

  /**
   * 附加费ID列表（JSON数组）
   */
  additionalFeeIds: string | string[];

  /**
   * 违约金名称
   */
  penaltyName?: string;

  /**
   * 附加费名称
   */
  additionalFeeNames?: string;

  /**
   * 描述
   */
  description: string;
  waterUseType: string;
}

export interface StandardPriceForm extends BaseEntity {
  /**
   * 主键ID
   */
  id?: number | string;

  /**
   * 价格名称
   */
  name?: string;

  /**
   * 价格
   */
  price?: number | string;
  /**
   * 违约金是否
   */
  penaltyEnabled?: boolean;

  /**
   * 违约金ID
   */
  penaltyId?: number | string;
  /**
   * 附加费是否
   */
  additionalEnabled?: boolean;

  /**
   * 附加费ID列表（JSON数组）
   */
  additionalFeeIds?: string | string[];

  /**
   * 违约金名称
   */
  penaltyName?: string;

  /**
   * 附加费名称
   */
  additionalFeeNames?: string;

  /**
   * 描述
   */
  description?: string;
}

export interface StandardPriceQuery extends PageQuery {
  /**
   * 价格名称
   */
  name?: string;

  /**
   * 价格
   */
  price?: number | string;

  /**
   * 违约金ID
   */
  penaltyId?: number | string;

  /**
   * 附加费ID列表（JSON数组）
   */
  additionalFeeIds?: string | string[];

  /**
   * 违约金名称
   */
  penaltyName?: string;

  /**
   * 附加费名称
   */
  additionalFeeNames?: string;

  /**
   * 描述
   */
  description?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
