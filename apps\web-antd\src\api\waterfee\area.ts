import type { ID } from '#/api/common';
import { requestClient } from '#/api/request';
import { ensureMeterIdString, preserveIdPrecision } from './meter/index';

/**
 * 区域信息接口
 */
export interface WaterfeeAreaVo {
  areaId: string;
  areaName: string;
  areaCode: string;
  parentId: string;
  orderNum: number;
  status: string;
  createBy: string;
  createTime: string;
  updateBy: string;
  updateTime: string;
  remark: string;
}

/**
 * 获取区域信息
 * @param areaId 区域ID
 * @returns 区域信息
 */
export function areaInfo(areaId: ID) {
  // 确保ID作为字符串处理
  const safeId = ensureMeterIdString(areaId);
  
  return requestClient.get<WaterfeeAreaVo>(`/waterfee/area/${safeId}`);
}

/**
 * 批量获取区域信息
 * @param areaIds 区域ID数组
 * @returns 区域信息列表
 */
export function areaInfoBatch(areaIds: ID[]) {
  // 转为字符串数组，确保 ID 精度（如果是大数）
  const safeIds = areaIds.map(id => ensureMeterIdString(id));
  
  // 使用 POST 请求传数组，避免 URL 太长
  return requestClient.post<WaterfeeAreaVo[]>('/waterfee/meter/areas', safeIds);
}

/**
 * 获取区域列表
 * @returns 区域列表
 */
export function areaList() {
  return requestClient.get<WaterfeeAreaVo[]>('/waterfee/area/list');
}
