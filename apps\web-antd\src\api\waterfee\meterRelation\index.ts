import { requestClient } from '#/api/request';
import type { BasicFetchResult } from '#/api/types/common';

// 水表总分关系API
enum Api {
  // 总分表关系
  meterRelationTree = '/waterfee/meter/relation/tree',
  addMeterRelation = '/waterfee/meter/relation/add',

  // 总分表平衡分析
  analyzeBalance = '/waterfee/api/meter/balance/analyze',
  getRecordByMonth = '/waterfee/api/meter/balance/getRecordByMonth',


  // 总分表预警阈值
  saveThreshold = '/waterfee/api/meter/threshold/save',
  listThreshold = '/waterfee/api/meter/threshold/list',


}

// 水表总分树结构
export interface MeterRelationTreeVO {
  meterId: string;
  meterNo: string;
  children?: MeterRelationTreeVO[];
}

// 添加总分表关系参数
export interface MeterRelationAddDTO {
  parentMeterId: string;
  childMeterId: string;
}

// 总分表平衡分析结果
export interface MeterBalanceAnalysisVO {
  parentMeterId: string;
  parentMeterNo: string;
  parentWaterUsage: number;
  totalChildWaterUsage: number;
  diffWater: number;
  leakRate: number;
  abnormal: boolean;
  abnormalReason: string;
  readingTime: string;
  children: {
    meterId: string;
    meterNo: string;
    waterUsage: number;
  }[];
}

// 分表所选时间的抄表记录 
export interface RecordByMonth {
  meterNo?: string;  
  waterUsage?: number; // 本期水量 
}
// 总分表预警阈值配置
export interface MeterThresholdConfigVO {
  parentMeterId: string;
  parentMeterNo: string;
  childMeterId: string;
  childMeterNo: string;
  threshold: number;
}

// 总分表预警阈值配置参数
export interface MeterThresholdConfigDTO {
  parentMeterId: string;
  childMeterId: string;
  threshold: number;
}

/**
 * 获取水表总分树结构
 * @returns 水表总分树结构
 */
export async function getMeterRelationTree() {
  try {
    const response = await requestClient.get(Api.meterRelationTree);

    // 如果响应是数组，直接返回
    if (Array.isArray(response)) {
      return response as MeterRelationTreeVO[];
    }

    // 如果响应是对象且有data字段，且data是数组，则返回data
    if (response && typeof response === 'object' && 'data' in response && Array.isArray(response.data)) {
      return response.data as MeterRelationTreeVO[];
    }

    // 其他情况返回空数组
    return [] as MeterRelationTreeVO[];
  } catch (error) {
    console.error('获取水表总分树结构失败:', error);
    return [] as MeterRelationTreeVO[];
  }
}

/**
 * 添加总分表关系
 * @param data 总分表关系参数
 * @returns void
 */
export function addMeterRelation(data: MeterRelationAddDTO) {
  return requestClient.post(Api.addMeterRelation, data);
}

/**
 * 分析总分表水量差异
 * @param parentMeterId 总表ID
 * @param readingTime 抄表时间
 * @returns 分析结果
 */
export function analyzeBalance(parentMeterId: string, readingTime: string) {
  return requestClient.get<MeterBalanceAnalysisVO>(Api.analyzeBalance, {
    params: {
      parentMeterId,
      readingTime,
    },
  });
}

/**
 * 获取分表所选时间的用水量
 * @param MeterNo 水表编号
 * @param readingTime 抄表时间
 * @returns 分析结果
 */
export function getRecordByMonth(MeterNo: string, readingTime: string) {
  return requestClient.get<RecordByMonth>(Api.getRecordByMonth, {
    params: {
      MeterNo,
      readingTime,
    },
  });
}

/**
 * 保存/更新总分表预警阈值
 * @param data 预警阈值参数
 * @returns void
 */
export function saveThreshold(data: MeterThresholdConfigDTO) {
  return requestClient.post(Api.saveThreshold, data);
}

/**
 * 获取所有阈值配置
 * @returns 阈值配置列表
 */
export function listThreshold() {
  return requestClient.get<MeterThresholdConfigVO[]>(Api.listThreshold);
}
