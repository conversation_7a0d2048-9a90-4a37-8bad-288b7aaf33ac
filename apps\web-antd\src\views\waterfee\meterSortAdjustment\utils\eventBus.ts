// 事件类型
export const EventType = {
  ADJUST_METER: 'ADJUST_METER',
  MOVE_UP: 'MOVE_UP',
  MOVE_DOWN: 'MOVE_DOWN',
  CUSTOM_SORT: 'CUSTOM_SORT',
};

// 创建简单的事件总线
class EventBus {
  private events: Record<string, Array<(data: any) => void>> = {};

  // 添加事件监听器
  on(event: string, callback: (data: any) => void) {
    if (!this.events[event]) {
      this.events[event] = [];
    }
    this.events[event].push(callback);
  }

  // 移除事件监听器
  off(event: string, callback: (data: any) => void) {
    if (!this.events[event]) return;
    this.events[event] = this.events[event].filter(cb => cb !== callback);
  }

  // 触发事件
  emit(event: string, data?: any) {
    if (!this.events[event]) return;
    this.events[event].forEach(callback => callback(data));
  }
}

// 导出事件总线实例
export default new EventBus();
