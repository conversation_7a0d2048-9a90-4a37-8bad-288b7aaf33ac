export interface Ladder {
  range: string;
  price: number;
}

export interface LadderPriceModel {
  id: string;
  priceName: string;
  waterType: string;
  calculationCycle: string;
  isPopulation: boolean;
  populationNumber: number;
  ladder1: Ladder;
  ladder2: Ladder;
  ladder3: Ladder;
  remark: string;
}

export interface LadderPriceParams {
  priceName?: string;
  waterType?: string;
  calculationCycle?: string;
}

export interface LadderPriceListGetResultModel {
  list: LadderPriceModel[];
}
