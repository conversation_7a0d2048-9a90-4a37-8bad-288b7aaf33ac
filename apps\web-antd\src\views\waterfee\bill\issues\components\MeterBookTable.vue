<template>
  <BasicTable table-title="账单发行">
    <template #toolbar-tools>
      <Space>
        <Button @click="handleRefresh">
          <template #icon><ReloadOutlined /></template>
          刷新
        </Button>
        <Button
          type="primary"
          :disabled="!selectedRows.length"
          @click="handleBatchIssue"
        >
          <template #icon><SendOutlined /></template>
          批量发行
        </Button>
      </Space>
    </template>
    <template #action="{ row }">
      <Space>
        <Button type="link" @click="handleViewBills(row)">
          查看
        </Button>
        <Button type="link" @click="handleIssue(row)" v-if="row.executeStatus === '未发行'">
          发行
        </Button>
      </Space>
    </template>
  </BasicTable>
</template>

<script lang="ts" setup>
  import { Button, Space, message, Modal } from 'ant-design-vue';
  import { ReloadOutlined, SendOutlined } from '@ant-design/icons-vue';
  import { useVbenVxeGrid } from '#/adapter/vxe-table';
  import { billIssueList, billIssue } from '#/api/waterfee/bill';
  import { columns, querySchema } from '../data';
  import { ref, onMounted } from 'vue';
  import { useRouter } from 'vue-router';

  const router = useRouter();

  // 选中的表册
  const selectedRows = ref<any[]>([]);

  // 表册列表表单配置
  const formOptions = {
    commonConfig: {
      labelWidth: 80,
      componentProps: {
        allowClear: true,
      },
    },
    schema: querySchema(),
    wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
  };

  // 表册列表表格配置
  const gridOptions = {
    columns,
    // height: 'calc(100vh - 280px)',
    checkboxConfig: {
      highlight: true,
      reserve: true,
    },
    proxyConfig: {
      ajax: {
        query: async ({ page }: { page?: { currentPage: number; pageSize: number } }, formValues: Record<string, any> = {}) => {
          // 构建查询参数
          const params: any = {
            pageNum: page?.currentPage || 1,  // 添加默认值
            pageSize: page?.pageSize || 10,   // 添加默认值
            ...formValues,
          };

          try {
            const resp = await billIssueList(params);
            console.log('API Response:', resp); // 添加日志

            // 处理数据，根据需要进行转换
            const rows = resp.rows || [];
            rows.forEach((row: any) => {
              // 计算未付用户数
              row.unpaidBills = (row.totalBills || 0) - (row.paidBills || 0);

              // 如果没有发行状态，设置默认值
              if (!row.executeStatus) {
                row.executeStatus = '未发行';
              }

              // 如果没有发行人，设置为 N/A
              if (!row.executor) {
                row.executor = 'N/A';
              }

              // 如果没有发行时间，设置为 N/A
              if (!row.executeTime) {
                row.executeTime = 'N/A';
              }
            });

            return {
              rows: rows,
              total: resp.total || 0
            };
          } catch (error) {
            console.error('获取账单发行列表失败:', error);
            message.error('获取账单发行列表失败，请稍后重试');
            return { rows: [], total: 0 };
          }
        },
      },
    },
    id: 'waterfee-bill-issues-index',
    rowConfig: {
      keyField: 'meterBookId',
    },
    // 添加分页配置
    pagerConfig: {
      enabled: true,
      pageSize: 10
    }
  };

  // 创建表格实例
  const [BasicTable, tableApi] = useVbenVxeGrid({
    formOptions,
    gridOptions,
    gridEvents: {
      checkboxChange: () => {
        selectedRows.value = tableApi.grid.getCheckboxRecords();
      },
      checkboxAll: () => {
        selectedRows.value = tableApi.grid.getCheckboxRecords();
      },
    },
  });

  // 组件挂载时初始化数据
  onMounted(() => {
    tableApi.query();
  });

  // 查看表册下的账单列表（路由跳转）
  function handleViewBills(row: any) {
    if (row.meterBookId) {
      router.push(`/charges/bills?meterBookId=${row.meterBookId}`);
    }
  }

  // 发行单个表册的账单
  function handleIssue(row: any) {
    if (!row.meterBookId) {
      message.warning('无效的表册ID');
      return;
    }

    Modal.confirm({
      title: '发行账单',
      content: `确定要发行表册 ${row.meterBookName} 的账单吗？发行后将无法修改账单信息。`,
      onOk: async () => {
        try {
          await billIssue(row.meterBookId);
          console.log('id: ', row.meterBookId);
          message.success('账单发行成功');
          tableApi.query();
        } catch (error) {
          console.error('发行失败:', error);
          message.error('发行失败，请稍后重试');
        }
      },
    });
  }

  // 批量发行账单
  function handleBatchIssue() {
    if (!selectedRows.value.length) {
      message.warning('请选择要发行的表册');
      return;
    }

    const meterBookIds = selectedRows.value.filter(row => row.meterBookId !== undefined).map(row => row.meterBookId);

    Modal.confirm({
      title: '批量发行账单',
      content: `确定要发行选中的 ${meterBookIds.length} 个表册的账单吗？发行后将无法修改账单信息。`,
      onOk: async () => {
        try {
          await billIssue(meterBookIds);
          message.success('账单批量发行成功');
          tableApi.query();
          // 清空选中
          selectedRows.value = [];
        } catch (error) {
          console.error('批量发行失败:', error);
          message.error('批量发行失败，请稍后重试');
        }
      },
    });
  }

  // 刷新表格数据
  function handleRefresh() {
    tableApi.query();
  }
</script>

<style scoped>
/* 自定义样式 */
:deep(.vxe-table--render-default .vxe-body--row:hover) {
  background-color: #f5f5f5;
}
</style>
