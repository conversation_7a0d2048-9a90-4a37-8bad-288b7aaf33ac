export interface UserPriceChangeRecordVO {
  /**
   * 主键
   */
  priceChangeId: number | string;

  /**
   * 用户ID
   */
  userId: number | string;

  /**
   * 原价格-用水性质（字典waterfee_user_use_water_nature）
   */
  beforePriceUseWaterNature: string;

  /**
   * 原计费方式（字典waterfee_user_billing_method）
   */
  beforeBillingMethod: string;

  /**
   * 原是否有违约金（字典yes_no）
   */
  beforeIfPenalty: string;

  /**
   * 原违约金类型（字典waterfee_user_penalty_type）
   */
  beforePenaltyType: string;

  /**
   * 原是否有附加费（字典yes_no）
   */
  beforeIfExtraCharge: string;

  /**
   * 原附加费内容（多选，逗号隔开）（字典waterfee_user_extra_charge_type）
   */
  beforeExtraChargeType: string;

  /**
   * 新价格-用水性质（字典waterfee_user_use_water_nature）
   */
  afterPriceUseWaterNature: string;

  /**
   * 新计费方式（字典waterfee_user_billing_method）
   */
  afterBillingMethod: string;

  /**
   * 新是否有违约金（字典yes_no）
   */
  afterIfPenalty: string;

  /**
   * 新违约金类型（字典waterfee_user_penalty_type）
   */
  afterPenaltyType: string;

  /**
   * 新是否有附加费（字典yes_no）
   */
  afterIfExtraCharge: string;

  /**
   * 新附加费内容（多选，逗号隔开）（字典waterfee_user_extra_charge_type）
   */
  afterExtraChargeType: string;
}

export interface UserPriceChangeRecordForm extends BaseEntity {
  /**
   * 主键
   */
  priceChangeId?: number | string;

  /**
   * 用户ID
   */
  userId?: number | string;

  /**
   * 原价格-用水性质（字典waterfee_user_use_water_nature）
   */
  beforePriceUseWaterNature?: string;

  /**
   * 原计费方式（字典waterfee_user_billing_method）
   */
  beforeBillingMethod?: string;

  /**
   * 原是否有违约金（字典yes_no）
   */
  beforeIfPenalty?: string;

  /**
   * 原违约金类型（字典waterfee_user_penalty_type）
   */
  beforePenaltyType?: string;

  /**
   * 原是否有附加费（字典yes_no）
   */
  beforeIfExtraCharge?: string;

  /**
   * 原附加费内容（多选，逗号隔开）（字典waterfee_user_extra_charge_type）
   */
  beforeExtraChargeType?: string;

  /**
   * 新价格-用水性质（字典waterfee_user_use_water_nature）
   */
  afterPriceUseWaterNature?: string;

  /**
   * 新计费方式（字典waterfee_user_billing_method）
   */
  afterBillingMethod?: string;

  /**
   * 新是否有违约金（字典yes_no）
   */
  afterIfPenalty?: string;

  /**
   * 新违约金类型（字典waterfee_user_penalty_type）
   */
  afterPenaltyType?: string;

  /**
   * 新是否有附加费（字典yes_no）
   */
  afterIfExtraCharge?: string;

  /**
   * 新附加费内容（多选，逗号隔开）（字典waterfee_user_extra_charge_type）
   */
  afterExtraChargeType?: string;
}

export interface UserPriceChangeRecordQuery extends PageQuery {
  /**
   * 用户编号或用户名
   */
  userNoOrUserName?: string;

  /**
   * 查询开始时间
   */
  startTime?: string;

  /**
   * 查询结束时间
   */
  endTime?: string;
}
