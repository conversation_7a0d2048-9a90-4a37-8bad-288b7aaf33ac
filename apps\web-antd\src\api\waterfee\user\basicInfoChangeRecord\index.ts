import type { PageQuery } from '#/api/common';
import type {
  UserBasicInfoChangeRecordForm,
  UserBasicInfoChangeRecordQuery,
  UserBasicInfoChangeRecordVO,
} from '#/api/waterfee/user/basicInfoChangeRecord/model.d';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

enum Api {
  list = '/waterfee/userBasicInfoChangeRecord/list',
  root = '/waterfee/userBasicInfoChangeRecord',
}

/**
 * 用水用户基础信息变更记录导出
 * @param data data
 * @returns void
 */
export function UserBasicInfoChangeRecordExport(
  data: Partial<UserBasicInfoChangeRecordForm>,
) {
  return commonExport(`${Api.root}/export`, data);
}

/**
 * 查询用水用户基础信息变更记录列表
 * @param params 查询参数
 * @returns 用水用户基础信息变更记录列表
 */
export function listUserBasicInfoChangeRecord(
  params?: PageQuery & UserBasicInfoChangeRecordQuery,
) {
  return requestClient.get<UserBasicInfoChangeRecordVO>(Api.list, { params });
}

/**
 * 查询用水用户基础信息变更记录详细
 * @param basicInfoChangeId 用水用户基础信息变更记录ID
 * @returns 用水用户基础信息变更记录信息
 */
export function getUserBasicInfoChangeRecord(
  basicInfoChangeId: number | string,
) {
  return requestClient.get<UserBasicInfoChangeRecordForm>(
    `${Api.root}/${basicInfoChangeId}`,
  );
}

/**
 * 新增用水用户基础信息变更记录
 * @param data 新增数据
 * @returns void
 */
export function addUserBasicInfoChangeRecord(
  data: UserBasicInfoChangeRecordForm,
) {
  return requestClient.postWithMsg<void>(Api.root, data);
}

/**
 * 修改用水用户基础信息变更记录
 * @param data 修改数据
 * @returns void
 */
export function updateUserBasicInfoChangeRecord(
  data: UserBasicInfoChangeRecordForm,
) {
  return requestClient.putWithMsg<void>(Api.root, data);
}

/**
 * 删除用水用户基础信息变更记录
 * @param basicInfoChangeId 用水用户基础信息变更记录ID或ID数组
 * @returns void
 */
export function delUserBasicInfoChangeRecord(
  basicInfoChangeId: Array<number | string> | number | string,
) {
  return requestClient.deleteWithMsg<void>(`${Api.root}/${basicInfoChangeId}`);
}
