import type { PageQuery } from '#/api/common';
import type {
  StandardPriceForm,
  StandardPriceQuery,
  StandardPriceVO,
} from '#/api/waterfee/price/standardPrice/model.d';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

enum Api {
  list = '/waterfee/standardPrice/list',
  root = '/waterfee/standardPrice',
}

/**
 * 标准价格导出
 * @param data data
 * @returns void
 */
export function StandardPriceExport(data: Partial<StandardPriceForm>) {
  return commonExport(`${Api.root}/export`, data);
}

/**
 * 查询标准价格列表
 * @param params 查询参数
 * @returns 标准价格列表
 */
export function listStandardPrice(params?: PageQuery & StandardPriceQuery) {
  return requestClient.get<{ rows: StandardPriceVO[]; total: number }>(
    Api.list,
    { params },
  );
}

/**
 * 查询标准价格详细
 * @param id 标准价格ID
 * @returns 标准价格信息
 */
export function getStandardPrice(id: number | string) {
  return requestClient.get<StandardPriceForm>(`${Api.root}/${id}`);
}

/**
 * 新增标准价格
 * @param data 新增数据
 * @returns void
 */
export function addStandardPrice(data: StandardPriceForm) {
  return requestClient.postWithMsg<void>(Api.root, data);
}

/**
 * 修改标准价格
 * @param data 修改数据
 * @returns void
 */
export function updateStandardPrice(data: StandardPriceForm) {
  return requestClient.putWithMsg<void>(Api.root, data);
}

/**
 * 删除标准价格
 * @param id 标准价格ID或ID数组
 * @returns void
 */
export function delStandardPrice(id: Array<number | string> | number | string) {
  return requestClient.deleteWithMsg<void>(`${Api.root}/${id}`);
}
