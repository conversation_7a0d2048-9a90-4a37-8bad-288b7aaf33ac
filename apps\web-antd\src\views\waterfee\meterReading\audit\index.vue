<template>
  <Page :auto-content-height="true" contentClass="min-h-[600px]">
    <div v-if="!showDetail">
      <div class="mb-4">
        <Space>
          <Button type="primary" @click="handleBatchUpdateCount">
            <template #icon><SyncOutlined /></template>
            更新用户数
          </Button>
        </Space>
      </div>
      <div class="table-container">
        <BasicTable
          @reload="tableApi.query()"
          @form-submit="handleFormSubmit"
          table-title="抄表审核列表"
          @checkbox-change="handleTableSelectionChange"
          @checkbox-all="handleTableSelectionChange"
        />
        <div v-if="isLoading" class="loading-overlay">
          <div class="loading-spinner">
            <div class="spinner"></div>
            <div class="loading-text">数据加载中...</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 详情页标签页 -->
    <div v-else>
      <ACard :title="detailTitle" :loading="detailLoading">
        <template #extra>
          <AButton @click="handleBack">返回</AButton>
        </template>
        <!-- <Description @register="registerDescription" /> -->

        <div class="mt-4">
          <div class="mb-4 record-search-bar">
            <Space>
              <Input
                v-model:value="searchMeterNo"
                placeholder="请输入水表编号"
                style="width: 240px"
                @pressEnter="handleSearchMeter"
              />
              <Button type="primary" @click="handleSearchMeter">
                查询
              </Button>
            </Space>
          </div>

          <div class="record-list-container">
            <RecordList
              :meter-book-id="currentBookData.meterBookId"
              :task-id="currentBookData.taskId"
              :book-data="currentBookData"
              :search-meter-no="searchMeterNo"
              @reload="handleSuccess"
            />
          </div>
        </div>
      </ACard>
    </div>
  </Page>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, onUnmounted, reactive, computed, nextTick } from 'vue';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { Button, Space, message, Modal, Card as ACard, Input } from 'ant-design-vue';
import { Page } from '@vben/common-ui';
import { CheckOutlined, SyncOutlined } from '@ant-design/icons-vue';
import { Description, useDescription } from '#/components/description';
import { columns, querySchema } from './audit.data';
import { preserveBigInt } from '#/utils/json-bigint';
import { initAllOptions } from './utils/options';
// import { renderDict } from '#/utils/render';
import { auditRecordsByBook } from '#/api/waterfee/meterReadingRecord';
import { getMeterReadingAuditList } from '#/api/waterfee/meterReadingRecord/audit';
import { getReadingTaskList, updateReadingTaskCount, updateReadingTaskCountBatch } from '#/api/waterfee/meterReading';
import eventBus, { EventType } from './utils/eventBus';
import { meterInfoByNo } from '#/api/waterfee/meter';
import { getReadingTaskInfo } from '#/api/waterfee/meterReading';
import { meterBookInfo } from '#/api/waterfee/meterbook';
import { areaInfo } from '#/api/waterfee/area';

// 导入详情页组件
import RecordList from './components/RecordList.vue';

// 表单配置
const formOptions = {
  commonConfig: {
    labelWidth: 80,
    componentProps: {
      allowClear: true,
    },
  },
  schema: querySchema(),
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
  submitOnChange: false // 禁用表单值变化时自动提交，避免频繁刷新
};

// 当前选择的表册数据
const currentBookData = ref({});

// 缓存对象
const cache = reactive({
  taskInfo: {}, // 缓存任务信息，key为taskId
  meterInfo: {}, // 缓存水表信息，key为meterNo
  areaInfo: {}, // 缓存区域信息，key为businessAreaId
  bookInfo: {}, // 缓存表册信息，key为meterBookId
});

// 表格配置
const gridOptions = {
  columns,
  height: 'auto',
  minHeight: 400, // 设置适中的最小高度
  maxHeight: 600, // 设置最大高度，避免无限增长
  width: '100%',
  fit: true,
  autoResize: true, // 启用自动调整大小
  tableLayout: 'fixed',
  scrollX: {
    enabled: true,
    gt: 10,
  },
  scrollY: {
    enabled: true, // 启用虚拟滚动
    gt: 30, // 超过30条数据启用虚拟滚动
  },
  resizable: true,
  resizeConfig: {
    minWidth: 80,
  },
  tooltipConfig: {
    showAll: true,
  },
  rowConfig: {
    height: 40, // 设置行高
    isHover: true,
    // 设置行索引起始值为0，这样序号列显示的值就是从1开始
    // 因为我们在序号列中使用了 rowIndex + 1
    indexMethod: (row) => row._XID,
  },
  checkboxConfig: {
    // 高亮
    highlight: true,
    // 翻页时保留选中状态
    reserve: true,
  },
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues = {}) => {
        try {
          // 设置加载状态
          isLoading.value = true;

          // 只查询任务状态正常的任务
          const params = {
            pageNum: page?.currentPage || 1,
            pageSize: page?.pageSize || 10,
            taskStatus: '1', // 任务状态正常
            ...formValues
          };

          const resp = await getReadingTaskList(params);

          if (resp && resp.rows) {
            // 直接返回响应，不进行额外的数据处理
            // 因为我们现在展示的是任务列表，不需要获取额外的信息
            isLoading.value = false; // 重置加载状态
            return resp;
          } else {
            // 如果API返回格式不符合预期，使用模拟数据（仅用于开发测试）
            if (process.env.NODE_ENV !== 'production' && formValues.meterBookId) {
              const mockData = generateMockData(formValues.meterBookId);
              return {
                rows: mockData,
                total: mockData.length
              };
            } else {
              return { rows: [], total: 0 };
            }
          }
        } catch (error) {
          console.error('获取抄表任务列表失败:', error);
          message.error(`获取数据失败：${error.message || '未知错误'}`);

          // 重置加载状态
          isLoading.value = false;

          // 如果API调用失败，使用模拟数据（仅用于开发测试）
          if (process.env.NODE_ENV !== 'production' && formValues.meterBookId) {
            const mockData = generateMockData(formValues.meterBookId);
            return {
              rows: mockData,
              total: mockData.length
            };
          } else {
            return { rows: [], total: 0 };
          }
        }
      },
    },
  },
  id: 'waterfee-meter-reading-audit-index',
};

// 获取字典值对应的文本
function getReadingMethodText(code) {
  switch (code) {
    case '1':
      return '人工抄表';
    case '2':
      return '远程抄表';
    case '3':
      return '用户自报';
    default:
      return code || '';
  }
}

// 生成模拟数据
function generateMockData(meterBookId) {
  const data = [];
  for (let i = 1; i <= 5; i++) {
    data.push({
      taskId: `${i}`,
      meterBookId: meterBookId,
      meterBookName: `表册${meterBookId}`,
      businessAreaId: '1',
      businessAreaName: '默认区域',
      taskName: `抄表任务${i}`,
      meterType: i % 2 === 0 ? '1' : '2', // 1-智能表, 2-机械表
      isAudited: i % 3 === 0 ? '1' : '0',
      readingCount: Math.floor(Math.random() * 100) + 10,
      createTime: '2023-01-01 00:00:00',
      // 新增字段
      readerId: `${i + 100}`,
      readerName: `抄表员${i}`,
      readingMethod: i % 3 === 0 ? '1' : (i % 3 === 1 ? '2' : '3'),
      readingDay: 1,
      baseDay: 25,
      bookUserNum: Math.floor(Math.random() * 50) + 20,
      planReadingNum: Math.floor(Math.random() * 50) + 20,
      actualReadingNum: Math.floor(Math.random() * 40) + 10,
      auditorId: i % 3 === 0 ? '1' : '',
      auditorName: i % 3 === 0 ? '管理员' : ''
    });
  }
  return data;
}

// 初始化表格
const [BasicTable, tableApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

// 详情页状态
const showDetail = ref(false);
const detailLoading = ref(false);
const selectedBookIds = ref([]);
const searchMeterNo = ref(''); // 搜索水表编号
const isLoading = ref(false); // 表格加载状态

// 使用Description组件
const [registerDescription, { setDescProps }] = useDescription({
  column: 2,
  bordered: true,
  labelStyle: {
    fontWeight: 'bold',
    minWidth: '120px',
  },
  schema: [
    { field: 'meterBookName', label: '抄表手册' },
    { field: 'businessAreaName', label: '营业区域' },
    { field: 'readerName', label: '抄表员' },
    { field: 'readingMethod', label: '抄表方式' },
    { field: 'readingDay', label: '抄表例日' },
    { field: 'baseDay', label: '抄表基准日' },
    { field: 'bookUserNum', label: '手册用户数' },
    { field: 'planReadingNum', label: '计划抄表数' },
    { field: 'actualReadingNum', label: '实际抄表数' },
    { field: 'auditedReadingNum', label: '审核抄表数' },
    { field: 'readingMonth', label: '抄表月份' },
    { field: 'isAudited', label: '审核状态' },
    { field: 'auditTime', label: '审核时间' },
    { field: 'auditorName', label: '审核人' },
  ],
});

// 计算详情页标题
const detailTitle = computed(() => {
  return `抄表任务详情 - ${currentBookData.value.meterBookName || ''}`;
});

// 选中的记录ID
const selectedRecordIds = ref([]);

// 计算是否有选中行
const hasSelectedRows = computed(() => {
  return selectedRecordIds.value.length > 0;
});

// 防抖定时器
let formSubmitTimer = null;
let isSubmitting = false;

// 处理表单提交
function handleFormSubmit(formData) {
  // 如果正在提交，则不重复提交
  if (isSubmitting) return;

  // 防抖处理，避免短时间内多次调用
  if (formSubmitTimer) clearTimeout(formSubmitTimer);

  formSubmitTimer = setTimeout(() => {
    isSubmitting = true;

    // 直接查询，减少不必要的延迟
    tableApi.query(formData);

    // 重置状态
    setTimeout(() => {
      isSubmitting = false;
    }, 300);
  }, 200); // 减少防抖延迟
}

// 处理选择变化
function handleTableSelectionChange(params) {
  console.log('表格选择变化:', params);

  try {
    // 直接从表格API获取选中的记录
    const selectRecords = tableApi.grid.getCheckboxRecords() || [];
    console.log('通过API获取选中的记录:', selectRecords);

    if (selectRecords.length > 0) {
      // 更新选中的记录ID
      selectedRecordIds.value = selectRecords.map(item => item.taskId);

      // 更新选中的表册ID
      selectedBookIds.value = selectRecords.map(record => record.meterBookId);

      console.log('选中的记录ID:', selectedRecordIds.value);
    } else {
      // 清空选中记录
      selectedRecordIds.value = [];
      selectedBookIds.value = [];
    }
  } catch (error) {
    console.error('获取选中记录失败:', error);
  }
}

// 处理批量更新用户数和计划抄表数
async function handleBatchUpdateCount() {
  // 获取选中的行
  let selectRecords = [];

  try {
    selectRecords = tableApi.grid.getCheckboxRecords() || [];
    console.log('更新用户数操作 - 选中的记录:', selectRecords);

    if (!selectRecords.length) {
      message.warning('请选择要更新的任务');
      return;
    }

    // 获取选中的任务ID
    const taskIds = selectRecords.map(item => item.taskId);

    // 确认更新
    Modal.confirm({
      title: '确认更新',
      content: `确定要更新选中的 ${taskIds.length} 个任务的手册用户数和计划抄表数吗？`,
      onOk: async () => {
        try {
          // 调用批量更新API
          await updateReadingTaskCountBatch(taskIds);
          message.success('更新成功');
          // 刷新表格
          await tableApi.query();
        } catch (error) {
          console.error('更新失败:', error);
          message.error('更新失败，请重试');
        }
      }
    });
  } catch (error) {
    console.error('获取选中记录失败:', error);
    message.error('获取选中记录失败，请重试');
    return;
  }
}

// 处理批量审核
async function handleBatchAudit() {
  // 获取选中的行
  let selectRecords = [];

  try {
    selectRecords = tableApi.grid.getCheckboxRecords() || [];
    console.log('审核操作 - 选中的记录:', selectRecords);

    if (!selectRecords.length) {
      message.warning('请选择要审核的记录');
      return;
    }

    // 更新选中的记录ID（以防选择变化事件没有正确触发）
    selectedRecordIds.value = selectRecords.map(item => item.taskId);
  } catch (error) {
    console.error('获取选中记录失败:', error);
    message.error('获取选中记录失败，请重试');
    return;
  }

  // 过滤出未审核的记录
  const unauditedRecords = selectRecords.filter(record => record.isAudited !== '1');
  if (!unauditedRecords.length) {
    message.warning('选中的记录都已审核');
    return;
  }

  // 确认审核
  Modal.confirm({
    title: '确认审核',
    content: `确定要审核选中的 ${unauditedRecords.length} 条记录吗？`,
    onOk: async () => {
      try {
        // 批量审核
        for (const record of unauditedRecords) {
          await auditRecordsByBook(record.meterBookId, record.taskId);
        }

        message.success('审核成功');

        // 刷新表格
        tableApi.query();
      } catch (error) {
        console.error('审核失败:', error);
        message.error('审核失败，请重试');
      }
    }
  });
}

// 处理审核
function handleAuditRecords(row) {
  // 确认审核
  Modal.confirm({
    title: '确认审核',
    content: `确定要审核 ${row.meterBookName} 的抄表记录吗？`,
    onOk: async () => {
      try {
        // 调用审核API
        await auditRecordsByBook(row.meterBookId, row.taskId);

        message.success('审核成功');

        // 刷新表格
        tableApi.query();
      } catch (error) {
        console.error('审核失败:', error);
        message.error('审核失败，请重试');
      }
    }
  });
}

// 处理操作成功
function handleSuccess() {
  // 刷新表格
  tableApi.query();
}

// 获取区域和表册信息
async function fetchAreaAndMeterBookInfo(bookData) {
  try {
    // 获取区域信息
    if (bookData.businessAreaId) {
      const areaData = await areaInfo(bookData.businessAreaId);
      if (areaData) {
        bookData.businessAreaName = areaData.areaName;
      }
    }

    // 获取表册信息
    if (bookData.meterBookId) {
      const bookInfo = await meterBookInfo(bookData.meterBookId);
      if (bookInfo) {
        bookData.meterBookName = bookInfo.bookName;
      }
    }
  } catch (error) {
    console.error('获取区域和表册信息失败:', error);
  }
}

// 返回列表页
function handleBack() {
  showDetail.value = false;
}

// 监听表格选择变化 - 已由 handleTableSelectionChange 替代
// 保留此函数以兼容可能的其他调用
function handleSelectionChange() {
  try {
    const selectRecords = tableApi.grid.getCheckboxRecords() || [];
    hasSelectedRows.value = selectRecords.length > 0;
    selectedBookIds.value = selectRecords.map(record => record.meterBookId);

    // 同步更新 selectedRecordIds
    selectedRecordIds.value = selectRecords.map(record => record.taskId);
  } catch (error) {
    console.error('获取选中记录失败:', error);
  }
}

// 处理水表编号搜索
function handleSearchMeter() {
  console.log('搜索水表编号:', searchMeterNo.value);
  // 搜索逻辑由RecordList组件处理
}

// 处理查看详情
async function handleViewDetails(row) {
  try {
    detailLoading.value = true;
    showDetail.value = true;

    // 保存当前选中的数据
    currentBookData.value = { ...row };

    // 获取额外信息
    await fetchAreaAndMeterBookInfo(currentBookData.value);

    // 获取抄表方式的字典值
    const readingMethodText = getReadingMethodText(currentBookData.value.readingMethod);

    // 设置详情页数据
    const data = {
      meterBookName: currentBookData.value.meterBookName || '',
      businessAreaName: currentBookData.value.businessAreaName || '',
      readerName: currentBookData.value.readerName || '',
      readingMethod: readingMethodText,
      readingDay: currentBookData.value.readingDay || '',
      baseDay: currentBookData.value.baseDay || '',
      bookUserNum: currentBookData.value.bookUserNum || '',
      planReadingNum: currentBookData.value.planReadingNum || '',
      actualReadingNum: currentBookData.value.actualReadingNum || '',
      auditedReadingNum: currentBookData.value.auditedReadingNum || '',
      readingMonth: currentBookData.value.readingMonth || '',
      isAudited: currentBookData.value.isAudited === '1' ? '已审核' : '未审核',
      auditTime: currentBookData.value.auditTime || '',
      auditorName: currentBookData.value.auditorName || '',
    };

    setDescProps({ data });

    // 清空搜索条件
    searchMeterNo.value = '';
  } catch (error) {
    console.error('查看详情失败:', error);
    message.error('查看详情失败，请重试');
  } finally {
    detailLoading.value = false;
  }
}

// 组件挂载时初始化所有选项
onMounted(async () => {
  try {
    await initAllOptions();
    // console.log('抄表记录审核页面选项初始化完成');

    // 添加事件监听器
    eventBus.on(EventType.AUDIT_RECORDS, handleAuditRecords);
    eventBus.on(EventType.FORM_SUBMIT, handleFormSubmit);
    eventBus.on(EventType.VIEW_DETAILS, handleViewDetails);

    // 注意：不需要手动监听表格选择变化，VxeGrid 会自动处理
    // 我们可以在 gridOptions 中配置 checkboxConfig 来处理选择事件
  } catch (error) {
    console.error('初始化选项失败:', error);
    message.error('加载选项数据失败，请刷新页面重试');
  }
});

// 组件卸载时移除事件监听器
onUnmounted(() => {
  eventBus.off(EventType.AUDIT_RECORDS, handleAuditRecords);
  eventBus.off(EventType.FORM_SUBMIT, handleFormSubmit);
  eventBus.off(EventType.VIEW_DETAILS, handleViewDetails);
});
</script>

<style scoped>
.mt-4 {
  margin-top: 16px;
}
.mb-4 {
  margin-bottom: 16px;
}
.record-search-bar {
  padding: 8px 16px;
  background-color: #f9f9f9;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}
.record-list-container {
  padding: 16px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 确保表格占满整个宽度 */
:deep(.vxe-table--main-wrapper) {
  width: 100%;
}
:deep(.vxe-table--body-wrapper) {
  width: 100%;
}
:deep(.vxe-table--header-wrapper) {
  width: 100%;
}

/* 按钮样式 */
.mb-4 {
  padding: 8px 0;
}

/* 表格容器样式 */
.table-container {
  position: relative;
}

/* 加载遮罩层样式 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

/* 加载动画容器 */
.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 加载动画 */
.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* 加载文本 */
.loading-text {
  margin-top: 10px;
  font-size: 14px;
  color: #1890ff;
}

/* 旋转动画 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
