import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { getPopupContainer } from '@vben/utils';

import { getDictOptions } from '#/utils/dict';

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'userNoOrUserName',
    label: '关键字',
    componentProps: {
      placeholder: '请输入用户编号或名称',
      allowClear: true,
    },
  },
  {
    component: 'RangePicker',
    fieldName: 'timeRange',
    label: '处理日期',
    componentProps: {
      getPopupContainer,
      placeholder: ['开始日期', '结束日期'],
      valueFormat: 'YYYY-MM-DD',
      allowClear: true,
    },
  },
];

export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
  {
    title: '用户编号',
    field: 'userNo',
  },
  {
    title: '用户名',
    field: 'userName',
  },
  {
    title: '变更内容',
    field: 'changeContent',
  },
  {
    title: '处理人',
    field: 'createByUserName',
  },
  {
    title: '处理日期',
    field: 'createTime',
  },
];

export const drawerSchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
    fieldName: 'basicInfoChangeId',
    label: '主键',
  },
  {
    component: 'Input',
    fieldName: 'userId',
    label: '用户ID',
    rules: 'required',
  },
  {
    component: 'Select',
    componentProps: {
      options: getDictOptions('waterfee_user_customer_nature'),
    },
    fieldName: 'beforeCustomerNature',
    label: '原客户性质',
  },
  {
    component: 'Select',
    componentProps: {
      options: getDictOptions('waterfee_user_use_water_nature'),
    },
    fieldName: 'beforeUseWaterNature',
    label: '原用水性质',
  },
  {
    component: 'Input',
    fieldName: 'beforeUseWaterNumber',
    label: '原用水人数',
  },
  {
    component: 'Input',
    fieldName: 'beforePhoneNumber',
    label: '原手机号码',
  },
  {
    component: 'Input',
    fieldName: 'beforeAddress',
    label: '原用水地址',
  },
  {
    component: 'Input',
    fieldName: 'beforeEmail',
    label: '原电子邮箱',
  },
  {
    component: 'Input',
    fieldName: 'beforeTaxpayerIdentificationNumber',
    label: '原纳税人识别号',
  },
  {
    component: 'Input',
    fieldName: 'beforeInvoiceName',
    label: '原开票名称',
  },
  {
    component: 'Select',
    componentProps: {
      options: getDictOptions('waterfee_user_invoice_type'),
    },
    fieldName: 'beforeInvoiceType',
    label: '原发票类型',
  },
  {
    component: 'Select',
    componentProps: {
      options: getDictOptions('waterfee_user_customer_nature'),
    },
    fieldName: 'afterCustomerNature',
    label: '新客户性质',
    rules: 'required',
  },
  {
    component: 'Select',
    componentProps: {
      options: getDictOptions('waterfee_user_use_water_nature'),
    },
    fieldName: 'afterUseWaterNature',
    label: '新用水性质',
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'afterUseWaterNumber',
    label: '新用水人数',
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'afterPhoneNumber',
    label: '新手机号码',
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'afterAddress',
    label: '新用水地址',
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'afterEmail',
    label: '新电子邮箱',
  },
  {
    component: 'Input',
    fieldName: 'afterTaxpayerIdentificationNumber',
    label: '新纳税人识别号',
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'afterInvoiceName',
    label: '新开票名称',
    rules: 'required',
  },
  {
    component: 'Select',
    componentProps: {
      options: getDictOptions('waterfee_user_invoice_type'),
    },
    fieldName: 'afterInvoiceType',
    label: '新发票类型',
    rules: 'required',
  },
  {
    component: 'RichTextarea',
    componentProps: {
      width: '100%',
    },
    fieldName: 'changeContent',
    label: '变更内容',
  },
];
