<script setup lang="ts">
import { ref, reactive, onMounted, nextTick, h } from 'vue';
import { useDebounceFn } from '@vueuse/core';
import { useRouter } from 'vue-router';

import { Page } from '@vben/common-ui';
import { getPopupContainer } from '@vben/utils';

import { Button, Card, Form, Input, Table, message, Space, Select, DatePicker, InputNumber, Spin, AutoComplete, Modal } from 'ant-design-vue';
import { SearchOutlined, SaveOutlined, RollbackOutlined, PlusOutlined } from '@ant-design/icons-vue';

import { getMetersByUserNo } from '#/api/waterfee/meter/userMeter';
import { listUser } from '#/api/waterfee/user/archivesManage';
import { meterInfoByNo } from '#/api/waterfee/meter';
import { getLatestReading } from '#/api/waterfee/meterReadingRecord';
import { addMeterChange } from '#/api/waterfee/meter/change';
// 不再需要业务区域和抄表手册API，因为所属区域和所属表册以旧表为准
import { getDictOptions } from '#/utils/dict';
import { preserveBigInt } from '#/utils/json-bigint';

// 路由实例
const router = useRouter();

// 加载状态
const loading = ref(false);
const submitting = ref(false);

// 用户编号
const userNo = ref('');
const userOptions = ref([]);
const userLoading = ref(false);
const userSearchValue = ref('');

// 水表列表
const meterList = ref([]);
const meterListLoading = ref(false);

// 选中的水表
const selectedMeter = ref(null);

// 表单实例
const formRef = ref();
const formState = reactive({
  // 换表基本信息 - 与后端 MeterChangeRecordBo 对应
  changeId: undefined,
  userId: '',
  oldMeterId: '',
  newMeterId: '',
  oldMeterNo: '',
  newMeterNo: '',
  changeType: 2, // 默认换表类型：2-更换
  changeReason: undefined,
  oldMeterReading: 0,
  newMeterReading: 0,
  changeTime: '',
  operator: '',
  remark: '',
  auditStatus: '0', // 默认未审核

  // 新表信息 - 与后端 MeterChangeRecordBo 对应
  meterType: undefined, // 水表类型(1-机械表 2-智能表)
  manufacturer: '',     // 生产厂家
  caliber: '',          // 口径
  accuracy: '',         // 精度
  installAddress: '',   // 安装地址
  communicationMode: '', // 通讯方式
  valveControl: 0,      // 阀控功能(0-无 1-有)
  imei: '',             // IMEI号
  imsi: '',             // IMSI号

  // 新表附加信息 - 参考智能表详情页面
  meterCategory: '',    // 水表类别
  meterClassification: '', // 水表分类
  initialReading: 0,    // 初始读数
  installDate: '',      // 安装日期
  businessAreaId: '',   // 业务区域ID
  businessAreaName: '', // 业务区域名称
  meterBookId: '',      // 抄表手册ID
  meterBookName: '',    // 抄表手册名称
  meterRatio: 1,        // 表倍率
  measurementPurpose: '', // 计量用途
  waterNature: '',      // 用水性质
  iotPlatform: '',      // 物联网平台
  prepaid: 0,           // 是否预付费

  // 前端展示用字段 - 不传递给后端
  userNo: '',
  userName: '',

  // 旧表信息 - 仅用于前端展示
  _oldMeterType: undefined,
  _oldManufacturer: '',
  _oldMeterCategory: '',
  _oldMeterClassification: '',
  _oldMeasurementPurpose: '',
  _oldCaliber: '',
  _oldAccuracy: '',
  _oldInstallDate: '',
  _oldBusinessAreaId: '',
  _oldBusinessAreaName: '',
  _oldMeterBookId: '',
  _oldMeterBookName: '',
  _oldInstallAddress: '',
  _oldMeterRatio: '',
  _oldCommunicationMode: '',
  _oldValveControl: 0,
  _oldImei: '',
  _oldImsi: '',
  _oldIotPlatform: '',
  _oldPrepaid: '',
  _oldInitialReading: 0,
  _oldWaterNature: '',
});

// 不再需要业务区域和抄表手册选项，因为以旧表为准

// 表格列定义
const columns = [
  {
    title: '序号',
    width: 60,
    customRender: ({ index }) => index + 1,
  },
  {
    title: '用户编号',
    dataIndex: 'userNo',
    key: 'userNo',
    width: 120,
  },
  {
    title: '用户名',
    dataIndex: 'userName',
    key: 'userName',
    width: 120,
  },
  {
    title: '水表编号',
    dataIndex: 'meterNo',
    key: 'meterNo',
    width: 150,
  },
  {
    title: '小区名称',
    dataIndex: 'businessAreaName',
    key: 'businessAreaName',
    width: 150,
  },
  {
    title: '用表地址',
    dataIndex: 'installAddress',
    key: 'installAddress',
    width: 250,
  },
  {
    title: '管辖区域',
    dataIndex: 'businessAreaName',
    key: 'businessAreaName',
    width: 120,
  },
  {
    title: '抄表册册',
    dataIndex: 'meterBookName',
    key: 'meterBookName',
    width: 120,
  },
  {
    title: '操作',
    key: 'action',
    width: 80,
    fixed: 'right',
  },
];

// 搜索用户
async function handleSearchUser(value) {
  if (!value) {
    userOptions.value = [];
    return;
  }

  try {
    userLoading.value = true;
    userSearchValue.value = value;
    searchUserDebounced(value);
  } catch (error) {
    console.error('搜索用户失败:', error);
    message.error(`搜索失败：${error.message || '未知错误'}`);
    userOptions.value = [];
    userLoading.value = false;
  }
}

// 使用防抖函数优化搜索
const searchUserDebounced = useDebounceFn(async (value) => {
  if (!value || value.length < 2) {
    userOptions.value = [];
    userLoading.value = false;
    return;
  }

  try {
    const res = await listUser({
      searchValue: value,
      pageSize: 20,
      pageNum: 1,
    });

    // 处理用户数据
    let userData = [];
    if (res && res.rows) {
      userData = res.rows;
    } else if (res && Array.isArray(res)) {
      userData = res;
    } else if (res && res.data && Array.isArray(res.data)) {
      userData = res.data;
    }

    // 优化显示格式，使用更丰富的展示方式
    userOptions.value = userData.map((item) => {
      // 使用h函数创建更丰富的标签内容
      const label = {
        // 使用render函数创建更丰富的标签内容
        label: h('div', { class: 'flex flex-col' }, [
          h('span', { class: 'font-medium' }, `${item.userNo} - ${item.userName || '无名称'}`),
          h(
            'span',
            { class: 'text-xs text-gray-500' },
            item.installAddress || '暂无安装地址'
          ),
        ]),
        value: item.userNo,
        userId: item.userId,
        data: item,
      };

      return label;
    });
  } catch (error) {
    console.error('搜索用户失败:', error);
    message.error(`搜索失败：${error.message || '未知错误'}`);
    userOptions.value = [];
  } finally {
    userLoading.value = false;
  }
}, 300);

// 查询用户水表
async function handleSearch(selectedUserNo = null) {
  // 确保参数是字符串类型
  if (selectedUserNo && typeof selectedUserNo !== 'string') {
    console.warn('非字符串参数被传递给handleSearch:', selectedUserNo);
    selectedUserNo = null;
  }

  // 使用传入的用户编号或输入框中的用户编号
  const searchUserNo = selectedUserNo || userNo.value;

  if (!searchUserNo) {
    message.warning('请输入用户编号');
    return;
  }

  try {
    meterListLoading.value = true;

    // 查找选中的用户选项，获取用户ID
    const selectedOption = userOptions.value.find(option => option.value === searchUserNo);
    let res;

    res = await getMetersByUserNo(searchUserNo);

    // 处理不同的响应数据结构
    if (res) {
      let metersData = [];

      // 如果响应是数组，直接使用
      if (Array.isArray(res)) {
        metersData = res;
      }
      // 如果响应有rows字段且是数组，使用rows
      else if (res.rows && Array.isArray(res.rows)) {
        metersData = res.rows;
      }
      // 如果响应有data字段且是数组，使用data
      else if (res.data && Array.isArray(res.data)) {
        metersData = res.data;
      }
      // 如果响应是您提供的JSON格式（直接包含水表数据）
      else if (typeof res === 'object' && Object.keys(res).length > 0 && res.meterId) {
        metersData = [res];
      }

      // 处理数据
      meterList.value = metersData.map(item => preserveBigInt(item));

      if (meterList.value.length === 0) {
        message.warning('未找到该用户的水表信息');
      }
    } else {
      meterList.value = [];
      message.warning('未找到该用户的水表信息');
    }
  } catch (error) {
    console.error('查询用户水表失败:', error);
    message.error(`查询失败：${error.message || '未知错误'}`);
    meterList.value = [];
  } finally {
    meterListLoading.value = false;
  }
}

// 选择水表
async function handleSelectMeter(record) {
  console.log('选择水表:', record);

  // 设置选中的水表
  selectedMeter.value = record;

  // 设置旧表信息 - 与后端 MeterChangeRecordBo 对应的字段
  formState.oldMeterId = record.meterId;
  formState.oldMeterNo = record.meterNo;
  formState.userId = record.userId;
  formState.userNo = record.userNo;
  formState.userName = record.userName;

  // 设置新表的所属区域和所属表册，以旧表为准
  formState.businessAreaId = record.businessAreaId || record.areaId;
  formState.businessAreaName = record.businessAreaName || record.areaName;
  formState.areaId = record.businessAreaId || record.areaId;
  formState.areaName = record.businessAreaName || record.areaName;
  formState.meterBookId = record.meterBookId || record.bookId;
  formState.meterBookName = record.meterBookName || record.bookName;
  formState.bookId = record.meterBookId || record.bookId;
  formState.bookName = record.meterBookName || record.bookName;

  // 设置操作人
  formState.operator = localStorage.getItem('userName') || '';

  // 设置旧表信息 - 仅用于前端展示
  formState._oldMeterType = record.meterType;
  formState._oldManufacturer = record.manufacturer;
  formState._oldMeterCategory = record.meterCategory;
  formState._oldMeterClassification = record.meterClassification;
  formState._oldMeasurementPurpose = record.measurementPurpose;
  formState._oldCaliber = record.caliber;
  formState._oldAccuracy = record.accuracy;
  formState._oldInstallDate = record.installDate;
  formState._oldBusinessAreaId = record.businessAreaId || record.areaId;
  formState._oldBusinessAreaName = record.businessAreaName || record.areaName;
  formState._oldMeterBookId = record.meterBookId || record.bookId;
  formState._oldMeterBookName = record.meterBookName || record.bookName;
  formState._oldInstallAddress = record.installAddress;
  formState._oldMeterRatio = record.meterRatio;
  formState._oldCommunicationMode = record.communicationMode;
  formState._oldValveControl = record.valveControl;
  formState._oldImei = record.imei;
  formState._oldImsi = record.imsi;
  formState._oldIotPlatform = record.iotPlatform;
  formState._oldPrepaid = record.prepaid;
  formState._oldInitialReading = record.initialReading || 0;
  formState._oldWaterNature = record.waterNature || '';

  // 查询最新抄表记录
  try {
    loading.value = true;
    const latestReading = await getLatestReading(record.meterNo);
    if (latestReading && latestReading.lastReading !== undefined) {
      formState.oldMeterReading = latestReading.lastReading;
    } else {
      // 如果没有抄表记录，使用水表初始读数
      formState.oldMeterReading = record.initialReading || 0;
    }
  } catch (error) {
    console.error('获取最新抄表记录失败:', error);
    // 使用水表初始读数
    formState.oldMeterReading = record.initialReading || 0;
  } finally {
    loading.value = false;
  }

  // 确保表单显示
  console.log('选中的水表:', selectedMeter.value);

  // 强制更新视图
  nextTick(() => {
    console.log('表单状态:', formState);
  });
}

// 生成新表编号
function generateNewMeterNo() {
  // 生成基于时间戳的唯一编号
  const timestamp = new Date().getTime();
  const randomNum = Math.floor(Math.random() * 1000);
  return `M${timestamp}${randomNum}`;
}

// 设置新表信息
function handleSetNewMeter() {
  try {
    // 如果没有输入新表编号，自动生成一个
    if (!formState.newMeterNo) {
      formState.newMeterNo = generateNewMeterNo();
    }

    // 设置新表初始读数为0
    formState.newMeterReading = 0;

    // 设置初始读数
    formState.initialReading = formState.newMeterReading;

    // 设置安装日期为当前日期
    const now = new Date();
    formState.installDate = now.toISOString().split('T')[0];

    // 如果没有设置水表类型，默认与旧表相同
    if (!formState.meterType && formState._oldMeterType) {
      formState.meterType = formState._oldMeterType;
    }

    // 如果没有设置厂家，默认与旧表相同
    if (!formState.manufacturer && formState._oldManufacturer) {
      formState.manufacturer = formState._oldManufacturer;
    }

    // 如果没有设置水表类别，默认与旧表相同
    if (!formState.meterCategory && formState._oldMeterCategory) {
      formState.meterCategory = formState._oldMeterCategory;
    }

    // 如果没有设置水表分类，默认与旧表相同
    if (!formState.meterClassification && formState._oldMeterClassification) {
      formState.meterClassification = formState._oldMeterClassification;
    }

    // 如果没有设置计量用途，默认与旧表相同
    if (!formState.measurementPurpose && formState._oldMeasurementPurpose) {
      formState.measurementPurpose = formState._oldMeasurementPurpose;
    }

    // 如果没有设置口径，默认与旧表相同
    if (!formState.caliber && formState._oldCaliber) {
      formState.caliber = formState._oldCaliber;
    }

    // 如果没有设置精度，默认与旧表相同
    if (!formState.accuracy && formState._oldAccuracy) {
      formState.accuracy = formState._oldAccuracy;
    }

    // 如果没有设置安装地址，默认与旧表相同
    if (!formState.installAddress && formState._oldInstallAddress) {
      formState.installAddress = formState._oldInstallAddress;
    }

    // 确保业务区域和抄表手册与旧表相同
    if (formState._oldBusinessAreaId) {
      formState.businessAreaId = formState._oldBusinessAreaId;
      formState.businessAreaName = formState._oldBusinessAreaName;
      formState.areaId = formState._oldBusinessAreaId;
      formState.areaName = formState._oldBusinessAreaName;
    }

    // 确保抄表手册与旧表相同
    if (formState._oldMeterBookId) {
      formState.meterBookId = formState._oldMeterBookId;
      formState.meterBookName = formState._oldMeterBookName;
      formState.bookId = formState._oldMeterBookId;
      formState.bookName = formState._oldMeterBookName;
    }

    // 如果没有设置表倍率，默认与旧表相同
    if (!formState.meterRatio && formState._oldMeterRatio) {
      formState.meterRatio = formState._oldMeterRatio;
    }

    // 如果没有设置用水性质，默认与旧表相同
    if (!formState.waterNature && formState._oldWaterNature) {
      formState.waterNature = formState._oldWaterNature;
    }

    // 如果是智能表，设置通信相关信息
    if (formState.meterType === 2) {
      // 如果没有设置通信方式，默认与旧表相同
      if (!formState.communicationMode && formState._oldCommunicationMode) {
        formState.communicationMode = formState._oldCommunicationMode;
      }

      // 如果没有设置阀门控制，默认与旧表相同
      if (formState.valveControl === undefined && formState._oldValveControl !== undefined) {
        formState.valveControl = formState._oldValveControl;
      }

      // 如果没有设置IMEI号，生成一个新的
      if (!formState.imei) {
        formState.imei = `IMEI${Date.now()}`;
      }

      // 如果没有设置IMSI号，生成一个新的
      if (!formState.imsi) {
        formState.imsi = `IMSI${Date.now()}`;
      }

      // 如果没有设置物联网平台，默认与旧表相同
      if (!formState.iotPlatform && formState._oldIotPlatform) {
        formState.iotPlatform = formState._oldIotPlatform;
      }

      // 如果没有设置是否预付费，默认与旧表相同
      if (formState.prepaid === undefined && formState._oldPrepaid !== undefined) {
        formState.prepaid = formState._oldPrepaid;
      }
    }

    message.success('新表信息设置成功');
  } catch (error) {
    console.error('设置新表信息失败:', error);
    message.error(`设置失败：${error.message || '未知错误'}`);
  }
}

// 提交表单前的验证
async function validateForm() {
  try {
    // 表单验证
    await formRef.value.validateFields();

    if (!selectedMeter.value) {
      message.warning('请先选择要换表的水表');
      return false;
    }

    if (!formState.newMeterNo) {
      // 自动生成新表编号
      formState.newMeterNo = generateNewMeterNo();
    }

    if (!formState.changeReason) {
      message.warning('请选择换表原因');
      return false;
    }

    if (!formState.meterType) {
      message.warning('请选择水表类型');
      return false;
    }

    // 确保所属区域和所属表册已设置
    if (!formState.businessAreaId || !formState.businessAreaName) {
      // 如果没有设置，尝试从旧表获取
      if (formState._oldBusinessAreaId && formState._oldBusinessAreaName) {
        formState.businessAreaId = formState._oldBusinessAreaId;
        formState.businessAreaName = formState._oldBusinessAreaName;
        formState.areaId = formState._oldBusinessAreaId;
        formState.areaName = formState._oldBusinessAreaName;
      } else {
        message.warning('新表所属区域未设置');
        return false;
      }
    }

    if (!formState.meterBookId || !formState.meterBookName) {
      // 如果没有设置，尝试从旧表获取
      if (formState._oldMeterBookId && formState._oldMeterBookName) {
        formState.meterBookId = formState._oldMeterBookId;
        formState.meterBookName = formState._oldMeterBookName;
        formState.bookId = formState._oldMeterBookId;
        formState.bookName = formState._oldMeterBookName;
      } else {
        message.warning('新表所属表册未设置');
        return false;
      }
    }

    return true;
  } catch (error) {
    console.error('表单验证失败:', error);
    return false;
  }
}

// 提交表单
async function handleSubmit() {
  // 先验证表单
  const isValid = await validateForm();
  if (!isValid) {
    return;
  }

  // 显示确认对话框
  Modal.confirm({
    title: '确认提交',
    content: `您确定要提交此换表记录吗？
      旧表编号: ${formState.oldMeterNo}
      旧表读数: ${formState.oldMeterReading}
      新表编号: ${formState.newMeterNo}
      新表读数: ${formState.newMeterReading}`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      try {
        submitting.value = true;

        // 准备提交数据
        const submitData = {
          userId: formState.userId,
          oldMeterId: formState.oldMeterId,
          oldMeterNo: formState.oldMeterNo,
          newMeterNo: formState.newMeterNo,
          changeType: formState.changeType,
          changeReason: formState.changeReason,
          oldMeterReading: formState.oldMeterReading,
          newMeterReading: formState.newMeterReading,
          changeTime: formState.changeTime,
          operator: formState.operator,
          remark: formState.remark,
          auditStatus: formState.auditStatus,

          // 新表基本信息
          meterType: formState.meterType,
          manufacturer: formState.manufacturer,
          caliber: formState.caliber,
          accuracy: formState.accuracy,
          installAddress: formState.installAddress,

          // 智能表特有信息
          communicationMode: formState.communicationMode,
          valveControl: formState.valveControl,
          imei: formState.imei,
          imsi: formState.imsi,

          // 新表附加信息
          meterCategory: formState.meterCategory,
          meterClassification: formState.meterClassification,
          initialReading: formState.initialReading,
          installDate: formState.installDate,
          businessAreaId: formState.businessAreaId,
          businessAreaName: formState.businessAreaName,
          areaId: formState.businessAreaId,
          areaName: formState.businessAreaName,
          meterBookId: formState.meterBookId,
          meterBookName: formState.meterBookName,
          bookId: formState.meterBookId,
          bookName: formState.meterBookName,
          meterRatio: formState.meterRatio,
          measurementPurpose: formState.measurementPurpose,
          waterNature: formState.waterNature,
          iotPlatform: formState.iotPlatform,
          prepaid: formState.prepaid,
        };

        console.log('提交数据:', submitData);

        // 添加换表记录
        await addMeterChange(submitData);
        message.success('换表记录添加成功，即将返回列表页面...');

        // 延迟1秒后返回列表页面，让用户有时间看到成功消息
        setTimeout(() => {
          router.push('/meterManagement/change').then(() => {
              // 强制刷新当前页面
              window.location.reload();
          });
        }, 1000);
      } catch (error) {
        console.error('提交表单失败:', error);
        message.error(`提交失败：${error.message || '未知错误'}`);
      } finally {
        submitting.value = false;
      }
    },
  });
}

// 返回列表页面
function handleBack() {
  router.push('/meterManagement/change').then(() => {
    // 强制刷新当前页面
    window.location.reload();
  });
}

// 这些函数不再需要，因为所属区域和所属表册以旧表为准

// 不再需要获取业务区域列表，因为所属区域以旧表为准

// 初始化
onMounted(() => {
  // 设置默认日期时间
  const now = new Date();
  formState.changeTime = now.toISOString().replace('T', ' ').substring(0, 19);
});
</script>

<template>
  <Page :auto-content-height="true">
    <div class="p-4">
      <Card title="添加换表记录" :bordered="false">
        <Spin :spinning="loading">
          <!-- 第一部分：用户编号查询条件 -->
          <Card class="mb-4" :bordered="true">
            <div class="search-area">
              <div class="flex items-center">
                <span class="mr-2">用户编号/用户名：</span>
                <Select
                  v-model:value="userNo"
                  :options="userOptions"
                  :loading="userLoading"
                  show-search
                  placeholder="请输入用户编号或用户名"
                  style="width: 300px"
                  :filter-option="false"
                  @search="handleSearchUser"
                  @select="value => handleSearch(value)"
                  @keydown.enter="() => handleSearch()"
                >
                  <template #notFoundContent>
                    <div v-if="userLoading">正在搜索...</div>
                    <div v-else-if="userSearchValue">未找到匹配的用户</div>
                    <div v-else>请输入用户编号或用户名搜索</div>
                  </template>
                </Select>
                <Button type="primary" class="ml-2" :loading="meterListLoading" @click="() => handleSearch()">
                  <template #icon><SearchOutlined /></template>
                  查询
                </Button>

              </div>
            </div>
          </Card>

          <!-- 第二部分：水表列表结果 -->
          <Card class="mb-4" :bordered="true" v-if="meterList.length > 0">
            <Table
              :columns="columns"
              :data-source="meterList"
              :loading="meterListLoading"
              :pagination="{ pageSize: 5 }"
              :row-key="(record) => record.meterId"
              :scroll="{ x: 1200 }"
              size="small"
              bordered
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'action'">
                  <Space>
                    <Button
                      type="primary"
                      size="small"
                      @click="handleSelectMeter(record)"
                    >
                      选择
                    </Button>
                  </Space>
                </template>
              </template>
            </Table>
          </Card>

          <!-- 第三部分：水表信息表单 -->
          <Card :bordered="true" v-if="selectedMeter || formState.oldMeterNo">
            <!-- 基本信息 -->
            <div class="mb-4">
              <h3 class="mb-2">基本信息</h3>
              <div class="grid grid-cols-2 gap-4">
                <!-- 左侧表单 -->
                <div>
                  <Form
                    ref="formRef"
                    :model="formState"
                    layout="horizontal"
                    :label-col="{ span: 8 }"
                    :wrapper-col="{ span: 16 }"
                  >
                    <!-- 用户信息 -->
                    <Form.Item label="用户编号" name="userNo">
                      <Input v-model:value="formState.userNo" disabled />
                    </Form.Item>
                    <Form.Item label="用户名称" name="userName">
                      <Input v-model:value="formState.userName" disabled />
                    </Form.Item>

                    <!-- 旧表信息 -->
                    <Form.Item label="旧表编号" name="oldMeterNo" :rules="[{ required: true, message: '请输入旧表编号' }]">
                      <Input v-model:value="formState.oldMeterNo" disabled />
                    </Form.Item>
                    <Form.Item label="旧表读数" name="oldMeterReading" :rules="[{ required: true, message: '请输入旧表读数' }]">
                      <InputNumber
                        v-model:value="formState.oldMeterReading"
                        :min="0"
                        :precision="2"
                        style="width: 100%"
                      />
                    </Form.Item>

                    <!-- 换表日期 -->
                    <Form.Item label="换表日期" name="changeTime" :rules="[{ required: true, message: '请选择换表日期' }]">
                      <DatePicker
                        v-model:value="formState.changeTime"
                        :get-popup-container="getPopupContainer"
                        show-time
                        format="YYYY-MM-DD HH:mm:ss"
                        value-format="YYYY-MM-DD HH:mm:ss"
                        style="width: 100%"
                      />
                    </Form.Item>
                  </Form>
                </div>

                <!-- 右侧表单 -->
                <div>
                  <Form
                    :model="formState"
                    layout="horizontal"
                    :label-col="{ span: 8 }"
                    :wrapper-col="{ span: 16 }"
                  >
                    <!-- 新表信息 -->
                    <Form.Item label="新表编号" name="newMeterNo" :rules="[{ required: true, message: '请输入新表编号' }]">
                      <Input
                        v-model:value="formState.newMeterNo"
                        placeholder="可自动生成，也可手动输入"
                      >
                        <template #suffix>
                          <Button type="link" @click="handleSetNewMeter">生成</Button>
                        </template>
                      </Input>
                    </Form.Item>
                    <Form.Item label="新表读数" name="newMeterReading" :rules="[{ required: true, message: '请输入新表读数' }]">
                      <InputNumber
                        v-model:value="formState.newMeterReading"
                        :min="0"
                        :precision="2"
                        style="width: 100%"
                        placeholder="默认为0"
                      />
                    </Form.Item>

                    <!-- 换表类型和原因 -->
                    <Form.Item label="换表类型" name="changeType" :rules="[{ required: true, message: '请选择换表类型' }]">
                      <Select
                        v-model:value="formState.changeType"
                        :options="[
                          { label: '安装', value: 1 },
                          { label: '更换', value: 2 },
                          { label: '拆除', value: 3 }
                        ]"
                        :get-popup-container="getPopupContainer"
                        placeholder="请选择换表类型"
                      />
                    </Form.Item>
                    <Form.Item label="换表原因" name="changeReason" :rules="[{ required: true, message: '请选择换表原因' }]">
                      <Select
                        v-model:value="formState.changeReason"
                        :options="[
                          { label: '到期更换', value: 1 },
                          { label: '故障维修', value: 2 },
                          { label: '用户申请', value: 3 },
                          { label: '其他', value: 4 }
                        ]"
                        :get-popup-container="getPopupContainer"
                        placeholder="请选择换表原因"
                      />
                    </Form.Item>
                  </Form>
                </div>
              </div>
            </div>

            <!-- 旧表详细信息 -->
            <div class="mb-4">
              <h3 class="mb-2">旧表详细信息</h3>
              <div class="grid grid-cols-3 gap-4">
                <Form
                  :model="formState"
                  layout="horizontal"
                  :label-col="{ span: 8 }"
                  :wrapper-col="{ span: 16 }"
                >
                  <Form.Item label="水表类型" name="_oldMeterType">
                    <Select
                      v-model:value="formState._oldMeterType"
                      :options="getDictOptions('waterfee_meter_type')"
                      :get-popup-container="getPopupContainer"
                      disabled
                    />
                  </Form.Item>
                  <Form.Item label="厂家" name="_oldManufacturer">
                    <Select
                      v-model:value="formState._oldManufacturer"
                      :options="getDictOptions('meter_factory')"
                      :get-popup-container="getPopupContainer"
                      disabled
                    />
                  </Form.Item>
                  <Form.Item label="水表类别" name="_oldMeterCategory">
                    <Select
                      v-model:value="formState._oldMeterCategory"
                      :options="getDictOptions('waterfee_meter_category')"
                      :get-popup-container="getPopupContainer"
                      disabled
                    />
                  </Form.Item>
                </Form>

                <Form
                  :model="formState"
                  layout="horizontal"
                  :label-col="{ span: 8 }"
                  :wrapper-col="{ span: 16 }"
                >
                  <Form.Item label="水表分类" name="_oldMeterClassification">
                    <Select
                      v-model:value="formState._oldMeterClassification"
                      :options="getDictOptions('waterfee_meter_classification')"
                      :get-popup-container="getPopupContainer"
                      disabled
                    />
                  </Form.Item>
                  <Form.Item label="计量用途" name="_oldMeasurementPurpose">
                    <Select
                      v-model:value="formState._oldMeasurementPurpose"
                      :options="getDictOptions('measure_purposes')"
                      :get-popup-container="getPopupContainer"
                      disabled
                    />
                  </Form.Item>
                  <Form.Item label="口径" name="_oldCaliber">
                    <Select
                      v-model:value="formState._oldCaliber"
                      :options="getDictOptions('dnmm')"
                      :get-popup-container="getPopupContainer"
                      disabled
                    />
                  </Form.Item>
                </Form>

                <Form
                  :model="formState"
                  layout="horizontal"
                  :label-col="{ span: 8 }"
                  :wrapper-col="{ span: 16 }"
                >
                  <Form.Item label="精度" name="_oldAccuracy">
                    <Select
                      v-model:value="formState._oldAccuracy"
                      :options="getDictOptions('water_meter_accuracy')"
                      :get-popup-container="getPopupContainer"
                      disabled
                    />
                  </Form.Item>
                  <Form.Item label="安装日期" name="_oldInstallDate">
                    <DatePicker
                      v-model:value="formState._oldInstallDate"
                      :get-popup-container="getPopupContainer"
                      format="YYYY-MM-DD"
                      value-format="YYYY-MM-DD"
                      style="width: 100%"
                      disabled
                    />
                  </Form.Item>
                  <Form.Item label="安装地址" name="_oldInstallAddress">
                    <Input v-model:value="formState._oldInstallAddress" disabled />
                  </Form.Item>
                </Form>
              </div>

              <!-- 旧表附加信息 -->
              <div class="grid grid-cols-3 gap-4 mt-2">
                <Form
                  :model="formState"
                  layout="horizontal"
                  :label-col="{ span: 8 }"
                  :wrapper-col="{ span: 16 }"
                >
                  <Form.Item label="初始读数" name="_oldInitialReading">
                    <InputNumber
                      v-model:value="formState._oldInitialReading"
                      :precision="2"
                      style="width: 100%"
                      disabled
                    />
                  </Form.Item>
                  <Form.Item label="表倍率" name="_oldMeterRatio">
                    <InputNumber
                      v-model:value="formState._oldMeterRatio"
                      :precision="2"
                      style="width: 100%"
                      disabled
                    />
                  </Form.Item>
                  <Form.Item label="用水性质" name="_oldWaterNature">
                    <Select
                      v-model:value="formState._oldWaterNature"
                      :options="getDictOptions('waterfee_user_use_water_nature')"
                      :get-popup-container="getPopupContainer"
                      disabled
                    />
                  </Form.Item>
                </Form>
              </div>

              <!-- 智能表特有字段 -->
              <div v-if="formState._oldMeterType === 2" class="grid grid-cols-3 gap-4 mt-2">
                <Form
                  :model="formState"
                  layout="horizontal"
                  :label-col="{ span: 8 }"
                  :wrapper-col="{ span: 16 }"
                >
                  <Form.Item label="通信方式" name="_oldCommunicationMode">
                    <Select
                      v-model:value="formState._oldCommunicationMode"
                      :options="getDictOptions('waterfee_communication_mode')"
                      :get-popup-container="getPopupContainer"
                      disabled
                    />
                  </Form.Item>
                  <Form.Item label="阀门控制" name="_oldValveControl">
                    <Select
                      v-model:value="formState._oldValveControl"
                      :options="getDictOptions('waterfee_valve_control')"
                      :get-popup-container="getPopupContainer"
                      disabled
                    />
                  </Form.Item>
                  <Form.Item label="IMEI号" name="_oldImei">
                    <Input v-model:value="formState._oldImei" disabled />
                  </Form.Item>
                </Form>

                <Form
                  :model="formState"
                  layout="horizontal"
                  :label-col="{ span: 8 }"
                  :wrapper-col="{ span: 16 }"
                >
                  <Form.Item label="IMSI号" name="_oldImsi">
                    <Input v-model:value="formState._oldImsi" disabled />
                  </Form.Item>
                  <Form.Item label="物联网平台" name="_oldIotPlatform">
                    <Select
                      v-model:value="formState._oldIotPlatform"
                      :options="getDictOptions('iot_platform')"
                      :get-popup-container="getPopupContainer"
                      disabled
                    />
                  </Form.Item>
                  <Form.Item label="是否预付费" name="_oldPrepaid">
                    <Select
                      v-model:value="formState._oldPrepaid"
                      :options="getDictOptions('yes_no')"
                      :get-popup-container="getPopupContainer"
                      disabled
                    />
                  </Form.Item>
                </Form>
              </div>
            </div>

            <!-- 新表详细信息 -->
            <div class="mb-4">
              <div class="flex justify-between items-center mb-2">
                <h3>新表详细信息</h3>
                <Button type="primary" size="small" @click="handleSetNewMeter">
                  <template #icon><PlusOutlined /></template>
                  一键生成新表信息
                </Button>
              </div>
              <div class="grid grid-cols-3 gap-4">
                <Form
                  :model="formState"
                  layout="horizontal"
                  :label-col="{ span: 8 }"
                  :wrapper-col="{ span: 16 }"
                >
                  <Form.Item label="水表类型" name="meterType" :rules="[{ required: true, message: '请选择水表类型' }]">
                    <Select
                      v-model:value="formState.meterType"
                      :options="getDictOptions('waterfee_meter_type')"
                      :get-popup-container="getPopupContainer"
                      placeholder="请选择水表类型"
                    />
                  </Form.Item>
                  <Form.Item label="厂家" name="manufacturer">
                    <Select
                      v-model:value="formState.manufacturer"
                      :options="getDictOptions('meter_factory')"
                      :get-popup-container="getPopupContainer"
                      placeholder="请选择厂家"
                    />
                  </Form.Item>
                  <Form.Item label="水表类别" name="meterCategory">
                    <Select
                      v-model:value="formState.meterCategory"
                      :options="getDictOptions('waterfee_meter_category')"
                      :get-popup-container="getPopupContainer"
                      placeholder="请选择水表类别"
                    />
                  </Form.Item>
                </Form>

                <Form
                  :model="formState"
                  layout="horizontal"
                  :label-col="{ span: 8 }"
                  :wrapper-col="{ span: 16 }"
                >
                  <Form.Item label="水表分类" name="meterClassification">
                    <Select
                      v-model:value="formState.meterClassification"
                      :options="getDictOptions('waterfee_meter_classification')"
                      :get-popup-container="getPopupContainer"
                      placeholder="请选择水表分类"
                    />
                  </Form.Item>
                  <Form.Item label="计量用途" name="measurementPurpose">
                    <Select
                      v-model:value="formState.measurementPurpose"
                      :options="getDictOptions('measure_purposes')"
                      :get-popup-container="getPopupContainer"
                      placeholder="请选择计量用途"
                    />
                  </Form.Item>
                  <Form.Item label="口径" name="caliber">
                    <Select
                      v-model:value="formState.caliber"
                      :options="getDictOptions('dnmm')"
                      :get-popup-container="getPopupContainer"
                      placeholder="请选择口径"
                    />
                  </Form.Item>
                </Form>

                <Form
                  :model="formState"
                  layout="horizontal"
                  :label-col="{ span: 8 }"
                  :wrapper-col="{ span: 16 }"
                >
                  <Form.Item label="精度" name="accuracy">
                    <Select
                      v-model:value="formState.accuracy"
                      :options="getDictOptions('water_meter_accuracy')"
                      :get-popup-container="getPopupContainer"
                      placeholder="请选择精度"
                    />
                  </Form.Item>
                  <Form.Item label="安装地址" name="installAddress">
                    <Input
                      v-model:value="formState.installAddress"
                      placeholder="请输入安装地址"
                      :maxlength="200"
                    />
                  </Form.Item>
                  <Form.Item label="安装日期" name="installDate">
                    <DatePicker
                      v-model:value="formState.installDate"
                      :get-popup-container="getPopupContainer"
                      format="YYYY-MM-DD"
                      value-format="YYYY-MM-DD"
                      style="width: 100%"
                      placeholder="请选择安装日期"
                    />
                  </Form.Item>
                </Form>
              </div>

              <!-- 新表附加信息 -->
              <div class="grid grid-cols-3 gap-4 mt-2">
                <Form
                  :model="formState"
                  layout="horizontal"
                  :label-col="{ span: 8 }"
                  :wrapper-col="{ span: 16 }"
                >
                  <Form.Item label="所属区域" name="businessAreaName">
                    <Input
                      v-model:value="formState.businessAreaName"
                      placeholder="以旧表为准"
                      disabled
                    />
                  </Form.Item>
                  <Form.Item label="所属表册" name="meterBookName">
                    <Input
                      v-model:value="formState.meterBookName"
                      placeholder="以旧表为准"
                      disabled
                    />
                  </Form.Item>
                  <Form.Item label="表倍率" name="meterRatio">
                    <InputNumber
                      v-model:value="formState.meterRatio"
                      :min="0"
                      :precision="2"
                      style="width: 100%"
                      placeholder="请输入表倍率"
                    />
                  </Form.Item>
                </Form>

                <Form
                  :model="formState"
                  layout="horizontal"
                  :label-col="{ span: 8 }"
                  :wrapper-col="{ span: 16 }"
                >
                  <Form.Item label="用水性质" name="waterNature">
                    <Select
                      v-model:value="formState.waterNature"
                      :options="getDictOptions('waterfee_user_use_water_nature')"
                      :get-popup-container="getPopupContainer"
                      placeholder="请选择用水性质"
                    />
                  </Form.Item>
                  <Form.Item label="通信方式" name="communicationMode">
                    <Select
                      v-model:value="formState.communicationMode"
                      :options="getDictOptions('waterfee_communication_mode')"
                      :get-popup-container="getPopupContainer"
                      placeholder="请选择通信方式"
                    />
                  </Form.Item>
                  <Form.Item label="阀门控制" name="valveControl">
                    <Select
                      v-model:value="formState.valveControl"
                      :options="getDictOptions('waterfee_valve_control')"
                      :get-popup-container="getPopupContainer"
                      placeholder="请选择阀门控制"
                    />
                  </Form.Item>
                </Form>

                <Form
                  :model="formState"
                  layout="horizontal"
                  :label-col="{ span: 8 }"
                  :wrapper-col="{ span: 16 }"
                >
                  <Form.Item label="IMEI号" name="imei">
                    <Input
                      v-model:value="formState.imei"
                      placeholder="请输入IMEI号"
                    />
                  </Form.Item>
                  <Form.Item label="IMSI号" name="imsi">
                    <Input
                      v-model:value="formState.imsi"
                      placeholder="请输入IMSI号"
                    />
                  </Form.Item>
                  <Form.Item label="物联网平台" name="iotPlatform">
                    <Select
                      v-model:value="formState.iotPlatform"
                      :options="getDictOptions('iot_platform')"
                      :get-popup-container="getPopupContainer"
                      placeholder="请选择物联网平台"
                    />
                  </Form.Item>
                </Form>

                <Form
                  :model="formState"
                  layout="horizontal"
                  :label-col="{ span: 8 }"
                  :wrapper-col="{ span: 16 }"
                >
                  <Form.Item label="是否预付费" name="prepaid">
                    <Select
                      v-model:value="formState.prepaid"
                      :options="getDictOptions('yes_no')"
                      :get-popup-container="getPopupContainer"
                      placeholder="请选择是否预付费"
                    />
                  </Form.Item>
                </Form>
              </div>
            </div>

            <!-- 备注 -->
            <Form
              :model="formState"
              layout="horizontal"
              :label-col="{ span: 4 }"
              :wrapper-col="{ span: 20 }"
            >
              <Form.Item label="备注" name="remark">
                <Input.TextArea
                  v-model:value="formState.remark"
                  placeholder="请输入备注信息"
                  :rows="3"
                />
              </Form.Item>
            </Form>

            <!-- 表单按钮 -->
            <div class="flex justify-center mt-4">
              <Space>
                <Button type="primary" :loading="submitting" @click="handleSubmit">
                  <template #icon><SaveOutlined /></template>
                  提交
                </Button>
                <Button @click="handleBack">
                  <template #icon><RollbackOutlined /></template>
                  返回
                </Button>
              </Space>
            </div>
          </Card>
        </Spin>
      </Card>
    </div>
  </Page>
</template>

<style scoped>
.ant-form-item {
  margin-bottom: 16px;
}

.search-area {
  padding: 8px;
}

.ant-card {
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.ant-table-small {
  font-size: 13px;
}

.ant-table-small .ant-table-thead > tr > th {
  background-color: #f5f5f5;
  padding: 8px;
}

.ant-table-small .ant-table-tbody > tr > td {
  padding: 8px;
}

/* 未关联用户时显示红色 */
.not-linked {
  color: #ff4d4f;
}

/* 自动完成下拉菜单样式 */
:deep(.ant-select-dropdown) {
  max-width: 500px;
}

:deep(.ant-select-item-option-content) {
  white-space: normal;
  word-break: break-all;
  line-height: 1.5;
}

</style>
