import type { PageQuery } from '#/api/common';
import type {
  LiquidatedDamagesConfigsForm,
  LiquidatedDamagesConfigsQuery,
  LiquidatedDamagesConfigsVO,
} from '#/api/waterfee/price/liquidatedDamagesConfigs/model.d';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

enum Api {
  list = '/waterfee/liquidatedDamagesConfigs/list',
  root = '/waterfee/liquidatedDamagesConfigs',
}

/**
 * 违约金配置导出
 * @param data data
 * @returns void
 */
export function LiquidatedDamagesConfigsExport(
  data: Partial<LiquidatedDamagesConfigsForm>,
) {
  return commonExport(`${Api.root}/export`, data);
}

/**
 * 查询违约金配置列表
 * @param params 查询参数
 * @returns 违约金配置列表
 */
export function listLiquidatedDamagesConfigs(
  params?: LiquidatedDamagesConfigsQuery & PageQuery,
) {
  return requestClient.get<{
    rows: LiquidatedDamagesConfigsVO[];
    total: number;
  }>(Api.list, { params });
}

/**
 * 查询违约金配置详细
 * @param id 违约金配置ID
 * @returns 违约金配置信息
 */
export function getLiquidatedDamagesConfigs(id: number | string) {
  return requestClient.get<LiquidatedDamagesConfigsForm>(`${Api.root}/${id}`);
}

/**
 * 新增违约金配置
 * @param data 新增数据
 * @returns void
 */
export function addLiquidatedDamagesConfigs(
  data: LiquidatedDamagesConfigsForm,
) {
  return requestClient.postWithMsg<void>(Api.root, data);
}

/**
 * 修改违约金配置
 * @param data 修改数据
 * @returns void
 */
export function updateLiquidatedDamagesConfigs(
  data: LiquidatedDamagesConfigsForm,
) {
  return requestClient.putWithMsg<void>(Api.root, data);
}

/**
 * 删除违约金配置
 * @param id 违约金配置ID或ID数组
 * @returns void
 */
export function delLiquidatedDamagesConfigs(
  id: Array<number | string> | number | string,
) {
  return requestClient.deleteWithMsg<void>(`${Api.root}/${id}`);
}
