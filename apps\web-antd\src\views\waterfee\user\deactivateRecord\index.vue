<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';

import type { VxeGridProps } from '#/adapter/vxe-table';
import type { UserDeactivateRecordVO } from '#/api/waterfee/user/deactivateRecord/model.d';

import { Page, useVbenDrawer } from '@vben/common-ui';
import { getVxePopupContainer } from '@vben/utils';

import { Popconfirm, Space } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  delUserDeactivateRecord,
  listUserDeactivateRecord,
  UserDeactivateRecordExport,
} from '#/api/waterfee/user/deactivateRecord';
import { commonDownloadExcel } from '#/utils/file/download';

import { columns, querySchema } from './data';
import userDeactivateRecordDrawer from './userDeactivateRecord-drawer.vue';

const formOptions: VbenFormProps = {
  commonConfig: {
    labelWidth: 80,
    componentProps: {
      allowClear: true,
    },
  },
  schema: querySchema(),
  fieldMappingTime: [['timeRange', ['startTime', 'endTime'], 'YYYY-MM-DD']],
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
};

const gridOptions: VxeGridProps = {
  checkboxConfig: {
    // 高亮
    highlight: true,
    // 翻页时保留选中状态
    reserve: true,
  },
  columns,
  height: 'auto',
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues = {}) => {
        return await listUserDeactivateRecord({
          pageNum: page.currentPage,
          pageSize: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  rowConfig: {
    keyField: 'deactivateId',
  },
  id: 'waterfee-userDeactivateRecord-index',
};

const [BasicTable, tableApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

const [UserDeactivateRecordDrawer, drawerApi] = useVbenDrawer({
  connectedComponent: userDeactivateRecordDrawer,
});

// function handleAdd() {
//   drawerApi.setData({});
//   drawerApi.open();
// }

async function handleEdit(record: UserDeactivateRecordVO) {
  drawerApi.setData({ id: record.deactivateId });
  drawerApi.open();
}

async function handleDelete(row: UserDeactivateRecordVO) {
  await delUserDeactivateRecord([row.deactivateId]);
  await tableApi.query();
}

// function handleMultiDelete() {
//   const rows = tableApi.grid.getCheckboxRecords();
//   const ids = rows.map((row: UserDeactivateRecordVO) => row.deactivateId);
//   Modal.confirm({
//     title: '提示',
//     okType: 'danger',
//     content: `确认删除选中的${ids.length}条记录吗？`,
//     onOk: async () => {
//       await delUserDeactivateRecord(ids);
//       await tableApi.query();
//     },
//   });
// }

function handleDownloadExcel() {
  commonDownloadExcel(
    UserDeactivateRecordExport,
    '用水用户报停记录数据',
    tableApi.formApi.form.values,
  );
}
</script>

<template>
  <Page :auto-content-height="true">
    <BasicTable table-title="报停记录">
      <template #toolbar-tools>
        <Space>
          <!-- <a-button
            :disabled="!vxeCheckboxChecked(tableApi)"
            danger
            type="primary"
            v-access:code="['waterfee:userDeactivateRecord:remove']"
            @click="handleMultiDelete"
          >
            {{ $t('pages.common.delete') }}
          </a-button>
          <a-button
            type="primary"
            v-access:code="['waterfee:userDeactivateRecord:add']"
            @click="handleAdd"
          >
            {{ $t('pages.common.add') }}
          </a-button> -->
          <a-button
            type="primary"
            v-access:code="['waterfee:userDeactivateRecord:export']"
            @click="handleDownloadExcel"
          >
            {{ $t('pages.common.export') }}
          </a-button>
        </Space>
      </template>
      <template #action="{ row }">
        <Space>
          <ghost-button
            v-access:code="['waterfee:userDeactivateRecord:edit']"
            @click="handleEdit(row)"
          >
            {{ $t('pages.common.edit') }}
          </ghost-button>
          <Popconfirm
            :get-popup-container="getVxePopupContainer"
            placement="left"
            title="确认删除？"
            @confirm="handleDelete(row)"
          >
            <ghost-button
              danger
              v-access:code="['waterfee:userDeactivateRecord:remove']"
              @click.stop=""
            >
              {{ $t('pages.common.delete') }}
            </ghost-button>
          </Popconfirm>
        </Space>
      </template>
    </BasicTable>
    <UserDeactivateRecordDrawer @reload="tableApi.query()" />
  </Page>
</template>
