<script setup lang="ts">
import { computed, defineAsyncComponent, onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import {
  Button as AButton,
  Card as ACard,
  message,
  Tabs,
} from 'ant-design-vue';

import { areaInfo } from '#/api/waterfee/area';
import { getMechanicalMeterInfo } from '#/api/waterfee/meter/mechanical';
import { meterBookInfo } from '#/api/waterfee/meterbook';
import { Description, useDescription } from '#/components/description';
import { preserveBigInt } from '#/utils/json-bigint';

import { getDescSchema } from './mechanical.data';

// 异步加载组件
const ReadingRecords = defineAsyncComponent(
  () => import('../components/ReadingRecords.vue'),
);
const MeterChangeRecords = defineAsyncComponent(
  () => import('../../components/MeterChangeRecords.vue'),
);

const { TabPane } = Tabs;

// 路由和导航
const route = useRoute();
const router = useRouter();
const activeKey = ref('reading');

// 状态定义
const detailInfo = ref({});
const title = computed(() => '机械水表详情');
const loading = ref(false);
const meterId = ref('');

// 使用Description组件
const [registerDescription, { setDescProps }] = useDescription({
  column: 2,
  bordered: true,
  labelStyle: {
    fontWeight: 'bold',
    minWidth: '120px',
  },
  schema: [],
});

// 获取区域和表册信息
async function fetchAreaAndMeterBookInfo(meterData) {
  try {
    if (meterData.businessAreaId) {
      const areaData = await areaInfo(meterData.businessAreaId);
      if (areaData) {
        meterData.businessAreaName = areaData.areaName;
      }
    }

    if (meterData.meterBookId) {
      const bookData = await meterBookInfo(meterData.meterBookId);
      if (bookData) {
        meterData.meterBookName = bookData.bookName;
      }
    }
  } catch (error) {
    console.error('获取区域或表册信息失败:', error);
  }
}

// 返回列表页
function handleBack() {
  router.back();
}

// 初始化数据
const initData = async () => {
  const { id } = route.query;
  if (!id) {
    message.error('缺少水表ID参数');
    return;
  }

  meterId.value = String(id).replaceAll(/\D/g, ''); // 移除非数字字符
  console.log('详情页获取到水表ID:', meterId.value);

  loading.value = true;
  try {
    // 直接获取数据，因为 requestClient 已经处理了响应结果
    const data = await getMechanicalMeterInfo(meterId.value);
    // console.log('获取水表详情成功:', JSON.stringify(data, null, 2));

    // 处理返回的数据结构
    if (data && data.meterInfo) {
      detailInfo.value = preserveBigInt(data.meterInfo);
    } else {
      detailInfo.value = {};
      message.warning('未找到水表详细信息');
    }

    // 获取区域和表册信息
    await fetchAreaAndMeterBookInfo(detailInfo.value);
    // console.log(
    //   '获取区域和表册信息后的数据:',
    //   JSON.stringify(detailInfo.value, null, 2),
    // );

    // 设置描述项数据
    setDescProps(
      {
        data: detailInfo.value,
        schema: getDescSchema(detailInfo.value),
        title: '水表基本信息',
      },
      true,
    );
  } catch (error) {
    console.error('获取水表详情失败:', error);
    message.error(error.message || '获取水表详情失败');
  } finally {
    loading.value = false;
  }
};

// 组件挂载时初始化
onMounted(() => {
  initData();
});
</script>

<template>
  <div class="p-4">
    <ACard :title="title" :loading="loading">
      <template #extra>
        <AButton @click="handleBack">返回</AButton>
      </template>
      <Description @register="registerDescription" />

      <div class="mt-4">
        <Tabs v-model:active-key="activeKey">
          <TabPane key="reading" tab="抄表记录">
            <ReadingRecords
              :meter-id="meterId"
              :meter-no="detailInfo.meterNo"
            />
          </TabPane>
          <TabPane key="replacement" tab="换表记录">
            <MeterChangeRecords :meter-id="meterId" :meter-no="detailInfo.meterNo" />
          </TabPane>
        </Tabs>
      </div>
    </ACard>
  </div>
</template>

<style scoped>
.empty-content {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #999;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.form-item {
  display: flex;
  margin-bottom: 16px;
}

.form-item label {
  width: 120px;
  padding-right: 12px;
  font-weight: bold;
  color: rgb(0 0 0 / 65%);
  text-align: right;
}

.form-item span {
  color: rgb(0 0 0 / 85%);
}

.form-title {
  padding-left: 8px;
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: bold;
  border-left: 3px solid #1890ff;
}
</style>
