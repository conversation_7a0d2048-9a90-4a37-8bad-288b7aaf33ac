<script setup lang="ts">
import { ref, reactive } from 'vue';
import { Drawer, Form, Button, message, Select, Spin } from 'ant-design-vue';
import { addMeterRelation } from '#/api/waterfee/meterRelation';
import { meterList } from '#/api/waterfee/meter';
import type { MeterModel } from '#/api/waterfee/model/meter/meterModel';

const props = defineProps({
  open: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['update:open', 'reload']);

// 表单实例
const formRef = ref();

// 表单数据
const formState = reactive({
  parentMeterId: '',
  childMeterId: '',
});

// 水表列表
const meterOptions = ref<{ label: string; value: string }[]>([]);
const loading = ref(false);

// 缓存水表列表
let cachedMeterOptions = [];
let lastFetchTime = 0;
const CACHE_DURATION = 5 * 60 * 1000; // 缓存5分钟

// 加载水表列表
async function loadMeterList() {
  const now = Date.now();

  // 如果缓存有效，直接使用缓存
  if (cachedMeterOptions.length > 0 && now - lastFetchTime < CACHE_DURATION) {
    console.log('使用缓存的水表列表数据');
    meterOptions.value = [...cachedMeterOptions];
    return;
  }

  loading.value = true;
  try {
    console.log('从服务器获取水表列表数据');
    const res = await meterList({
      pageNum: 1,
      pageSize: 1000,
    });

    if (res && res.rows) {
      const options = res.rows.map((item) => ({
        label: `${item.meterNo} (ID: ${item.meterId})`,
        value: String(item.meterId),
      }));

      // 更新缓存
      cachedMeterOptions = [...options];
      lastFetchTime = now;

      // 更新选项
      meterOptions.value = options;
    }
  } catch (error) {
    console.error('获取水表列表失败:', error);
    message.error('获取水表列表失败');

    // 如果有缓存，使用缓存
    if (cachedMeterOptions.length > 0) {
      console.log('获取失败，使用缓存的水表列表数据');
      meterOptions.value = [...cachedMeterOptions];
    }
  } finally {
    loading.value = false;
  }
}

// 提交表单
async function handleSubmit() {
  try {
    await formRef.value.validate();

    if (formState.parentMeterId === formState.childMeterId) {
      message.error('总表和分表不能是同一个水表');
      return;
    }

    await addMeterRelation({
      parentMeterId: formState.parentMeterId,
      childMeterId: formState.childMeterId,
    });

    message.success('添加总分表关系成功');
    handleCancel();
    emit('reload');
  } catch (error) {
    console.error('添加总分表关系失败:', error);
    message.error('添加总分表关系失败');
  }
}

// 取消
function handleCancel() {
  formRef.value.resetFields();
  emit('update:open', false);
}

// 初始化
loadMeterList();
</script>

<template>
  <Drawer
    title="添加总分表关系"
    :width="500"
    :visible="props.open"
    @close="handleCancel"
    :destroy-on-close="true"
  >
    <Spin :spinning="loading">
      <Form
        ref="formRef"
        :model="formState"
        layout="vertical"
      >
        <Form.Item
          label="总表"
          name="parentMeterId"
          :rules="[{ required: true, message: '请选择总表' }]"
        >
          <Select
            v-model:value="formState.parentMeterId"
            placeholder="请选择总表"
            :options="meterOptions"
            show-search
            :filter-option="(input, option) =>
              option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0"
          />
        </Form.Item>

        <Form.Item
          label="分表"
          name="childMeterId"
          :rules="[{ required: true, message: '请选择分表' }]"
        >
          <Select
            v-model:value="formState.childMeterId"
            placeholder="请选择分表"
            :options="meterOptions"
            show-search
            :filter-option="(input, option) =>
              option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0"
          />
        </Form.Item>
      </Form>
    </Spin>

    <template #footer>
      <div style="text-align: right">
        <Button style="margin-right: 8px" @click="handleCancel">取消</Button>
        <Button type="primary" @click="handleSubmit">确定</Button>
      </div>
    </template>
  </Drawer>
</template>
