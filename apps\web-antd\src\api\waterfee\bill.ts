import type { BillModel, BillParams } from './model/billModel';

import type { ID, PageResult } from '#/api/common';

import { requestClient } from '#/api/request';

enum Api {
  adjust = '/waterfee/bill/adjust',
  billIssueList = '/waterfee/bill/issue/list',
  billList = '/waterfee/bill/list',
  billListDetail = '/waterfee/bill/listDetail',
  issue = '/waterfee/bill/issue',
  pay = '/waterfee/bill/pay',
  pendingIssueList = '/waterfee/bill/pending-issue/list',
  root = '/waterfee/bill',
}

/**
 * 获取账单列表
 * @param params 查询参数
 * @returns 账单列表
 */
export function billList(params?: BillParams) {
  return requestClient.get<PageResult<BillModel>>(Api.billList, { params });
}

/**
 * 获取账单列表
 * @param params 查询参数
 * @returns 账单列表
 */
export function billListDetail(params?: BillParams) {
  return requestClient.get<PageResult<BillModel>>(Api.billListDetail, { params });
}

/**
 * 获取账单详情
 * @param id 账单ID
 * @returns 账单详情
 */
export function billInfo(id: ID) {
  return requestClient.get<BillModel>(`${Api.root}/${id}`);
}

/**
 * 新增账单
 * @param data 账单数据
 * @returns void
 */
export function billAdd(data: Partial<BillModel>) {
  return requestClient.postWithMsg<void>(Api.root, data);
}

/**
 * 修改账单
 * @param data 账单数据
 * @returns void
 */
export function billUpdate(data: Partial<BillModel>) {
  return requestClient.putWithMsg<void>(Api.root, data);
}

/**
 * 删除账单
 * @param ids 账单ID或ID数组
 * @returns void
 */
export function billRemove(ids: Array<ID> | ID) {
  return requestClient.deleteWithMsg<void>(`${Api.root}/${ids}`);
}

/**
 * 调整账单（支持调整用量、金额等）
 * @param data 调整数据
 * @returns void
 */
export function billAdjustConsumption(data: Partial<BillModel>) {
  return requestClient.putWithMsg<void>(`${Api.adjust}`, data);
}

/**
 * 支付账单
 * @param billId 账单ID
 * @param amount 支付金额
 * @returns void
 */
export function billPay(billId: ID, amount: number) {
  return requestClient.putWithMsg<void>(`${Api.pay}/${billId}`, { amount });
}

/**
 * 获取待发行账单列表
 * @param params 查询参数
 * @returns 待发行账单列表
 */
export function billPendingIssueList(params?: BillParams) {
  return requestClient.get<PageResult<BillModel>>(Api.pendingIssueList, {
    params,
  });
}

/**
 * 发行账单
 * @param ids 账单ID或ID数组
 * @returns void
 */
export function billIssue(ids: Array<ID> | ID) {
  return requestClient.putWithMsg<void>(`${Api.issue}/${ids}`);
}

/**
 * 获取分组列表
 * @param params 查询参数
 * @returns 分组账单列表
 */
export function billIssueList(params?: BillParams) {
  return requestClient.get<PageResult<BillModel>>(Api.billIssueList, {
    params,
  });
}
