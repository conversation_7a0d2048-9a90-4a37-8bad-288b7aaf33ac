export interface UserDeactivateRecordVO {
  /**
   * 主键
   */
  deactivateId: number | string;

  /**
   * 用户ID
   */
  userId: number | string;

  /**
   * 停用原因
   */
  deactivateReason: string;

  /**
   * 停用时间
   */
  cancellationTime: string;
}

export interface UserDeactivateRecordForm extends BaseEntity {
  /**
   * 主键
   */
  deactivateId?: number | string;

  /**
   * 用户ID
   */
  userIds: Array<number | string>;

  /**
   * 停用原因
   */
  deactivateReason?: string;

  /**
   * 停用时间
   */
  cancellationTime?: string;
}

export interface UserDeactivateRecordQuery extends PageQuery {
  /**
   * 用户编号或用户名
   */
  userNoOrUserName?: string;

  /**
   * 查询开始时间
   */
  startTime?: string;

  /**
   * 查询结束时间
   */
  endTime?: string;
}
