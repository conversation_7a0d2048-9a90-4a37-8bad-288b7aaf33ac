export interface SurchargeConfigsVO {
  /**
   * 
   */
  id: string | number;

  /**
   * 附加费名称
   */
  name: string;

  /**
   * 计算方式 (e.g., fixed_amount, meter_reading)
   */
  calculationMethod: string;

  /**
   * 固定金额 (if calculation_method is fixed_amount)
   */
  fixedAmount: number;

  /**
   * 比例(%) (e.g., if calculation_method is meter_reading)
   */
  ratePercent: number;

  /**
   * 附加费类别
   */
  category: string;

  /**
   * 备注
   */
  remarks: string;

}

export interface SurchargeConfigsForm extends BaseEntity {
  /**
   * 
   */
  id?: string | number;

  /**
   * 附加费名称
   */
  name?: string;

  /**
   * 计算方式 (e.g., fixed_amount, meter_reading)
   */
  calculationMethod?: string;

  /**
   * 固定金额 (if calculation_method is fixed_amount)
   */
  fixedAmount?: number;

  /**
   * 比例(%) (e.g., if calculation_method is meter_reading)
   */
  ratePercent?: number;

  /**
   * 附加费类别
   */
  category?: string;

  /**
   * 备注
   */
  remarks?: string;

}

export interface SurchargeConfigsQuery extends PageQuery {

  /**
   * 附加费名称
   */
  name?: string;

  /**
   * 计算方式 (e.g., fixed_amount, meter_reading)
   */
  calculationMethod?: string;

  /**
   * 固定金额 (if calculation_method is fixed_amount)
   */
  fixedAmount?: number;

  /**
   * 比例(%) (e.g., if calculation_method is meter_reading)
   */
  ratePercent?: number;

  /**
   * 附加费类别
   */
  category?: string;

  /**
   * 备注
   */
  remarks?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



