import { requestClient } from '#/api/request';

// 抄表记录API
enum Api {
  auditRecord = '/waterfee/meterReadingRecord/audit', // 审核抄表记录
  auditRecordsByBook = '/waterfee/meterReadingRecord/audit/book', // 按表册审核抄表记录
  historyReading = '/waterfee/meterReadingRecord/history', // 获取抄表历史记录
  latestReading = '/waterfee/meterReadingRecord/latest', // 获取最新抄表记录
}

// 最新抄表记录模型
export interface LatestReadingModel {
  lastReading?: number; // 上期读数
  oldMeterStopReading?: number; // 旧表止数
  readingTime?: string; // 抄表时间
  [key: string]: any;
}

// 获取最新抄表记录
export function getLatestReading(meterNo: string) {
  return requestClient.get<LatestReadingModel>(
    `${Api.latestReading}/${meterNo}`,
  );
}

// 历史抄表记录模型
export interface HistoryReadingModel {
  id?: string;
  lastReading?: number; // 上期抄表读数
  lastReadingTime?: string; // 上期抄表时间
  currentReading?: number; // 本期抄表读数
  readingTime?: string; // 本期抄表时间
  oldMeterStopReading?: number; // 旧表止数
  waterUsage?: number; // 本期水量
  [key: string]: any;
}

// 获取历史抄表记录
export function getHistoryReadings(meterNo: string) {
  return requestClient.get<HistoryReadingModel[]>(
    `${Api.historyReading}/${meterNo}`,
  );
}

/**
 * 审核单条抄表记录
 * @param recordId 抄表记录ID
 * @returns Promise<void>
 */
export function auditRecord(recordId: number | string) {
  return requestClient.put<void>(`${Api.auditRecord}/${recordId}`);
}

/**
 * 按表册审核抄表记录
 * @param meterBookId 表册ID
 * @param taskId 任务ID
 * @returns Promise<void>
 */
export function auditRecordsByBook(
  meterBookId: number | string,
  taskId: number | string,
) {
  return requestClient.put<void>(Api.auditRecordsByBook, null, {
    params: {
      meterBookId,
      taskId,
    },
  });
}
