import type { PageQuery } from '#/api/common';
import type {
  UserDeactivateRecordForm,
  UserDeactivateRecordQuery,
  UserDeactivateRecordVO,
} from '#/api/waterfee/user/deactivateRecord/model.d';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

enum Api {
  list = '/waterfee/userDeactivateRecord/list',
  root = '/waterfee/userDeactivateRecord',
}

/**
 * 用水用户报停记录导出
 * @param data data
 * @returns void
 */
export function UserDeactivateRecordExport(
  data: Partial<UserDeactivateRecordForm>,
) {
  return commonExport(`${Api.root}/export`, data);
}

/**
 * 查询用水用户报停记录列表
 * @param params 查询参数
 * @returns 用水用户报停记录列表
 */
export function listUserDeactivateRecord(
  params?: PageQuery & UserDeactivateRecordQuery,
) {
  return requestClient.get<UserDeactivateRecordVO>(Api.list, { params });
}

/**
 * 查询用水用户报停记录详细
 * @param deactivateId 用水用户报停记录ID
 * @returns 用水用户报停记录信息
 */
export function getUserDeactivateRecord(deactivateId: number | string) {
  return requestClient.get<UserDeactivateRecordForm>(
    `${Api.root}/${deactivateId}`,
  );
}

/**
 * 新增用水用户报停记录
 * @param data 新增数据
 * @returns void
 */
export function addUserDeactivateRecord(data: UserDeactivateRecordForm) {
  return requestClient.postWithMsg<void>(Api.root, data);
}

/**
 * 修改用水用户报停记录
 * @param data 修改数据
 * @returns void
 */
export function updateUserDeactivateRecord(data: UserDeactivateRecordForm) {
  return requestClient.putWithMsg<void>(Api.root, data);
}

/**
 * 删除用水用户报停记录
 * @param deactivateId 用水用户报停记录ID或ID数组
 * @returns void
 */
export function delUserDeactivateRecord(
  deactivateId: Array<number | string> | number | string,
) {
  return requestClient.deleteWithMsg<void>(`${Api.root}/${deactivateId}`);
}
