import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { getPopupContainer } from '@vben/utils';
import { getDictOptions } from '#/utils/dict';
import { renderDict } from '#/utils/render';
import { h } from 'vue';

// 获取标签颜色的背景色
function getTagColor(color: string): string {
  const colorMap: Record<string, string> = {
    'blue': '#1890ff',
    'red': '#ff4d4f',
    'orange': '#faad14',
    'green': '#52c41a',
    'purple': '#722ed1',
    'default': '#d9d9d9'
  };
  return colorMap[color] || '#d9d9d9';
}

// 查询表单架构
export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入水表编号',
    },
    fieldName: 'meterNo',
    label: '水表编号',
  },
  {
    component: 'Select',
    componentProps: {
      getPopupContainer,
      options: getDictOptions('waterfee_meter_alert_type'),
      placeholder: '请选择告警类型',
    },
    fieldName: 'alertType',
    label: '告警类型',
  },
  {
    component: 'Select',
    componentProps: {
      getPopupContainer,
      options: getDictOptions('waterfee_meter_alert_level'),
      placeholder: '请选择告警级别',
    },
    fieldName: 'alertLevel',
    label: '告警级别',
  },
  {
    component: 'Select',
    componentProps: {
      getPopupContainer,
      options: getDictOptions('waterfee_meter_alert_status'),
      placeholder: '请选择处理状态',
    },
    fieldName: 'alertStatus',
    label: '处理状态',
  },
  {
    component: 'RangePicker',
    componentProps: {
      getPopupContainer,
      placeholder: ['开始时间', '结束时间'],
      showTime: true,
    },
    fieldName: 'alertTimeRange',
    label: '告警时间',
  },
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入用户编号',
    },
    fieldName: 'userNo',
    label: '用户编号',
  },
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入用户名称',
    },
    fieldName: 'userName',
    label: '用户名称',
  },
];

// 表格列配置
export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 40 },
  {
    title: '序号',
    width: 50,
    cellRender: { name: 'SafeIndex' },
  },
  {
    field: 'alertId',
    title: '告警ID',
    visible: false,
  },
  {
    field: 'meterNo',
    title: '水表编号',
    width: 150,
    align: 'center',
  },
  {
    field: 'userNo',
    title: '用户编号',
    width: 90,
    align: 'center',
    slots: {
      default: ({ row }) => {
        if (!row.userNo) {
          return h('span', { style: { color: '#ff4d4f' } }, '未关联');
        }
        return row.userNo;
      },
    },
  },
  {
    field: 'userName',
    title: '用户名称',
    width: 90,
    align: 'center',
  },
  {
    field: 'alertType',
    title: '告警类型',
    width: 90,
    align: 'center',
    slots: {
      default: ({ row }) => {
        return renderDict(row.alertType, 'waterfee_meter_alert_type');
      },
    },
  },
  {
    field: 'alertContent',
    title: '告警内容',
    minWidth: 100,
    align: 'left',
  },
  {
    field: 'alertLevel',
    title: '告警级别',
    width: 90,
    align: 'center',
    slots: {
      default: ({ row }) => {
        const level = row.alertLevel;
        let color = '';
        let text = renderDict(level, 'waterfee_meter_alert_level');

        switch (level) {
          case '1':
            color = 'blue';
            break;
          case '2':
            color = 'orange';
            break;
          case '3':
            color = 'red';
            break;
          case '4':
            color = 'purple';
            break;
          default:
            color = 'default';
        }

        // 使用自定义样式而不是 Tag 组件，避免重叠
        return h('div', {
          style: {
            color: getTagColor(color),
            fontWeight: 'bold',
            width: '100%',
            textAlign: 'center',
            display: 'inline-block',
            whiteSpace: 'nowrap',
            overflow: 'hidden',
            textOverflow: 'ellipsis'
          }
        }, text);
      },
    },
  },
  {
    field: 'alertStatus',
    title: '处理状态',
    width: 90,
    align: 'center',
    slots: {
      default: ({ row }) => {
        const status = row.alertStatus;
        let color = '';
        let text = renderDict(status, 'waterfee_meter_alert_status');

        switch (status) {
          case '0':
            color = 'red';
            break;
          case '1':
            color = 'orange';
            break;
          case '2':
            color = 'green';
            break;
          default:
            color = 'default';
        }

        // 使用自定义样式而不是 Tag 组件，避免重叠
        return h('div', {
          style: {
            color: getTagColor(color),
            fontWeight: 'bold',
            width: '100%',
            textAlign: 'center',
            display: 'inline-block',
            whiteSpace: 'nowrap',
            overflow: 'hidden',
            textOverflow: 'ellipsis'
          }
        }, text);
      },
    },
  },
  {
    field: 'alertTime',
    title: '告警时间',
    width: 140,
    align: 'center',
  },
  {
    field: 'businessAreaName',
    title: '营业区域',
    width: 100,
    align: 'center',
    slots: {
      default: ({ row }) => {
        return row.businessAreaName || '未分配';
      },
    },
  },
  {
    field: 'manufacturer',
    title: '水表厂家',
    width: 90,
    align: 'center',
    slots: {
      default: ({ row }) => {
        return renderDict(row.manufacturer, 'meter_factory');
      },
    },
  },
  {
    field: 'caliber',
    title: '水表口径',
    width: 90,
    align: 'center',
    slots: {
      default: ({ row }) => {
        return renderDict(row.caliber, 'dnmm');
      },
    },
  },
  {
    field: 'processTime',
    title: '处理时间',
    width: 140,
    align: 'center',
  },
  {
    field: 'processUser',
    title: '处理人',
    width: 80,
    align: 'center',
  },
  {
    field: 'processResult',
    title: '处理结果',
    minWidth: 120,
    align: 'left',
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 200,
    align: 'center',
  },
];
