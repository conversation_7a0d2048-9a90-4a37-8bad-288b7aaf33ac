<script setup lang="ts">
import type { SelectProps, TableColumnType } from 'ant-design-vue'; // 添加 SelectProps 类型 (Add SelectProps type)
import type { Key } from 'ant-design-vue/lib/table/interface';

import { onMounted, reactive, ref } from 'vue'; // 添加 onMounted 生命周期钩子

import {
  DeleteOutlined,
  EditOutlined,
  PlusOutlined,
  ReloadOutlined,
  SearchOutlined,
} from '@ant-design/icons-vue';
import {
  Button as AButton,
  Card as ACard,
  Form as AForm,
  FormItem as AFormItem,
  Input as AInput,
  Popconfirm as APopconfirm,
  Select as ASelect,
  Space as ASpace,
  Table as ATable,
  // 移除 Drawer 相关的 imports: ADrawer, ASwitch, AInputNumber, ATextarea, ARow, ACol (Removed Drawer related imports: ADrawer, ASwitch, AInputNumber, ATextarea, ARow, ACol)
} from 'ant-design-vue';

// 移除 useMessage 引入
import {
  addStandardPrice,
  listStandardPrice,
  updateStandardPrice,
} from '#/api/waterfee/price/standardPrice'; // 添加 listStandardPrice (Add listStandardPrice)
import { getDictOptions } from '#/utils/dict'; // 添加 getDictOptions 工具函数 (Add getDictOptions tool function)
import { renderDict } from '#/utils/render'; // 导入 renderDict 渲染工具

import StandardPriceDrawer from './StandardPriceDrawer.vue'; // 导入新的组件

// 搜索参数
const searchParams = reactive({
  name: '',
  waterUseType: undefined,
});

// 搜索表单选择的选项
const searchWaterUsageTypeOptions = ref<SelectProps['options']>([]);

// 定义表格数据行的接口
interface TableDataType {
  id: string;
  waterUseType: string;
  name: string;
  price: number | string;
  penalty: string;
  surcharge: string;
  description?: string; // Make description optional
  penaltyEnabled?: boolean; // Add fields for drawer form
  penaltyId?: string;
  additionalEnabled?: boolean;
  additionalFeeIds?: string | string[];
}

// Form data type for the drawer (can be defined here or in the drawer component)
type FormDataType = Omit<TableDataType, 'id'> & { id?: number | string };

// 表格列定义，显式类型
const columns: TableColumnType<TableDataType>[] = [
  { title: '序号', dataIndex: 'id', key: 'id', width: 60, align: 'center' },
  {
    title: '用水性质',
    dataIndex: 'waterUseType',
    key: 'waterUseType',
    align: 'center',
    customRender: ({ text }) => {
      return renderDict(text, 'water_use_type');
    },
  },
  {
    title: '价格名称',
    dataIndex: 'name',
    key: 'name',
    align: 'center',
  },
  {
    title: '单价(元)',
    dataIndex: 'price',
    key: 'price',
    align: 'center',
  },
  // { title: '违约金', dataIndex: 'penalty', key: 'penalty', align: 'center' },
  // {
  //   title: '附加费',
  //   dataIndex: 'surcharge',
  //   key: 'surcharge',
  //   align: 'center',
  // },
  {
    title: '备注',
    dataIndex: 'description',
    key: 'description',
    align: 'center',
  },
  { title: '操作', key: 'action', width: 150, align: 'center' },
];

// 表格数据（示例），显式类型
const tableData = ref<TableDataType[]>([
  {
    id: '01',
    waterUseType: '1',
    name: '居民标准价',
    price: 2.5,
    penalty: '按固定金额10元',
    surcharge: '污水处理费5元',
    description: '',
  },
  // Add more data rows as needed
]);

// 表格选择的行键 - 使用 Key 类型
const selectedRowKeys = ref<Key[]>([]);

// Handle row selection change - Use Key type
const onSelectChange = (keys: Key[]) => {
  selectedRowKeys.value = keys;
};

// Function to fetch table data (获取表格数据)
const getList = async () => {
  console.log('Fetching list with params:', searchParams);
  try {
    const response = await listStandardPrice({
      ...searchParams,
    });
    // API returns { rows: StandardPriceVO[], total: number }
    tableData.value = response.rows.map((item) => ({
      ...item,
      id: item.id,
      waterUseType: item.waterUseType, // Assuming default value (假设默认值)
      name: item.name,
      price: item.price, // Keep price as number (保持价格为数字)
      penalty: item.penaltyEnabled ? `关联: ${item.penaltyName}` : '未关联',
      surcharge: item.additionalEnabled
        ? `关联: ${item.additionalFeeNames}`
        : '未关联',
      description: item.description,
    }));
  } catch (error) {
    console.error('Failed to fetch list:', error);
    // Error message likely handled globally (错误消息可能由全局处理)
  }
};

// Fetch options and initial data when component mounts (当组件挂载时获取选项和初始数据)
onMounted(async () => {
  // Fetch options for search dropdown (获取搜索下拉框的选项)
  searchWaterUsageTypeOptions.value = await getDictOptions('water_use_type');
  // Fetch initial table data (获取初始表格数据)
  getList();
});

// Handle search action (处理搜索操作)
const handleSearch = () => {
  getList(); // Call getList to fetch data with current search params (调用 getList 获取带有当前搜索参数的数据)
};

// Handle reset action (处理重置操作)
const handleReset = () => {
  searchParams.name = '';
  searchParams.waterUseType = undefined;
  getList(); // Call getList to fetch data with reset params (调用 getList 获取带有重置参数的数据)
};

// --- Drawer State Management --- (--- 抽屉状态管理 ---)
const drawerVisible = ref(false);
const isEditing = ref(false);
const currentRecord = ref<null | TableDataType>(null);

// Handle add action - Opens the drawer in add mode (处理添加操作 - 在添加模式下打开抽屉)
const handleAdd = () => {
  isEditing.value = false;
  currentRecord.value = null; // No record for adding (没有要添加的记录)
  drawerVisible.value = true;
  console.log('Open drawer for adding');
};

// Handle edit action - Opens the drawer in edit mode with the selected record (处理编辑操作 - 在编辑模式下打开抽屉，并选择记录)
const handleEdit = (record: TableDataType) => {
  isEditing.value = true;
  currentRecord.value = {
    ...record,
    additionalFeeIds: Array.isArray(record.additionalFeeIds)
      ? record.additionalFeeIds
      : record.additionalFeeIds?.split(','),
  };
  drawerVisible.value = true;
  console.log('Open drawer for editing:', record);
};

// Handle save action emitted from the drawer (处理从抽屉发出的保存操作)
const handleDrawerSave = async (data: FormDataType) => {
  console.log('Received save event from drawer:', data);
  try {
    if (isEditing.value && currentRecord.value) {
      // Update existing record logic - Call API (更新现有记录逻辑 - 调用 API)
      // Ensure the ID is included in the data payload for update (确保 ID 包含在要更新的数据中)
      const updateData = { ...data, id: currentRecord.value.id };
      await updateStandardPrice(updateData);
      // Message likely handled globally by requestClient (消息可能由 requestClient 全局处理)
    } else {
      // Add new record logic - Call API (添加新记录逻辑 - 调用 API)
      await addStandardPrice(data);
      // Message likely handled globally by requestClient (消息可能由 requestClient 全局处理)
    }
    drawerVisible.value = false; // Close drawer after successful save (保存成功后关闭抽屉)
    // Refresh table data by calling getList (通过调用 getList 刷新表格数据)
    await getList(); // Fetch updated data (获取更新后的数据)
  } catch (error) {
    console.error('Save failed:', error);
    // Error message likely handled globally by requestClient (错误消息可能由 requestClient 全局处理)
    // Error message is likely handled by the requestClient interceptor (错误消息可能由 requestClient 拦截器处理)
    // createMessage.error('保存失败'); // Optional: show specific message here (可选：在此处显示特定消息)
  }
};

// Handle cancel action emitted from the drawer (optional, drawer handles its own close) (处理从抽屉发出的取消操作（可选，抽屉处理自己的关闭）)
const handleDrawerCancel = () => {
  console.log('Drawer cancelled');
  // drawerVisible is already set to false by the drawer component via v-model:open (drawerVisible 已经通过 v-model:open 由抽屉组件设置为 false)
};

// Handle delete action - Use correct type for record (处理删除操作 - 为记录使用正确的类型)
const handleDelete = (record: TableDataType) => {
  console.log('Delete record:', record);
  // Implement delete logic here, e.g., call API to delete (在此处实现删除逻辑，例如，调用 API 进行删除)
  // Remove from tableData for demo purposes (为了演示目的，从 tableData 中删除)
  tableData.value = tableData.value.filter((item) => item.id !== record.id);
};
</script>

<template>
  <div class="p-4">
    <ACard class="mb-4" :bordered="false">
      <AForm layout="block">
        <div
          class="grid grid-cols-1 gap-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"
        >
          <AFormItem label="价格名称" class="mb-2">
            <AInput
              v-model:value="searchParams.name"
              placeholder="请输入价格名称"
            />
          </AFormItem>
          <AFormItem label="用水性质" class="mb-2">
            <ASelect
              v-model:value="searchParams.waterUseType"
              placeholder="请选择用水性质"
              style="width: 100%"
              :options="searchWaterUsageTypeOptions"
              allow-clear
            />
          </AFormItem>
          <div style="grid-column: -2 / -1; margin-left: auto">
            <AFormItem class="mb-2">
              <ASpace>
                <AButton @click="handleReset">
                  <ReloadOutlined /> 重置
                </AButton>
                <AButton type="primary" @click="handleSearch">
                  <SearchOutlined /> 查询
                </AButton>
              </ASpace>
            </AFormItem>
          </div>
        </div>

        <!-- Removed Add button from here -->
      </AForm>
    </ACard>
    <ACard
      title="标准价格配置"
      :bordered="false"
      :head-style="{ borderBottom: 0 }"
    >
      <template #extra>
        <AButton type="primary" @click="handleAdd">
          <PlusOutlined /> 添加
        </AButton>
      </template>

      <ATable
        :columns="columns"
        :data-source="tableData"
        :row-selection="{
          selectedRowKeys,
          onChange: onSelectChange,
        }"
        row-key="id"
        size="small"
        bordered
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <ASpace>
              <!-- Explicitly cast record to TableDataType -->
              <AButton type="link" @click="handleEdit(record as TableDataType)">
                <EditOutlined /> 编辑
              </AButton>
              <APopconfirm
                title="确定删除吗?"
                ok-text="确定"
                cancel-text="取消"
                @confirm="handleDelete(record as TableDataType)"
              >
                <AButton type="link" danger> <DeleteOutlined /> 删除 </AButton>
              </APopconfirm>
            </ASpace>
          </template>
        </template>
      </ATable>
    </ACard>

    <!-- Use the extracted Drawer Component -->
    <StandardPriceDrawer
      v-model:visible="drawerVisible"
      :is-editing="isEditing"
      :record="currentRecord"
      @save="handleDrawerSave"
      @cancel="handleDrawerCancel"
    />
  </div>
</template>

<style scoped>
/* Add any specific styles if needed (如果需要，添加特定的样式) */
.ml-auto {
  margin-left: auto;
}
</style>
