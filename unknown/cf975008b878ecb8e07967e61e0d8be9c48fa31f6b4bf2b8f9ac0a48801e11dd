<script setup lang="ts">
import { computed, ref, watch } from 'vue';

import { useVbenForm } from '#/adapter/form';
import { meterInfoByNo } from '#/api/waterfee/meter';
import { addMeterChange, getMeterChangeDetail, updateMeterChange } from '#/api/waterfee/meter/change';
import { getLatestReading } from '#/api/waterfee/meterReadingRecord';
import { preserveBigInt } from '#/utils/json-bigint';
import { getDictOptions } from '#/utils/dict';

import { Button, Drawer, Form, message, Space, Spin } from 'ant-design-vue';
import { formSchema } from '../change.data';

const props = defineProps({
  open: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    default: '',
  },
  readonly: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['update:open', 'reload']);

// 加载状态
const loading = ref(false);
// 提交状态
const submitting = ref(false);

// 标题
const title = computed(() => {
  if (props.readonly) {
    return '换表详情';
  }
  return props.id ? '编辑换表记录' : '添加换表记录';
});

// 初始化表单
const [BasicForm, formApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'col-span-2',
    labelWidth: 100,
  },
  schema: formSchema(),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-2',
});

// 关闭抽屉
function handleCancel() {
  emit('update:open', false);
}

// 提交表单
async function handleSubmit() {
  if (props.readonly) {
    handleCancel();
    return;
  }

  try {
    submitting.value = true;
    // 表单验证
    const values = await formApi.validate();
    console.log('表单数据:', values);

    if (props.id) {
      // 更新换表记录
      await updateMeterChange({
        ...values,
        changeId: props.id,
      });
      message.success('换表记录更新成功');
    } else {
      // 添加换表记录
      await addMeterChange(values);
      message.success('换表记录添加成功');
    }

    // 关闭抽屉并刷新列表
    handleCancel();
    emit('reload');
  } catch (error) {
    console.error('提交表单失败:', error);
    message.error(`提交失败：${error.message || '未知错误'}`);
  } finally {
    submitting.value = false;
  }
}

// 查询旧表信息
async function handleQueryOldMeter() {
  try {
    const meterNo = formApi.getFieldValue('oldMeterNo');
    if (!meterNo) {
      message.warning('请输入旧表编号');
      return;
    }

    loading.value = true;
    // 查询水表信息
    const meterInfo = await meterInfoByNo(meterNo);
    if (!meterInfo) {
      message.warning('未找到水表信息');
      return;
    }

    // 设置旧表ID
    formApi.setFieldValue('oldMeterId', meterInfo.meterId);

    // 查询最新抄表记录
    try {
      const latestReading = await getLatestReading(meterNo);
      if (latestReading && latestReading.lastReading !== undefined) {
        formApi.setFieldValue('oldMeterReading', latestReading.lastReading);
      } else {
        // 如果没有抄表记录，使用水表初始读数
        formApi.setFieldValue('oldMeterReading', meterInfo.initialReading || 0);
      }
    } catch (error) {
      console.error('获取最新抄表记录失败:', error);
      // 使用水表初始读数
      formApi.setFieldValue('oldMeterReading', meterInfo.initialReading || 0);
    }
  } catch (error) {
    console.error('查询旧表信息失败:', error);
    message.error(`查询失败：${error.message || '未知错误'}`);
  } finally {
    loading.value = false;
  }
}

// 查询新表信息
async function handleQueryNewMeter() {
  try {
    const meterNo = formApi.getFieldValue('newMeterNo');
    if (!meterNo) {
      message.warning('请输入新表编号');
      return;
    }

    loading.value = true;
    // 查询水表信息
    const meterInfo = await meterInfoByNo(meterNo);
    if (!meterInfo) {
      message.warning('未找到水表信息');
      return;
    }

    // 设置新表ID
    formApi.setFieldValue('newMeterId', meterInfo.meterId);
    // 设置新表初始读数
    formApi.setFieldValue('newMeterReading', meterInfo.initialReading || 0);
  } catch (error) {
    console.error('查询新表信息失败:', error);
    message.error(`查询失败：${error.message || '未知错误'}`);
  } finally {
    loading.value = false;
  }
}

// 初始化数据
async function initData() {
  if (!props.open) return;

  try {
    loading.value = true;

    // 重置表单
    await formApi.resetForm();

    // 如果是详情模式，加载详情数据
    if (props.id) {
      const detail = await getMeterChangeDetail(props.id);
      if (detail) {
        // 处理大整数
        const safeDetail = preserveBigInt(detail);
        // 设置表单数据
        await formApi.setValues(safeDetail);
      }
    } else {
      // 新增模式，设置默认值
      // 格式化当前日期时间为 YYYY-MM-DD HH:mm:ss
      const now = new Date();
      const formattedDateTime = now.toISOString().replace('T', ' ').substring(0, 19);

      await formApi.setValues({
        changeTime: formattedDateTime, // 当前日期时间
        changeType: '2', // 默认换表类型
      });
    }
  } catch (error) {
    console.error('初始化数据失败:', error);
    message.error(`初始化失败：${error.message || '未知错误'}`);
  } finally {
    loading.value = false;
  }
}

// 监听抽屉打开状态
watch(
  () => props.open,
  (val) => {
    if (val) {
      initData();
    }
  },
  { immediate: true }
);
</script>

<template>
  <Drawer
    :title="title"
    :open="open"
    :width="720"
    :maskClosable="false"
    :closable="true"
    @close="emit('update:open', false)"
    :afterVisibleChange="(visible) => { if (!visible) emit('update:open', false); }"
    :destroyOnClose="true"
  >
    <Spin :spinning="loading">
      <div class="p-4">
        <BasicForm ref="formRef" :disabled="readonly">
          <template #oldMeterNo-suffix v-if="!readonly">
            <Button type="link" @click="handleQueryOldMeter">查询</Button>
          </template>
          <template #newMeterNo-suffix v-if="!readonly">
            <Button type="link" @click="handleQueryNewMeter">查询</Button>
          </template>
        </BasicForm>
      </div>
    </Spin>
    <template #footer>
      <div class="flex justify-end">
        <Space>
          <Button @click="handleCancel">{{ readonly ? '关闭' : '取消' }}</Button>
          <Button v-if="!readonly" type="primary" :loading="submitting" @click="handleSubmit">
            提交
          </Button>
        </Space>
      </div>
    </template>
  </Drawer>
</template>

<style scoped>
/* 可以添加自定义样式 */
</style>
