<script setup lang="ts">
import type { SelectProps } from 'ant-design-vue';

import { computed, onMounted, reactive, ref, watch } from 'vue';

import {
  <PERSON><PERSON> as <PERSON>utton,
  Col as ACol,
  Drawer as <PERSON><PERSON>er,
  Form as AForm,
  FormItem as AFormItem,
  Input as AInput,
  InputNumber as AInputNumber,
  Row as ARow,
  Select as ASelect,
  Space as ASpace,
  Switch as ASwitch,
  Textarea as ATextarea,
} from 'ant-design-vue';

import { listLiquidatedDamagesConfigs } from '#/api/waterfee/price/liquidatedDamagesConfigs';
import { listSurchargeConfigs } from '#/api/waterfee/price/surchargeConfigs';
import { getDictOptions } from '#/utils/dict';

// Define interface for table data row (can be shared or redefined)
interface TableDataType {
  id: string;
  waterUseType: string;
  name: string;
  price: number | string;
  penalty: string;
  surcharge: string;
  remarks?: string;
  penaltyEnabled?: boolean;
  penaltyId?: string;
  additionalEnabled?: boolean;
  additionalFeeIds?: string | string[];
  penaltyName?: string;
  additionalFeeNames?: string;
}

// Form data type for the drawer
type FormDataType = Omit<TableDataType, 'id'> & { id?: number | string };

const props = defineProps<{
  isEditing: boolean;
  record: null | TableDataType;
  visible: boolean;
}>();

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'save', data: FormDataType): void;
  (e: 'cancel'): void;
}>();

// Form data for the drawer
const formData = reactive<FormDataType>({
  waterUseType: '',
  name: '',
  price: '',
  penalty: '', // Add initial value
  surcharge: '', // Add initial value
  penaltyEnabled: false,
  penaltyId: undefined,
  additionalEnabled: false,
  additionalFeeIds: '',
  penaltyName: '',
  additionalFeeNames: '',
  remarks: '',
});

// Options for selects
const waterUseTypeOptions = ref<SelectProps['options']>([]);
const penaltyOptions = ref<SelectProps['options']>([]);
const additionalFeeOptions = ref<SelectProps['options']>([]);

// Fetch options when component mounts
onMounted(async () => {
  // Fetch water usage types
  waterUseTypeOptions.value = await getDictOptions('water_use_type');

  // Fetch penalty options
  try {
    const response = await listLiquidatedDamagesConfigs(); // Assuming no params needed, adjust if necessary
    // Assuming the API returns an array like [{ id: '...', name: '...' }]
    // Adjust the mapping based on the actual API response structure
    penaltyOptions.value = response.rows.map((item) => ({
      label: item.name, // Use the appropriate field for the label
      value: item.id, // Use the appropriate field for the value
    }));

    const response2 = await listSurchargeConfigs(); // Assuming no params needed, adjust if necessary
    // Assuming the API returns an array like [{ id: '...', name: '...' }]
    // Adjust the mapping based on the actual API response structure
    additionalFeeOptions.value = response2.rows.map((item) => ({
      label: item.name, // Use the appropriate field for the label
      value: item.id, // Use the appropriate field for the value
    }));
  } catch (error) {
    console.error('Failed to fetch penalty options:', error);
    // Handle error appropriately, maybe show a message to the user
  }
});

// Reset form data
const resetFormData = () => {
  formData.id = undefined;
  formData.waterUseType = '';
  formData.name = '';
  formData.price = '';
  formData.penalty = ''; // Reset penalty
  formData.surcharge = ''; // Reset surcharge
  formData.penaltyEnabled = false;
  formData.penaltyId = undefined;
  formData.additionalEnabled = false;
  formData.additionalFeeIds = '';
  formData.penaltyName = '';
  formData.additionalFeeNames = '';
  formData.remarks = '';
};

// Watch for changes in props to update form data
watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      if (props.isEditing && props.record) {
        // Edit mode: Populate form with record data
        Object.assign(formData, props.record);
        // Ensure boolean values for switches
        formData.penaltyEnabled = !!props.record.penaltyEnabled;
        formData.additionalEnabled = !!props.record.additionalEnabled;
      } else {
        // Add mode: Reset form
        resetFormData();
      }
    }
  },
);

// Computed property for drawer title
const drawerTitle = computed(() =>
  props.isEditing ? '编辑标准价格' : '创建标准价格',
);

// Handle save action
const handleSave = () => {
  formData.additionalFeeIds = formData.additionalFeeIds?.toString() || '';
  emit('save', { ...formData }); // Emit a copy of the data
};

// Handle cancel action
const handleCancel = () => {
  emit('update:visible', false);
  emit('cancel');
};
</script>

<template>
  <ADrawer
    :open="props.visible"
    :title="drawerTitle"
    width="500"
    @close="handleCancel"
  >
    <AForm :model="formData" layout="vertical">
      <AFormItem label="用水性质" required>
        <ASelect
          v-model:value="formData.waterUseType"
          placeholder="请选择"
          :options="waterUseTypeOptions"
        />
      </AFormItem>
      <AFormItem label="价格名称" required>
        <AInput v-model:value="formData.name" placeholder="请输入" />
      </AFormItem>
      <AFormItem label="标准单价" required>
        <AInputNumber
          v-model:value="formData.price"
          placeholder="请输入"
          style="width: 100%"
          :min="0"
          :precision="2"
        />
      </AFormItem>

      <!-- <ARow :gutter="16">
        <ACol :span="12">
          <AFormItem label="关联违约金">
            <ASwitch v-model:checked="formData.penaltyEnabled" />
          </AFormItem>
        </ACol>
        <ACol :span="12">
          <AFormItem v-if="formData.penaltyEnabled">
            <ASelect
              v-model:value="formData.penaltyId"
              placeholder="选择选项"
              :options="penaltyOptions"
              :loading="!penaltyOptions?.length"
              allow-clear
            />
          </AFormItem>
        </ACol>
      </ARow>

      <ARow :gutter="16">
        <ACol :span="12">
          <AFormItem label="关联附加费">
            <ASwitch v-model:checked="formData.additionalEnabled" />
          </AFormItem>
        </ACol>
        <ACol :span="12">
          <AFormItem v-if="formData.additionalEnabled">
            <ASelect
              v-model:value="formData.additionalFeeIds"
              placeholder="选择选项"
              mode="multiple"
              :options="additionalFeeOptions"
              :loading="additionalFeeOptions?.length === 0"
              allow-clear
            />
          </AFormItem>
        </ACol>
      </ARow> -->

      <AFormItem label="备注">
        <ATextarea
          v-model:value="formData.remarks"
          placeholder="输入内容"
          :rows="4"
          :maxlength="100"
          show-count
        />
      </AFormItem>
    </AForm>
    <template #footer>
      <ASpace>
        <AButton @click="handleCancel">取消</AButton>
        <AButton type="primary" @click="handleSave">保存</AButton>
      </ASpace>
    </template>
  </ADrawer>
</template>
