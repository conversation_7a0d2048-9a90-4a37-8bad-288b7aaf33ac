<template>
  <Page :auto-content-height="false">
    <div class="h-full">
      <MeterBookTable ref="meterBookTableRef" />
    </div>
  </Page>
</template>

<script lang="ts" setup>
import { Page } from '@vben/common-ui';
import { ref } from 'vue';
import MeterBookTable from './components/MeterBookTable.vue';

const meterBookTableRef = ref();
</script>

<style scoped>
/* 自定义样式 */
:deep(.vxe-table--render-default .vxe-body--row:hover) {
  background-color: #f5f5f5;
}

.h-full {
  height: 100%;
}
</style>
