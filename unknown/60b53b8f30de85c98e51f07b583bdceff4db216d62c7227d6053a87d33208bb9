<script setup lang="ts">
import { computed, reactive, ref, watch } from 'vue';

import {
  Button,
  DatePicker,
  Drawer,
  Form,
  Input,
  InputNumber,
  message,
  Select,
  Space,
} from 'ant-design-vue';
import dayjs from 'dayjs';

import {
  addMeterReadingManual,
  getLatestReading,
  getMeterList,
  getMeterReadingManualInfo,
  updateMeterReadingManual,
} from '#/api/waterfee/meterReadingManual';

import { readerOptions } from '../utils/options';

const props = defineProps({
  open: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    default: '',
  },
  readonly: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['update:open', 'reload']);

const loading = ref(false);
const isUpdate = ref(false);

// 计算标题
const title = computed(() => {
  if (props.readonly) {
    return '抄表补录详情';
  }
  return isUpdate.value ? '编辑抄表补录' : '新增抄表补录';
});

// 初始化表单
const formRef = ref();
const formModel = reactive({
  meterNo: '',
  userNo: '',
  userName: '',
  businessAreaId: undefined,
  businessAreaName: '',
  meterBookId: undefined,
  meterBookName: '',
  meterId: undefined,
  userId: undefined,
  lastReading: undefined,
  oldMeterStopReading: undefined,
  currentReading: undefined,
  waterUsage: undefined,
  readerId: undefined,
  readingTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
  remark: '',
});

const formRules = {
  meterNo: [{ required: true, message: '请输入水表编号', trigger: 'blur' }],
  currentReading: [
    { required: true, message: '请输入本期读数', trigger: 'blur' },
  ],
  readerId: [{ required: true, message: '请选择抄表员', trigger: 'change' }],
  readingTime: [
    { required: true, message: '请选择抄表时间', trigger: 'change' },
  ],
};

// 初始化下拉选项
const meterOptions = ref([]);
const meterLoading = ref(false);
const meterSearchValue = ref('');

// 搜索水表
async function handleSearchMeter(value) {
  if (!value) {
    meterOptions.value = [];
    return;
  }

  try {
    meterLoading.value = true;
    meterSearchValue.value = value;

    const res = await getMeterList({
      meterNo: value,
      pageSize: 20,
      pageNum: 1,
    });

    meterOptions.value =
      res && res.rows
        ? res.rows.map((item) => ({
            label: `${item.meterNo} - ${item.userName || ''}`,
            value: item.meterNo,
            data: item,
          }))
        : [];
  } catch (error) {
    console.error('搜索水表失败:', error);
    message.error(`搜索水表失败：${error.message || '未知错误'}`);
    meterOptions.value = [];
  } finally {
    meterLoading.value = false;
  }
}

// 选择水表
async function handleSelectMeter(value, option) {
  if (value && option && option.data) {
    const meterData = option.data;

    // 更新基本表单数据
    formModel.meterId = meterData.meterId;
    formModel.userId = meterData.userId;
    formModel.userNo = meterData.userNo;
    formModel.userName = meterData.userName;
    formModel.businessAreaId = meterData.businessAreaId;
    formModel.businessAreaName = meterData.businessAreaName;
    formModel.meterBookId = meterData.meterBookId;
    formModel.meterBookName = meterData.meterBookName;

    // 尝试获取最新抄表记录
    try {
      loading.value = true;
      const latestReading = await getLatestReading(value);

      if (latestReading) {
        // 更新上期读数和旧表止数
        formModel.lastReading = latestReading.lastReading;
        formModel.oldMeterStopReading = latestReading.oldMeterStopReading;
        console.log('获取到最新抄表记录:', latestReading);
      } else {
        // 如果没有最新记录，使用水表基本信息中的数据
        formModel.lastReading = meterData.lastReading;
        formModel.oldMeterStopReading = meterData.oldMeterStopReading;
      }

      message.success('水表信息加载成功');
    } catch (error) {
      console.error('获取最新抄表记录失败:', error);
      // 如果获取最新记录失败，使用水表基本信息中的数据
      formModel.lastReading = meterData.lastReading;
      formModel.oldMeterStopReading = meterData.oldMeterStopReading;
      message.warning('获取最新抄表记录失败，使用基本信息');
    } finally {
      loading.value = false;
    }
  }
}

// 查询水表信息
// async function queryMeterInfo(meterNo) {
//   try {
//     loading.value = true;
//     const res = await getMeterInfoByNo(meterNo);

//     if (!res) {
//       message.warning('未找到该水表信息');
//       return;
//     }

//     // 更新表单数据
//     formModel.meterId = res.meterId;
//     formModel.userId = res.userId;
//     formModel.userNo = res.userNo;
//     formModel.userName = res.userName;
//     formModel.lastReading = res.lastReading;
//     formModel.oldMeterStopReading = res.oldMeterStopReading;
//     formModel.businessAreaId = res.businessAreaId;
//     formModel.businessAreaName = res.businessAreaName;
//     formModel.meterBookId = res.meterBookId;
//     formModel.meterBookName = res.meterBookName;

//     message.success('水表信息查询成功');
//   } catch (error) {
//     console.error('查询水表信息失败:', error);
//     message.error(`查询水表信息失败：${error.message || '未知错误'}`);
//   } finally {
//     loading.value = false;
//   }
// }

// 获取抄表补录详情
async function getManualDetail(id) {
  try {
    loading.value = true;
    console.log('获取抄表补录详情, ID:', id);

    const res = await getMeterReadingManualInfo(id);
    console.log('获取到的数据:', res);

    if (res) {
      // 直接使用响应数据替换表单模型
      Object.assign(formModel, {
        meterNo: res.meterNo || '',
        userNo: res.userNo || '',
        userName: res.userName || '',
        businessAreaId: res.businessAreaId,
        businessAreaName: res.businessAreaName || '',
        meterBookId: res.meterBookId,
        meterBookName: res.meterBookName || '',
        meterId: res.meterId,
        userId: res.userId,
        lastReading: res.lastReading,
        oldMeterStopReading: res.oldMeterStopReading,
        currentReading: res.currentReading,
        waterUsage: res.waterUsage,
        readerId: res.readerId,
        readingTime: res.readingTime,
        remark: res.remark || '',
      });

      console.log('设置后的表单模型:', { ...formModel });
    }
  } catch (error) {
    console.error('获取抄表补录详情失败:', error);
    message.error(`获取详情失败：${error.message || '未知错误'}`);
  } finally {
    loading.value = false;
  }
}

// 提交表单
async function handleSubmit() {
  try {
    // 只读模式下不执行提交
    if (props.readonly) {
      emit('update:open', false);
      return;
    }

    loading.value = true;

    // 校验表单
    await formRef.value.validate();

    // 准备提交数据
    const formValues = { ...formModel };

    // 确保ID字段是字符串类型
    if (isUpdate.value) {
      formValues.id = props.id;
    }

    // 处理日期字段，确保格式正确
    if (formValues.readingTime) {
      // 确保是字符串类型
      formValues.readingTime = String(formValues.readingTime);
    }

    // 计算用水量
    if (
      formValues.currentReading !== undefined &&
      formValues.lastReading !== undefined
    ) {
      const lastReading = Number(formValues.lastReading) || 0;
      const current = Number(formValues.currentReading) || 0;

      // 计算用水量
      if (current >= lastReading) {
        formValues.waterUsage = current - lastReading;
      } else {
        // 如果本期读数小于上期读数，可能是水表走满一圈
        message.warning('本期读数小于上期读数，请确认是否正确');
        formValues.waterUsage = 0;
      }
    }

    // 添加operator字段，值为readerId对应的名称
    if (formValues.readerId) {
      // 从readerOptions.value中查找对应的抄表员名称
      const selectedReader = readerOptions.value.find(
        (option) => option.value === formValues.readerId,
      );
      if (selectedReader) {
        formValues.operator = selectedReader.label;
        console.log('设置operator字段:', formValues.operator);
      }
    }

    // 转换为JSON字符串再解析，防止精度丢失
    const jsonStr = JSON.stringify(formValues);
    const jsonValues = JSON.parse(jsonStr);

    console.log('提交表单数据:', jsonValues);

    if (isUpdate.value) {
      // 编辑模式
      await updateMeterReadingManual(jsonValues);
      message.success('更新成功');
    } else {
      // 新增模式
      await addMeterReadingManual(jsonValues);
      message.success('添加成功');
    }

    emit('reload');
    emit('update:open', false);
  } catch (error) {
    console.error('提交抄表补录失败:', error);
    message.error(`提交失败：${error.message || '未知错误'}`);
  } finally {
    loading.value = false;
  }
}

// 取消操作
// function handleCancel() {
//   emit('update:open', false);
// }

// 不再需要监听水表编号变化，因为我们使用 handleSelectMeter 函数来处理水表选择

// 监听本期读数变化，自动计算用水量
watch(
  () => formModel.currentReading,
  (currentReading) => {
    if (currentReading !== undefined && formModel.lastReading !== undefined) {
      const lastReading = Number(formModel.lastReading) || 0;
      const current = Number(currentReading) || 0;

      // 计算用水量
      if (current >= lastReading) {
        formModel.waterUsage = current - lastReading;
      } else {
        // 如果本期读数小于上期读数，可能是水表走满一圈
        message.warning('本期读数小于上期读数，请确认是否正确');
        formModel.waterUsage = 0;
      }
    }
  },
);

// 监听抽屉打开状态
watch(
  () => props.open,
  async (val) => {
    console.log('抽屉状态变化:', val);
    if (val) {
      // 抽屉打开时
      isUpdate.value = !!props.id;
      console.log(
        '抽屉打开, isUpdate:',
        isUpdate.value,
        'props.id:',
        props.id,
        'props.readonly:',
        props.readonly,
      );

      try {
        loading.value = true;

        // 重置表单
        formModel.meterNo = '';
        formModel.userNo = '';
        formModel.userName = '';
        formModel.businessAreaId = undefined;
        formModel.businessAreaName = '';
        formModel.meterBookId = undefined;
        formModel.meterBookName = '';
        formModel.meterId = undefined;
        formModel.userId = undefined;
        formModel.lastReading = undefined;
        formModel.oldMeterStopReading = undefined;
        formModel.currentReading = undefined;
        formModel.waterUsage = undefined;
        formModel.readerId = undefined;
        formModel.readingTime = dayjs().format('YYYY-MM-DD HH:mm:ss');
        formModel.remark = '';

        console.log('重置表单后:', { ...formModel });

        // 编辑模式或详情模式加载详情
        if (props.id) {
          console.log('准备加载详情, ID:', props.id);
          try {
            await getManualDetail(props.id);
          } catch (error) {
            console.error('获取抄表补录详情失败:', error);
          }
        }
      } catch (error) {
        console.error('初始化抽屉失败:', error);
      } finally {
        loading.value = false;
      }
    } else {
      console.log('抽屉关闭');
    }
  },
  { immediate: true },
);
</script>

<template>
  <Drawer
    :title="title"
    :open="open"
    :width="700"
    :mask-closable="false"
    :closable="true"
    @close="emit('update:open', false)"
    :after-visible-change="
      (visible) => {
        if (!visible) emit('update:open', false);
      }
    "
    :destroy-on-close="true"
  >
    <div style="padding: 0 20px">
      <Form
        ref="formRef"
        :model="formModel"
        :rules="formRules"
        :label-col="{ span: 8 }"
        :wrapper-col="{ span: 16 }"
        :disabled="props.readonly"
      >
        <Form.Item label="水表编号" name="meterNo">
          <Select
            v-model:value="formModel.meterNo"
            :options="meterOptions"
            :loading="meterLoading"
            show-search
            placeholder="请输入水表编号"
            :filter-option="false"
            @search="handleSearchMeter"
            @select="handleSelectMeter"
            style="width: 100%"
          >
            <template #notFoundContent>
              <div v-if="meterLoading">正在搜索...</div>
              <div v-else-if="meterSearchValue">未找到匹配的水表</div>
              <div v-else>请输入水表编号搜索</div>
            </template>
          </Select>
        </Form.Item>

        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px">
          <Form.Item label="用户编号" name="userNo">
            <Input
              v-model:value="formModel.userNo"
              placeholder="自动获取"
              disabled
            />
          </Form.Item>
          <Form.Item label="用户姓名" name="userName">
            <Input
              v-model:value="formModel.userName"
              placeholder="自动获取"
              disabled
            />
          </Form.Item>

          <Form.Item label="营业区域" name="businessAreaName">
            <Input
              v-model:value="formModel.businessAreaName"
              placeholder="自动获取"
              disabled
            />
          </Form.Item>
          <Form.Item label="抄表手册" name="meterBookName">
            <Input
              v-model:value="formModel.meterBookName"
              placeholder="自动获取"
              disabled
            />
          </Form.Item>

          <Form.Item label="上期读数" name="lastReading">
            <InputNumber
              v-model:value="formModel.lastReading"
              style="width: 100%"
              placeholder="自动获取"
              disabled
            />
          </Form.Item>
          <Form.Item label="旧表止数" name="oldMeterStopReading">
            <InputNumber
              v-model:value="formModel.oldMeterStopReading"
              style="width: 100%"
              placeholder="自动获取"
              disabled
            />
          </Form.Item>

          <Form.Item label="本期读数" name="currentReading">
            <InputNumber
              v-model:value="formModel.currentReading"
              :min="0"
              :precision="2"
              style="width: 100%"
              placeholder="请输入本期读数"
            />
          </Form.Item>
          <Form.Item label="用水量" name="waterUsage">
            <InputNumber
              v-model:value="formModel.waterUsage"
              :min="0"
              :precision="2"
              style="width: 100%"
              placeholder="自动计算"
              disabled
            />
          </Form.Item>

          <Form.Item label="抄表员" name="readerId">
            <Select
              v-model:value="formModel.readerId"
              :options="readerOptions"
              placeholder="请选择抄表员"
            />
          </Form.Item>
          <Form.Item label="抄表时间" name="readingTime">
            <DatePicker
              v-model:value="formModel.readingTime"
              style="width: 100%"
              placeholder="请选择抄表时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              show-time
            />
          </Form.Item>
        </div>

        <Form.Item label="备注" name="remark">
          <Input.TextArea
            v-model:value="formModel.remark"
            :rows="4"
            placeholder="请输入备注"
          />
        </Form.Item>
      </Form>
    </div>

    <template #footer>
      <div style="text-align: right">
        <Space>
          <Button @click="emit('update:open', false)">
            {{ props.readonly ? '关闭' : '取消' }}
          </Button>
          <Button
            v-if="!props.readonly"
            type="primary"
            :loading="loading"
            @click="handleSubmit"
          >
            {{ isUpdate ? '更新' : '添加' }}
          </Button>
        </Space>
      </div>
    </template>
  </Drawer>
</template>

<style scoped>
/* 自定义样式 */
</style>
