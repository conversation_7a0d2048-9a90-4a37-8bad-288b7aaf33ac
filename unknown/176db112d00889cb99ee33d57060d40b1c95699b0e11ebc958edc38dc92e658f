<script setup lang="ts">
import { onMounted, ref, watch } from 'vue';

import { Empty, message, Table } from 'ant-design-vue';

import { getHistoryReadings } from '#/api/waterfee/meterReadingRecord';
import { preserveBigInt } from '#/utils/json-bigint';

const props = defineProps({
  meterId: {
    type: String,
    required: true,
  },
  meterNo: {
    type: String,
    default: '',
  },
});

const columns = [
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    width: 60,
    align: 'center',
    customRender: ({ index }) => index + 1,
  },
  {
    title: '上期抄表读数',
    dataIndex: 'lastReading',
    key: 'lastReading',
    align: 'center',
  },
  {
    title: '上期抄表时间',
    dataIndex: 'lastReadingTime',
    key: 'lastReadingTime',
    align: 'center',
  },
  {
    title: '本期抄表读数',
    dataIndex: 'currentReading',
    key: 'currentReading',
    align: 'center',
  },
  {
    title: '本期抄表时间',
    dataIndex: 'readingTime',
    key: 'readingTime',
    align: 'center',
  },
  {
    title: '旧表止数',
    dataIndex: 'oldMeterStopReading',
    key: 'oldMeterStopReading',
    align: 'center',
  },
  {
    title: '本期水量',
    dataIndex: 'waterUsage',
    key: 'waterUsage',
    align: 'center',
  },
];

const dataSource = ref([]);
const loading = ref(false);
// 由于只使用latestReading接口，不需要复杂的分页
const pagination = ref(false);

// 获取智能水表抄表记录
async function fetchReadingRecords(meterId) {
  if (!meterId) return;

  loading.value = true;
  try {
    // 如果有meterNo，使用 getHistoryReadings 接口获取历史抄表记录
    if (props.meterNo) {
      await fetchHistoryReadings(props.meterNo);
    } else {
      // 如果没有meterNo，显示提示信息
      dataSource.value = [];
      message.warning('无法获取水表编号，请稍后再试');
    }
  } catch (error) {
    console.error('获取智能水表抄表记录失败:', error);
    message.error('获取抄表记录失败');
    dataSource.value = [];
  } finally {
    loading.value = false;
  }
}

// 获取历史抄表记录
async function fetchHistoryReadings(meterNo) {
  if (!meterNo) return;

  try {
    const historyReadings = await getHistoryReadings(meterNo);
    if (historyReadings && historyReadings.length > 0) {
      // 处理数据，确保大整数精度
      dataSource.value = historyReadings.map((item, index) => ({
        ...preserveBigInt(item),
        index,
      }));
    } else {
      dataSource.value = [];
      message.warning('未找到抄表记录');
    }
  } catch (error) {
    console.error('获取历史抄表记录失败:', error);
    message.error('获取历史抄表记录失败');
    dataSource.value = [];
  }
}

// 不需要处理分页变化，因为我们禁用了分页

// 监听meterId和meterNo变化
watch(
  [() => props.meterId, () => props.meterNo],
  ([newMeterId, newMeterNo]) => {
    if (newMeterId && newMeterNo) {
      // 当meterId和meterNo都有值时获取记录
      console.log('监听到meterId和meterNo变化:', newMeterId, newMeterNo);
      fetchReadingRecords(newMeterId);
    }
  },
  { immediate: true },
);

// 单独监听meterNo变化，确保在meterNo后加载时也能获取数据
watch(
  () => props.meterNo,
  (newMeterNo) => {
    if (newMeterNo && props.meterId) {
      console.log('监听到meterNo变化:', newMeterNo);
      fetchReadingRecords(props.meterId);
    }
  },
);

onMounted(() => {
  if (props.meterId && props.meterNo) {
    // 只有当meterId和meterNo都有值时才获取记录
    console.log('组件挂载时获取记录:', props.meterId, props.meterNo);
    fetchReadingRecords(props.meterId);
  }
});
</script>

<template>
  <div>
    <Table
      :columns="columns"
      :data-source="dataSource"
      :loading="loading"
      :pagination="pagination"
      bordered
    >
      <template #emptyText>
        <Empty description="暂无抄表记录" />
      </template>
    </Table>
  </div>
</template>

<style scoped>
/* 可以添加自定义样式 */
</style>
