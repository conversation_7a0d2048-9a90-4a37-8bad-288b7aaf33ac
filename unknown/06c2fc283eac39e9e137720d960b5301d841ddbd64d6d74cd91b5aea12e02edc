<script setup lang="ts">
import { ref, computed, watch, reactive, onMounted } from 'vue';
import { Drawer, Form, Button, Space, message, TreeSelect, Select } from 'ant-design-vue';
import { updateMeterBook } from '#/api/waterfee/meterAdjustment';
import { areaList } from '#/api/waterfee/area';
import { meterBookList } from '#/api/waterfee/meterbook';
import { businessAreaOptions, meterBookOptions, buildAreaTree } from '../utils/options';

const props = defineProps({
  open: {
    type: Boolean,
    default: false
  },
  selectedMeterIds: {
    type: Array as () => string[],
    default: () => []
  },
  currentBookId: {
    type: String,
    default: ''
  },
  // 当前区域ID
  currentAreaId: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['update:open', 'reload']);

const loading = ref(false);
const areaTreeData = ref([]);

// 计算标题
const title = computed(() => {
  return '调整水表所属区域和表册';
});

// 初始化表单
const formRef = ref();
const formModel = reactive({
  businessAreaId: undefined,
  meterBookId: undefined,
});

// 存储原始值
const originalBusinessAreaId = ref('');
const originalMeterBookId = ref('');

const formRules = {
  businessAreaId: [{ required: true, message: '请选择所属区域', trigger: 'change' }],
  meterBookId: [{ required: true, message: '请选择所属表册', trigger: 'change' }],
};

// 加载业务区域树
async function loadBusinessAreaTree() {
  try {
    const res = await areaList();
    if (res && res.length > 0) {
      // 将平面列表转换为树形结构
      const treeData = buildAreaTree(res);
      areaTreeData.value = treeData;
    }
  } catch (error) {
    console.error('加载业务区域树失败:', error);
  }
}

// 提交表单
async function handleSubmit() {
  try {
    loading.value = true;

    // 校验表单
    await formRef.value.validate();

    // 检查是否有选中的水表
    if (!props.selectedMeterIds || props.selectedMeterIds.length === 0) {
      message.warning('请选择要调整的水表');
      return;
    }

    // 检查是否选择了区域和表册
    if (!formModel.businessAreaId) {
      message.warning('请选择所属区域');
      return;
    }

    if (!formModel.meterBookId) {
      message.warning('请选择所属表册');
      return;
    }

    // 检查是否至少修改了所属表册或所属区域中的一个
    const isAreaChanged = formModel.businessAreaId !== originalBusinessAreaId.value;
    const isBookChanged = formModel.meterBookId !== originalMeterBookId.value;

    if (!isAreaChanged && !isBookChanged) {
      message.warning('所属表册和所属区域至少修改一个');
      return;
    }

    // 准备提交数据
    const formValues = {
      meterIds: props.selectedMeterIds,
      businessAreaId: isAreaChanged ? formModel.businessAreaId : originalBusinessAreaId.value,
      meterBookId: isBookChanged ? formModel.meterBookId : originalMeterBookId.value,
    };

    console.log('提交表单数据:', formValues);

    try {
      // 调用API更新水表所属区域和表册
      await updateMeterBook(formValues);
      message.success('调整成功');

      emit('reload');
      emit('update:open', false);
    } catch (apiError) {
      console.error('API调用失败:', apiError);
      message.error(`调整失败：${apiError.message || '未知错误'}`);
    }
  } catch (error) {
    console.error('调整水表所属区域和表册失败:', error);
    message.error(`调整失败：${error.message || '未知错误'}`);
  } finally {
    loading.value = false;
  }
}

// 取消操作
function handleCancel() {
  emit('update:open', false);
}

// 加载初始数据
onMounted(async () => {
  try {
    await loadBusinessAreaTree();
  } catch (error) {
    console.error('加载初始数据失败:', error);
  }
});

// 监听抽屉打开状态
watch(
  () => props.open,
  async (val) => {
    console.log('抽屉状态变化:', val);
    if (val) {
      try {
        loading.value = true;

        // 保存原始值
        originalBusinessAreaId.value = props.currentAreaId;
        originalMeterBookId.value = props.currentBookId;

        console.log('原始值:', {
          businessAreaId: originalBusinessAreaId.value,
          meterBookId: originalMeterBookId.value
        });

        // 重置表单
        formModel.businessAreaId = undefined;
        formModel.meterBookId = undefined;

        console.log('重置表单后:', { ...formModel });
        console.log('选中的水表IDs:', props.selectedMeterIds);
      } catch (error) {
        console.error('初始化抽屉失败:', error);
      } finally {
        loading.value = false;
      }
    } else {
      console.log('抽屉关闭');
    }
  },
  { immediate: true }
);
</script>

<template>
  <Drawer
    :title="title"
    :open="open"
    :width="500"
    :maskClosable="false"
    :closable="true"
    @close="emit('update:open', false)"
    :afterVisibleChange="(visible) => { if (!visible) emit('update:open', false); }"
    :destroyOnClose="true"
  >
    <div style="padding: 0 20px;">
      <Form
        ref="formRef"
        :model="formModel"
        :rules="formRules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <Form.Item label="所属区域" name="targetAreaId">
          <TreeSelect
            v-model:value="formModel.businessAreaId"
            :tree-data="areaTreeData"
            placeholder="请选择所属区域"
            tree-default-expand-all
            :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
          />
        </Form.Item>
        <Form.Item label="所属表册" name="targetBookId">
          <Select
            v-model:value="formModel.meterBookId"
            :options="meterBookOptions"
            placeholder="请选择所属表册"
          />
        </Form.Item>
      </Form>
    </div>

    <template #footer>
      <div style="text-align: right">
        <Space>
          <Button @click="emit('update:open', false)">
            取消
          </Button>
          <Button
            type="primary"
            :loading="loading"
            @click="handleSubmit"
          >
            确定
          </Button>
        </Space>
      </div>
    </template>
  </Drawer>
</template>

<style scoped>
/* 自定义样式 */
</style>
