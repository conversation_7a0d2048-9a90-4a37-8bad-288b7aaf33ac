<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { Modal, Form, Input, message } from 'ant-design-vue';
import { processMeterAlert } from '#/api/waterfee/meter/alert';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  alertId: {
    type: String,
    default: '',
  },
  alertContent: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['update:visible', 'success']);

const formRef = ref();
const loading = ref(false);
const formState = reactive({
  processResult: '',
});

const rules = {
  processResult: [
    { required: true, message: '请输入处理结果', trigger: 'blur' },
    { min: 2, max: 500, message: '处理结果长度在2-500个字符之间', trigger: 'blur' },
  ],
};

// 处理报警事件
const handleProcess = async () => {
  try {
    await formRef.value.validate();
    loading.value = true;
    
    await processMeterAlert(props.alertId, formState.processResult);
    message.success('处理成功');
    
    // 重置表单并关闭弹窗
    formState.processResult = '';
    emit('update:visible', false);
    emit('success');
  } catch (error) {
    console.error('处理报警事件失败:', error);
    message.error('处理失败: ' + (error.message || '未知错误'));
  } finally {
    loading.value = false;
  }
};

// 取消处理
const handleCancel = () => {
  formState.processResult = '';
  emit('update:visible', false);
};

// 监听弹窗关闭
const afterClose = () => {
  formRef.value?.resetFields();
};

// 组件挂载时初始化
onMounted(() => {
  // 可以在这里添加初始化逻辑
});
</script>

<template>
  <Modal
    :title="'处理报警事件'"
    :open="visible"
    :confirmLoading="loading"
    @update:open="(val) => emit('update:visible', val)"
    @ok="handleProcess"
    @cancel="handleCancel"
    @after-close="afterClose"
    width="600px"
  >
    <div class="alert-info">
      <div class="alert-title">告警内容：</div>
      <div class="alert-content">{{ alertContent }}</div>
    </div>
    
    <Form
      ref="formRef"
      :model="formState"
      :rules="rules"
      layout="vertical"
    >
      <Form.Item label="处理结果" name="processResult">
        <Input.TextArea
          v-model:value="formState.processResult"
          placeholder="请输入处理结果"
          :rows="4"
          :maxlength="500"
          show-count
        />
      </Form.Item>
    </Form>
  </Modal>
</template>

<style scoped>
.alert-info {
  margin-bottom: 16px;
  padding: 12px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.alert-title {
  font-weight: bold;
  margin-bottom: 8px;
}

.alert-content {
  color: #ff4d4f;
}
</style>
