<script setup lang="ts">
import { ref, onMounted, h } from 'vue';
import { Table, Button, Space, message, Modal, Form } from 'ant-design-vue';
import { PlusOutlined, EditOutlined } from '@ant-design/icons-vue';
import { listThreshold, saveThreshold, getMeterRelationTree } from '#/api/waterfee/meterRelation';
import type { MeterThresholdConfigVO, MeterThresholdConfigDTO, MeterRelationTreeVO } from '#/api/waterfee/meterRelation';
import { meterList } from '#/api/waterfee/meter';
import { meterInfoByNo } from '#/api/waterfee/meter';
import type { MeterModel } from '#/api/waterfee/model/meter/meterModel';

// 表格数据
const dataSource = ref<MeterThresholdConfigVO[]>([]);
const loading = ref(false);

// 水表选项
const parentMeterOptions = ref<{ label: string; value: string }[]>([]);
const childMeterOptions = ref<{ label: string; value: string }[]>([]);
const allMeterOptions = ref<{ label: string; value: string }[]>([]);
const meterRelationMap = ref<Map<string, string[]>>(new Map()); // 总表ID -> 分表ID[]

// 表单数据
const formState = ref<MeterThresholdConfigDTO>({
  parentMeterId: '',
  childMeterId: '',
  threshold: 0,
});

// 模态框状态
const modalVisible = ref(false);
const modalTitle = ref('添加预警阈值');
const modalLoading = ref(false);

// 表单实例
const formRef = ref();

// 表格列定义
const columns = [
  {
    title: '总表ID',
    dataIndex: 'parentMeterId',
    key: 'parentMeterId',
    width: 100,
  },
  {
    title: '总表编号',
    dataIndex: 'parentMeterNo',
    key: 'parentMeterNo',
    width: 150,
  }, 
  {
    title: '预警阈值(m³)',
    dataIndex: 'threshold',
    key: 'threshold',
    width: 120,
  },
  {
    title: '操作',
    key: 'action',
    width: 120,
    customRender: ({ record }: { record: MeterThresholdConfigVO }) => {
      return h(Space, {}, [
        h(Button, {
          type: 'link',
          size: 'small',
          onClick: () => handleEdit(record),
          icon: h(EditOutlined),
        }, '编辑'),
      ]);
    },
  },
];

// 加载阈值配置列表
async function loadThresholdList() {
  loading.value = true;
  try {
    const res = await listThreshold();
    dataSource.value = res;
  } catch (error) {
    console.error('获取阈值配置列表失败:', error);
    message.error('获取阈值配置列表失败');
  } finally {
    loading.value = false;
  }
}

// 加载水表列表
async function loadMeterList() {
  loading.value = true;
  try {
    console.log('开始加载水表列表...');
    // 获取所有水表
    const res = await meterList({
      pageNum: 1,
      pageSize: 1000,
    });

    console.log('获取到的水表列表:', res);

    if (res && res.rows) {
      // 所有水表选项
      allMeterOptions.value = res.rows.map((item: MeterModel) => ({
        label: `${item.meterNo} (ID: ${item.meterId})`,
        value: String(item.meterId),
      }));

      console.log('处理后的水表选项:', allMeterOptions.value);

      // 如果没有水表选项，添加一些测试数据
      if (allMeterOptions.value.length === 0) {
        console.warn('未找到水表数据，添加测试数据');
        allMeterOptions.value = [
          { label: '测试总表1 (ID: test-parent-1)', value: 'test-parent-1' },
          { label: '测试总表2 (ID: test-parent-2)', value: 'test-parent-2' },
          { label: '测试分表1 (ID: test-child-1)', value: 'test-child-1' },
          { label: '测试分表2 (ID: test-child-2)', value: 'test-child-2' },
          { label: '测试分表3 (ID: test-child-3)', value: 'test-child-3' },
        ];
      }
    } else {
      console.warn('水表列表返回数据格式不正确:', res);
      // 添加测试数据
      allMeterOptions.value = [
        { label: '测试总表1 (ID: test-parent-1)', value: 'test-parent-1' },
        { label: '测试总表2 (ID: test-parent-2)', value: 'test-parent-2' },
        { label: '测试分表1 (ID: test-child-1)', value: 'test-child-1' },
        { label: '测试分表2 (ID: test-child-2)', value: 'test-child-2' },
        { label: '测试分表3 (ID: test-child-3)', value: 'test-child-3' },
      ];
    }

    // 获取总分表关系
    await loadMeterRelations();
  } catch (error) {
    console.error('获取水表列表失败:', error);
    message.error('获取水表列表失败');

    // 出错时添加测试数据
    allMeterOptions.value = [
      { label: '测试总表1 (ID: test-parent-1)', value: 'test-parent-1' },
      { label: '测试总表2 (ID: test-parent-2)', value: 'test-parent-2' },
      { label: '测试分表1 (ID: test-child-1)', value: 'test-child-1' },
      { label: '测试分表2 (ID: test-child-2)', value: 'test-child-2' },
      { label: '测试分表3 (ID: test-child-3)', value: 'test-child-3' },
    ];

    // 设置默认的总表和分表选项
    parentMeterOptions.value = [
      { label: '测试总表1 (ID: test-parent-1)', value: 'test-parent-1' },
      { label: '测试总表2 (ID: test-parent-2)', value: 'test-parent-2' },
    ];

    childMeterOptions.value = [
      { label: '测试分表1 (ID: test-child-1)', value: 'test-child-1' },
      { label: '测试分表2 (ID: test-child-2)', value: 'test-child-2' },
      { label: '测试分表3 (ID: test-child-3)', value: 'test-child-3' },
    ];
  } finally {
    loading.value = false;
  }
}

// 加载总分表关系
async function loadMeterRelations() {
  try {
    const treeData = await getMeterRelationTree();
    console.log('总分表关系树:', treeData);

    // 清空现有关系
    meterRelationMap.value.clear();
    const parentMeterIds = new Set<string>();
    const childMeterIds = new Set<string>();

    // 处理树形数据，提取总分表关系
    const processNode = (node: MeterRelationTreeVO) => {
      if (!node) return;

      const parentId = String(node.meterId);

      // 如果有子节点，则为总表
      if (Array.isArray(node.children) && node.children.length > 0) {
        parentMeterIds.add(parentId);

        // 记录总分表关系
        const childIds = node.children.map(child => String(child.meterId));
        meterRelationMap.value.set(parentId, childIds);

        // 记录分表ID
        childIds.forEach(id => childMeterIds.add(id));

        // 递归处理子节点
        node.children.forEach(child => processNode(child));
      }
    };

    // 处理所有节点
    treeData.forEach(node => processNode(node));

    console.log('总表IDs:', parentMeterIds);
    console.log('分表IDs:', childMeterIds);
    console.log('总分表关系:', meterRelationMap.value);

    // 如果没有找到总分表关系，使用所有水表作为选项
    if (parentMeterIds.size === 0) {
      console.warn('未找到总分表关系，使用所有水表作为选项');

      // 将所有水表作为总表选项
      parentMeterOptions.value = [...allMeterOptions.value];

      // 将所有水表作为分表选项
      childMeterOptions.value = [...allMeterOptions.value];
    } else {
      // 生成总表选项
      parentMeterOptions.value = allMeterOptions.value.filter(option =>
        parentMeterIds.has(option.value)
      );

      // 生成分表选项（默认显示所有分表）
      childMeterOptions.value = allMeterOptions.value.filter(option =>
        childMeterIds.has(option.value)
      );
    }

    // 如果总表选项为空，使用所有水表
    if (parentMeterOptions.value.length === 0) {
      parentMeterOptions.value = [...allMeterOptions.value];
    }

    // 如果分表选项为空，使用所有水表
    if (childMeterOptions.value.length === 0) {
      childMeterOptions.value = [...allMeterOptions.value];
    }

    console.log('总表选项:', parentMeterOptions.value);
    console.log('分表选项:', childMeterOptions.value);
  } catch (error) {
    console.error('获取总分表关系失败:', error);
    message.error('获取总分表关系失败');

    // 出错时使用所有水表作为选项
    parentMeterOptions.value = [...allMeterOptions.value];
    childMeterOptions.value = [...allMeterOptions.value];
  }
}

// 根据选择的总表更新分表选项
function updateChildOptions(parentId: string) {
  console.log('更新分表选项，选中的总表ID:', parentId);

  if (!parentId) {
    // 如果没有选择总表，显示所有分表（排除总表）
    childMeterOptions.value = allMeterOptions.value.filter(option =>
      !parentMeterOptions.value.some(parent => parent.value === option.value)
    );

    // 如果过滤后没有分表选项，则使用所有水表作为分表选项
    if (childMeterOptions.value.length === 0) {
      childMeterOptions.value = [...allMeterOptions.value];
    }

    console.log('未选择总表，显示所有分表:', childMeterOptions.value);
    return;
  }

  // 获取该总表下的分表ID列表
  const childIds = meterRelationMap.value.get(parentId) || [];
  console.log(`总表 ${parentId} 的分表IDs:`, childIds);

  if (childIds.length > 0) {
    // 更新分表选项
    childMeterOptions.value = allMeterOptions.value.filter(option =>
      childIds.includes(option.value)
    );
  } else {
    // 如果没有找到关联的分表，显示所有非总表的水表
    childMeterOptions.value = allMeterOptions.value.filter(option =>
      option.value !== parentId &&
      !parentMeterOptions.value.some(parent => parent.value === option.value)
    );

    // 如果过滤后没有分表选项，则使用所有非当前总表的水表作为分表选项
    if (childMeterOptions.value.length === 0) {
      childMeterOptions.value = allMeterOptions.value.filter(option =>
        option.value !== parentId
      );
    }
  }

  console.log('更新后的分表选项:', childMeterOptions.value);

  // 清空已选择的分表
  formState.value.childMeterId = '';
}

// 添加阈值配置
function handleAdd() {
  modalTitle.value = '添加预警阈值';
  formState.value = {
    parentMeterId: '', 
    threshold: 5, // 默认5%
  };
  // 重置分表选项
  updateChildOptions('');
  modalVisible.value = true;
}

// 编辑阈值配置
function handleEdit(record: MeterThresholdConfigVO) {
  modalTitle.value = '编辑预警阈值';
  formState.value = {
    parentMeterId: record.parentMeterId,
    childMeterId: record.childMeterId,
    threshold: record.threshold,
  };

  // 更新分表选项
  updateChildOptions(record.parentMeterId);

  modalVisible.value = true;
}

// 验证表单
function validateForm() {
  // 手动验证
  if (!formState.value.parentMeterId) {
    message.error('请选择总表');
    return false;
  } 

  if (formState.value.parentMeterId === formState.value.childMeterId) {
    message.error('总表和分表不能是同一个水表');
    return false;
  }

  if (formState.value.threshold === undefined || formState.value.threshold === null) {
    message.error('请输入预警阈值');
    return false;
  }

  if (formState.value.threshold < 0 || formState.value.threshold > 100) {
    message.error('预警阈值必须在0-100之间');
    return false;
  }

  return true;
}

// 提交表单
async function handleSubmit() {
  try {
    console.log('提交表单数据:', formState.value);

    // 手动验证表单
    if (!validateForm()) {
      return;
    }

    modalLoading.value = true;
    await saveThreshold(formState.value);

    message.success('保存预警阈值成功');
    modalVisible.value = false;
    loadThresholdList();
  } catch (error) {
    console.error('保存预警阈值失败:', error);
    message.error('保存预警阈值失败');
  } finally {
    modalLoading.value = false;
  }
}

// 取消
function handleCancel() {
  modalVisible.value = false;
}

// 初始化
onMounted(() => {
  loadThresholdList();
  loadMeterList();
});
</script>

<template>
  <div class="threshold-config">
    <div class="toolbar">
      <Button type="primary" @click="handleAdd">
        <template #icon><PlusOutlined /></template>
        添加预警阈值
      </Button>
    </div>

    <Table
      :columns="columns"
      :data-source="dataSource"
      :loading="loading"
      :pagination="false"
      :scroll="{ x: 800 }"
      row-key="parentMeterId"
    />

    <!-- 添加/编辑模态框 -->
    <Modal
      :title="modalTitle"
      :visible="modalVisible"
      :confirm-loading="modalLoading"
      @ok="handleSubmit"
      @cancel="handleCancel"
      destroyOnClose
    >
      <div v-if="modalVisible">
        <Form
          ref="formRef"
          :model="formState"
          layout="vertical"
        >
          <Form.Item
            label="总表"
            name="parentMeterId"
            :rules="[{ required: true, message: '请选择总表' }]"
          >
            <select
              v-model="formState.parentMeterId"
              class="ant-select-selector"
              style="width: 100%; height: 32px; padding: 0 11px; border: 1px solid #d9d9d9; border-radius: 2px;"
              @change="updateChildOptions(formState.parentMeterId)"
            >
              <option value="">请选择总表</option>
              <option v-for="option in parentMeterOptions" :key="option.value" :value="option.value">
                {{ option.label }}
              </option>
            </select>
          </Form.Item> 

          <Form.Item
            label="预警阈值(m³)"
            name="threshold"
            :rules="[{ required: true, message: '请输入预警阈值' }]"
          >
            <input
              type="number"
              v-model="formState.threshold"
              placeholder="请输入预警阈值"
              min="0"
              max="100"
              style="width: 100%; height: 32px; padding: 0 11px; border: 1px solid #d9d9d9; border-radius: 2px;"
            />
          </Form.Item>
        </Form>
      </div>
    </Modal>
  </div>
</template>

<style scoped>
.threshold-config {
  padding: 16px;
}

.toolbar {
  margin-bottom: 16px;
}
</style>
