<script setup lang="ts">
import { ref, onMounted, defineAsyncComponent, h } from 'vue';
import { useRouter } from 'vue-router';
import { Page } from '@vben/common-ui';
import { Button, Space, message, Modal } from 'ant-design-vue';
import { DownloadOutlined, ExclamationCircleOutlined } from '@ant-design/icons-vue';
import { useVbenVxeGrid } from '#/adapter/vxe-table';

import {
  getMeterAlertList,
  deleteMeterAlert,
  exportMeterAlert,
} from '#/api/waterfee/meter/alert';
import { columns, querySchema } from './alert.data';
import { preserveBigInt } from '#/utils/json-bigint';
import { commonDownloadExcel } from '#/utils/file/download';
import { useDictStore } from '#/store/dict';
import { getDictOptions } from '#/utils/dict';

// 异步加载组件
const ProcessModal = defineAsyncComponent(() => import('./components/ProcessModal.vue'));

const router = useRouter();
const dictStore = useDictStore();

// 处理弹窗状态
const processModalVisible = ref(false);
const currentAlert = ref({
  alertId: '',
  alertContent: '',
});

const formOptions = {
  commonConfig: {
    labelWidth: 80,
    componentProps: {
      allowClear: true,
    },
  },
  schema: querySchema(),
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
};

const gridOptions = {
  columns,
  height: 'auto',
  width: '100%',
  fit: true,
  autoResize: true,
  tableLayout: 'auto',
  scrollX: {
    enabled: true,
    gt: 10,
  },
  resizable: true,
  resizeConfig: {
    minWidth: 80,
  },
  tooltipConfig: {
    showAll: true,
  },
  proxyConfig: {
    ajax: {
      query: async ({ page }: { page?: { currentPage: number; pageSize: number } }, formValues: Record<string, any> = {}) => {
        const resp = await getMeterAlertList({
          pageNum: page?.currentPage,
          pageSize: page?.pageSize,
          ...formValues,
        });
        return resp;
      },
    },
  },
  id: 'waterfee-meter-alert-index',
};

const [BasicTable, tableApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

// 查看详情
function handleDetail(row: Record<string, any>) {
  message.info(`查看告警详情，告警ID: ${row.alertId}`);
  // 这里可以添加查看详情的逻辑，例如打开详情抽屉或弹窗
}

// 处理报警
function handleProcess(row: Record<string, any>) {
  currentAlert.value = {
    alertId: row.alertId,
    alertContent: row.alertContent,
  };
  processModalVisible.value = true;
}

// 删除报警
function handleDelete(row: Record<string, any>) {
  Modal.confirm({
    title: '确认删除',
    icon: h(ExclamationCircleOutlined),
    content: `确定要删除该报警事件吗？`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      try {
        await deleteMeterAlert(row.alertId);
        message.success('删除成功');
        tableApi.query();
      } catch (error) {
        console.error('删除报警事件失败:', error);
        message.error(`删除失败：${error.message || '未知错误'}`);
      }
    },
  });
}

// 批量删除
async function handleBatchDelete() {
  const selections = await tableApi.getSelections();
  if (selections.length === 0) {
    message.warning('请至少选择一条记录');
    return;
  }

  const alertIds = selections.map((item) => item.alertId);

  Modal.confirm({
    title: '确认批量删除',
    icon: h(ExclamationCircleOutlined),
    content: `确定要删除选中的 ${alertIds.length} 条报警事件吗？`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      try {
        await deleteMeterAlert(alertIds);
        message.success('批量删除成功');
        tableApi.query();
      } catch (error) {
        console.error('批量删除报警事件失败:', error);
        message.error(`批量删除失败：${error.message || '未知错误'}`);
      }
    },
  });
}

// 导出数据
async function handleExport() {
  try {
    const formValues = await tableApi.getFormValues();

    // 创建额外的表头映射
    const dictHeaders = [
      { label: '告警类型翻译', prop: 'alertTypeName' },
      { label: '告警级别翻译', prop: 'alertLevelName' },
      { label: '处理状态翻译', prop: 'alertStatusName' },
      { label: '水表厂家翻译', prop: 'manufacturerName' },
      { label: '水表口径翻译', prop: 'caliberName' },
    ];

    await commonDownloadExcel(exportMeterAlert, '水表报警事件数据', {
      ...formValues,
      headers: dictHeaders,
    });

    message.success('导出成功');
  } catch (error) {
    console.error('导出报警事件数据失败:', error);
    message.error(`导出失败：${error.message || '未知错误'}`);
  }
}

// 处理成功回调
function handleProcessSuccess() {
  tableApi.query();
}

const schemas = querySchema();

// 获取字典数据
const initDicts = async () => {
  try {
    const [alertTypeDict, alertLevelDict, alertStatusDict] =
      await Promise.all([
        getDictOptions('waterfee_meter_alert_type'),
        getDictOptions('waterfee_meter_alert_level'),
        getDictOptions('waterfee_meter_alert_status'),
      ]);

    for (const schema of schemas) {
      switch (schema.fieldName) {
        case 'alertType': {
          schema.componentProps.options = alertTypeDict;
          break;
        }
        case 'alertLevel': {
          schema.componentProps.options = alertLevelDict;
          break;
        }
        case 'alertStatus': {
          schema.componentProps.options = alertStatusDict;
          break;
        }
      }
    }
  } catch (error) {
    console.error('获取字典数据失败:', error);
  }
};

onMounted(() => {
  initDicts();
});
</script>

<template>
  <Page :auto-content-height="true">
    <BasicTable @reload="tableApi.query()" table-title="水表报警事件">
      <template #toolbar-tools>
        <Space>
          <Button type="primary" danger @click="handleBatchDelete">
            批量删除
          </Button>
          <Button type="primary" @click="handleExport">
            <template #icon><DownloadOutlined /></template>
            导出
          </Button>
        </Space>
      </template>
      <template #action="{ row }">
        <Space size="small">
          <Button
            type="link"
            size="small"
            style="padding: 0 4px; height: 22px;"
            @click="handleProcess(row)"
            v-if="row.alertStatus !== '2'"
          >处理</Button>
          <Button
            type="link"
            danger
            size="small"
            style="padding: 0 4px; height: 22px;"
            @click="handleDelete(row)"
          >删除</Button>
        </Space>
      </template>
    </BasicTable>

    <!-- 处理报警弹窗 -->
    <ProcessModal
      v-model:visible="processModalVisible"
      :alert-id="currentAlert.alertId"
      :alert-content="currentAlert.alertContent"
      @success="handleProcessSuccess"
    />
  </Page>
</template>

<style scoped>
/* 可以添加自定义样式 */
.alert-level-1 {
  color: #1890ff;
}
.alert-level-2 {
  color: #faad14;
}
.alert-level-3 {
  color: #ff4d4f;
}
.alert-level-4 {
  color: #722ed1;
}

.alert-status-0 {
  color: #ff4d4f;
}
.alert-status-1 {
  color: #faad14;
}
.alert-status-2 {
  color: #52c41a;
}
</style>
