import type { PageQuery } from '#/api/common';
import type {
  UserCancellationRecordForm,
  UserCancellationRecordQuery,
  UserCancellationRecordVO,
} from '#/api/waterfee/user/cancellationRecord/model.d';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

enum Api {
  list = '/waterfee/userCancellationRecord/list',
  root = '/waterfee/userCancellationRecord',
}

/**
 * 用水用户销户记录导出
 * @param data data
 * @returns void
 */
export function UserCancellationRecordExport(
  data: Partial<UserCancellationRecordForm>,
) {
  return commonExport(`${Api.root}/export`, data);
}

/**
 * 查询用水用户销户记录列表
 * @param params 查询参数
 * @returns 用水用户销户记录列表
 */
export function listUserCancellationRecord(
  params?: PageQuery & UserCancellationRecordQuery,
) {
  return requestClient.get<UserCancellationRecordVO>(Api.list, { params });
}

/**
 * 查询用水用户销户记录详细
 * @param cancellationId 用水用户销户记录ID
 * @returns 用水用户销户记录信息
 */
export function getUserCancellationRecord(cancellationId: number | string) {
  return requestClient.get<UserCancellationRecordForm>(
    `${Api.root}/${cancellationId}`,
  );
}

/**
 * 新增用水用户销户记录
 * @param data 新增数据
 * @returns void
 */
export function addUserCancellationRecord(data: UserCancellationRecordForm) {
  return requestClient.postWithMsg<void>(Api.root, data);
}

/**
 * 修改用水用户销户记录
 * @param data 修改数据
 * @returns void
 */
export function updateUserCancellationRecord(data: UserCancellationRecordForm) {
  return requestClient.putWithMsg<void>(Api.root, data);
}

/**
 * 删除用水用户销户记录
 * @param cancellationId 用水用户销户记录ID或ID数组
 * @returns void
 */
export function delUserCancellationRecord(
  cancellationId: Array<number | string> | number | string,
) {
  return requestClient.deleteWithMsg<void>(`${Api.root}/${cancellationId}`);
}
