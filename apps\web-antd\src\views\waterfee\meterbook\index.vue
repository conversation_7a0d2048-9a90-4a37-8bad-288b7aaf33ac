<template>
  <Page :auto-content-height="true">
    <BasicTable table-title="抄表手册">
      <template #toolbar-tools>
        <Space>
          <Button type="primary" @click="handleAdd">
            新增
          </Button>
        </Space>
      </template>
      <template #action="{ row }">
        <Space>
          <Button type="link" @click="handleEdit(row)">
            编辑
          </Button>
          <Popconfirm
            placement="left"
            title="确认删除?"
            @confirm="handleDelete(row)"
          >
            <Button type="link" danger @click.stop="">
              删除
            </Button>
          </Popconfirm>
        </Space>
      </template>
    </BasicTable>
    <MeterBookDrawer @reload="tableApi.query()" />
  </Page>
</template>

<script lang="ts" setup>
  import { Button, Space, Popconfirm } from 'ant-design-vue';
  import { useVbenVxeGrid } from '#/adapter/vxe-table';
  import { useVbenDrawer } from '@vben/common-ui';
  import { Page } from '@vben/common-ui';
  import { meterBookList, meterBookRemove } from '#/api/waterfee/meterbook';
  import { columns, querySchema } from './meterbook.data';
  import type { MeterBookModel } from '#/api/waterfee/model/meterbookModel';
  import type { ID } from '#/api/common';
  import meterBookDrawer from './meter-book-drawer.vue';

  const formOptions = {
    commonConfig: {
      componentProps: {
        allowClear: true,
      },
    },
    schema: querySchema(),
    wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
  };

  const gridOptions = {
    columns,
    height: 'auto',
    proxyConfig: {
      ajax: {
        query: async ({ page }: { page?: { currentPage: number; pageSize: number } }, formValues: Record<string, any> = {}) => {
          // Only pass the necessary search parameters to avoid long requests
          const resp = await meterBookList({
            pageNum: page?.currentPage,
            pageSize: page?.pageSize,
            ...formValues,
          });
          return resp;
        },
      },
    },
    id: 'waterfee-meterbook-index',
  };

  const [BasicTable, tableApi] = useVbenVxeGrid({
    formOptions,
    gridOptions,
  });
  
  const [MeterBookDrawer, drawerApi] = useVbenDrawer({
    connectedComponent: meterBookDrawer,
  });

  function handleAdd() {
    drawerApi.setData({});
    drawerApi.open();
  }

  function handleEdit(record: MeterBookModel) {
    drawerApi.setData({ id: record.id });
    drawerApi.open();
  }

  async function handleDelete(record: MeterBookModel) {
    if (record.id) {
      await meterBookRemove(record.id as ID);
      await tableApi.query();
    }
  }
</script>
