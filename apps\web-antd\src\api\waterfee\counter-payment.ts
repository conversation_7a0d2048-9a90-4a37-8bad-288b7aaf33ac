import type { BillModel } from './model/billModel';
import type { ID, PageResult } from '#/api/common';
import { requestClient } from '#/api/request';

enum Api {
  getUserByKeyword = '/waterfee/counter-payment/user',
  getUserById = '/waterfee/counter-payment/user/detail',
  searchUsers = '/waterfee/counter-payment/users/search',
  getUserBills = '/waterfee/counter-payment/bills',
  getUserPaymentDetails = '/waterfee/counter-payment/payment-details',
  // getUserMeterReadings = '/waterfee/counter-payment/meter-readings',
  payBills = '/waterfee/counter-payment/pay',
  addDeposit = '/waterfee/counter-payment/deposit',
  adjustConsumption = '/waterfee/counter-payment/adjust',
  calculateAdjustment = '/waterfee/counter-payment/calculate-adjustment',
  refundBill = '/waterfee/counter-payment/refund',
}

/**
 * 根据关键字查询用户信息
 * @param keyword 用户编号或姓名
 * @returns 用户信息
 */
export function getUserByKeyword(keyword: string) {
  return requestClient.get(`${Api.getUserByKeyword}`, { params: { keyword } });
}

/**
 * 根据用户ID查询详细信息
 * @param userId 用户ID
 * @returns 用户详细信息
 */
export function getUserById(userId: string) {
  return requestClient.get(`${Api.getUserById}`, { params: { userId } });
}

/**
 * 根据关键字模糊查询用户列表
 * @param keyword 关键字
 * @returns 用户列表
 */
export function searchUsersByKeyword(keyword: string) {
  return requestClient.get(`${Api.searchUsers}`, { params: { keyword } });
}

/**
 * 获取用户账单列表
 * @param userId 用户ID
 * @param params 查询参数，可以包含status（账单状态）或billMonth（账单月份）
 * @returns 账单列表
 */
export function getUserBills(userId: string, params?: { status?: string; billMonth?: string }) {
  return requestClient.get(`${Api.getUserBills}`, {
    params: {
      userId,
      ...(params || {})
    }
  });
}

/**
 * 获取用户缴费明细
 * @param userId 用户ID
 * @returns 缴费明细列表
 */
export function getUserPaymentDetails(userId: string) {
  return requestClient.get(`${Api.getUserPaymentDetails}`, { params: { userId } });
}

/**
 * 获取用户抄表记录
 * @param userId 用户ID
 * @returns 抄表记录列表
 */
// export function getUserMeterReadings(userId: string) {
//   return requestClient.get(`${Api.getUserMeterReadings}`, { params: { userId } });
// }

/**
 * 缴费
 * @param data 缴费数据
 * @returns void
 */
export function payBills(data: {
  billIds: Array<ID>;
  amount: number;
  paymentMethod: string;
  remark?: string;
}) {
  return requestClient.postWithMsg<void>(`${Api.payBills}`, data);
}

/**
 * 预存充值
 * @param data 充值数据
 * @returns void
 */
export function addDeposit(data: {
  userId: string;
  amount: number;
  paymentMethod: string;
  remark?: string;
}) {
  return requestClient.postWithMsg<void>(`${Api.addDeposit}`, data);
}

/**
 * 账单调整（支持调整用量、读数、附加费、违约金）
 * @param data 调整数据
 * @returns void
 */
export function adjustConsumption(data: {
  billId: ID;
  adjustmentVolume?: number;
  adjustmentReading?: number;
  adjustmentAdditionalCharge?: number;
  adjustmentSurcharge?: number;
  adjustmentType?: string;
  adjustmentReason: string;
}) {
  return requestClient.putWithMsg<void>(`${Api.adjustConsumption}`, data);
}

/**
 * 计算账单调整结果（不保存数据）
 * @param data 调整数据
 * @returns 计算结果
 */
export function calculateAdjustment(data: Record<string, any>) {
  return requestClient.post<any>(`${Api.calculateAdjustment}`, data);
}

/**
 * 账单退费
 * @param data 退费数据
 * @returns void
 */
export function refundBill(data: {
  billId: ID;
  refundAmount: number;
  refundReason: string;
  refundMethod: string;
}) {
  return requestClient.putWithMsg<void>(`${Api.refundBill}`, data);
}
