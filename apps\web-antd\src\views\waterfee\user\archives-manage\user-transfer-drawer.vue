<script setup lang="ts">
import type { UserTransferOwnershipRecordForm } from '#/api/waterfee/user/transferOwnershipRecord/model.d';

import { useVbenDrawer } from '@vben/common-ui';
import { cloneDeep } from '@vben/utils';

import { useVbenForm } from '#/adapter/form';
import { getUser, transferUser } from '#/api/waterfee/user/archivesManage';
import { getDictOptions } from '#/utils/dict';

const emit = defineEmits<{ reload: [] }>();

const [BasicForm, formApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'col-span-1',
  },
  layout: 'vertical',
  schema: [
    {
      component: 'Input',
      dependencies: {
        show: () => false,
        triggerFields: [''],
      },
      fieldName: 'userId',
      label: '用户ID',
    },
    {
      component: 'Input',
      fieldName: 'beforeUserName',
      label: '原用水户名称',
      componentProps: {
        disabled: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'beforePhoneNumber',
      label: '原手机号码',
      componentProps: {
        disabled: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'beforeCertificateType',
      label: '原证件类型',
      componentProps: {
        disabled: true,
        options: getDictOptions('certificate_type'),
      },
    },
    {
      component: 'Input',
      fieldName: 'beforeCertificateNumber',
      label: '原证件号码',
      componentProps: {
        disabled: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'beforeEmail',
      label: '原电子邮箱',
      componentProps: {
        disabled: true,
      },
    },
    {
      component: 'InputNumber',
      fieldName: 'beforeUseWaterNumber',
      label: '原用水人数',
      componentProps: {
        disabled: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'afterUserName',
      label: '新用水户名称',
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'afterPhoneNumber',
      label: '新手机号码',
      rules: 'required|phone',
    },
    {
      component: 'Select',
      fieldName: 'afterCertificateType',
      label: '新证件类型',
      componentProps: {
        options: getDictOptions('certificate_type'),
      },
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'afterCertificateNumber',
      label: '新证件号码',
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'afterEmail',
      label: '新电子邮箱',
      componentProps: {
        type: 'email',
      },
    },
    {
      component: 'InputNumber',
      fieldName: 'afterUseWaterNumber',
      label: '新用水人数',
      rules: 'required',
    },
  ],
  showDefaultActions: false,
  wrapperClass: 'grid-cols-2 gap-x-4',
});

const [BasicDrawer, drawerApi] = useVbenDrawer({
  title: '用户过户',
  onClosed: handleCancel,
  onConfirm: handleConfirm,
  onOpenChange: async (isOpen) => {
    if (!isOpen) {
      return null;
    }
    drawerApi.drawerLoading(true);
    const { row } = drawerApi.getData() as { row?: any };
    if (row && row.userId) {
      const record = await getUser(row.userId);
      if (!record) {
        drawerApi.drawerLoading(false);
        return;
      }
      // 设置原用户信息
      await formApi.setValues({
        userId: row.userId,
        beforeUserName: record.userName,
        beforePhoneNumber: record.phoneNumber,
        beforeCertificateType: record.certificateType,
        beforeCertificateNumber: record.certificateNumber,
        beforeEmail: record.email,
        beforeUseWaterNumber: record.useWaterNumber,
      });
    }
    drawerApi.drawerLoading(false);
  },
});

async function handleConfirm() {
  try {
    drawerApi.drawerLoading(true);
    const { valid } = await formApi.validate();
    if (!valid) {
      return;
    }
    // 可能会做数据处理 使用cloneDeep深拷贝
    const data = cloneDeep(
      await formApi.getValues(),
    ) as UserTransferOwnershipRecordForm;
    await transferUser(data);
    emit('reload');
    await handleCancel();
  } catch (error) {
    console.error(error);
  } finally {
    drawerApi.drawerLoading(false);
  }
}

async function handleCancel() {
  drawerApi.close();
  await formApi.resetForm();
}
</script>

<template>
  <BasicDrawer :close-on-click-modal="false" class="w-[800px]">
    <BasicForm />
  </BasicDrawer>
</template>
