import type { PageResult } from '#/api/common';
import type { PaymentDetailBo, PaymentDetailVo } from './model/paymentDetailModel';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

enum Api {
  PaymentDetailList = '/waterfee/paymentDetail/list',
  PaymentDetailDetail = '/waterfee/paymentDetail', // Base path for CRUD
  PaymentDetailExport = '/waterfee/paymentDetail/export'
}

 /**
  * @description 获取缴费明细列表
  */
export function paymentDetailList(params: PaymentDetailBo) {
  return requestClient.get<PageResult<PaymentDetailVo>>(Api.PaymentDetailList, { params });
}

 /**
  * @description 获取缴费明细详情
  */
export function getPaymentDetail(paymentDetailId: string) {
  return requestClient.get<PaymentDetailVo>(`${Api.PaymentDetailDetail}/${paymentDetailId}`);
}

 /**
  * @description 新增缴费明细
  */
export function addPaymentDetail(data: PaymentDetailBo) {
  return requestClient.postWithMsg<void>(Api.PaymentDetailDetail, data);
}

 /**
  * @description 修改缴费明细
  */
export function updatePaymentDetail(data: PaymentDetailBo) {
  return requestClient.putWithMsg<void>(Api.PaymentDetailDetail, data);
}

 /**
  * @description 删除缴费明细
  */
export function deletePaymentDetail(ids: string | string[]) {
  return requestClient.deleteWithMsg<void>(`${Api.PaymentDetailDetail}/${Array.isArray(ids) ? ids.join(',') : ids}`);
}

/**
 * @description 导出缴费明细
 */
export function paymentDetailExport(data: Partial<PaymentDetailBo>) {
  return commonExport(Api.PaymentDetailExport, data);
}