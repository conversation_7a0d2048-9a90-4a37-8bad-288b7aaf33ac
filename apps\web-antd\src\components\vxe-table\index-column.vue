<script lang="ts" setup>
import { computed } from 'vue';

// 定义组件属性
const props = defineProps({
  // 字典数据
  dicts: {
    type: Array,
    default: () => [],
  },
  // 行索引，确保不为null
  rowIndex: {
    type: Number,
    default: 0,
  },
  // 值，可能为null，我们会处理它
  value: {
    type: [Number, String],
    default: '',
  },
});

// 计算实际显示的值，确保不为null
const displayValue = computed(() => {
  // 如果value不为null，则使用value
  if (props.value !== null && props.value !== undefined) {
    return props.value;
  }
  // 否则使用行索引+1作为序号
  return props.rowIndex + 1;
});
</script>

<template>
  <span>{{ displayValue }}</span>
</template>
