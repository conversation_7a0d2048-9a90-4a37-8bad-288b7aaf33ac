import { requestClient } from '#/api/request';
import { preserveBigInt } from '#/utils/json-bigint';

// 册内调整API
enum Api {
  sort = '/waterfee/meter/adjustment/sort',
}

// 水表排序项
export interface MeterSortItem {
  meterId: string;
  sortNo: number;
}

// 册内调整排序模型
export interface MeterSortAdjustmentModel {
  bookId: string;
  operationType: 'batch' | 'custom' | 'down' | 'up';
  meterId?: string; // 用于上移、下移操作
  customSortNo?: number; // 用于自定义序号操作
  meterSortItems: MeterSortItem[]; // 用于批量排序操作
}

/**
 * 调整水表在册内的排序
 * @param data 排序调整参数
 * @returns 调整结果
 */
export function adjustMeterSort(data: MeterSortAdjustmentModel) {
  // 保留大整数精度
  const safeData = preserveBigInt(data);

  // 确保 meterSortItems 字段存在，即使是空数组
  if (!safeData.meterSortItems) {
    safeData.meterSortItems = [];
  }

  console.log('调用排序调整API:', Api.sort, '数据:', safeData);

  return requestClient
    .post(Api.sort, safeData)
    .then((response) => {
      console.log('排序调整API响应:', response);
      return response;
    })
    .catch((error) => {
      console.error('排序调整API调用失败:', error);
      throw error;
    });
}
