import type { BaseEntity } from '#/api/common';

/**
 * 缴费明细查询参数
 */
export interface PaymentDetailBo extends BaseEntity {
  paymentDetailId?: string; // 缴费明细ID (optional for creation)
  billId?: string; // 关联账单ID
  billNumber?: string; // 账单编号
  userNo?: string; // 用户编号
  userName?: string; // 用户名
  address?: string; // 用水地址
  paymentAmount?: number; // 缴费金额
  paymentMethod?: string; // 缴费方式 (e.g., WECHAT_PAY, ALIPAY)
  transactionId?: string; // 交易流水号
  paymentStatus?: string; // 支付状态 (e.g., PENDING, SUCCESS, FAILED)
  paymentTime?: string; // 缴费时间 (ISO 8601 format)
  createBy?: string; // 收款人
  balanceDue?: number; // 未缴余额
  remark?: string; // 备注
}

/**
 * 缴费明细视图对象
 */
export interface PaymentDetailVo extends BaseEntity {
  paymentDetailId: string; // 缴费明细ID
  billId: string; // 关联账单ID
  billNumber: string; // 账单编号
  userNo: string; // 用户编号
  userName: string; // 用户名
  address: string; // 用水地址
  paymentAmount: number; // 缴费金额
  paymentMethod: string; // 缴费方式
  transactionId: string; // 交易流水号
  paymentStatus: string; // 支付状态
  paymentTime: string; // 缴费时间
  createBy?: string; // 收款人
  balanceDue?: number; // 未缴余额
  remark?: string; // 备注
  createTime: string; // 创建时间
  updateTime?: string; // 更新时间
}