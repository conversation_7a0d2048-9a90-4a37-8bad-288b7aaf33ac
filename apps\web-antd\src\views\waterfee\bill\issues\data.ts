import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { getPopupContainer } from '@vben/utils';
import { h } from 'vue';
import { Tag } from 'ant-design-vue';
// import { renderDict } from '#/utils/render';

// 查询表单模式
export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'meterBookName',
    label: '表册名称',
  },
  {
    component: 'Input',
    fieldName: 'meterReader',
    label: '抄表员',
  },
  {
    component: 'DatePicker',
    componentProps: {
      getPopupContainer,
      valueFormat: 'YYYY-MM',
      format: 'YYYY-MM',
      picker: 'month',
    },
    fieldName: 'billMonth',
    label: '账单月份',
  },
  {
    component: 'Select',
    componentProps: {
      getPopupContainer,
      options: [
        { label: '已发行', value: '已发行' },
        { label: '未发行', value: '未发行' },
      ],
    },
    fieldName: 'executeStatus',
    label: '发行状态',
  },
];

// 表格列定义
export const columns: VxeGridProps['columns'] = [
  {
    type: 'checkbox',
    width: 40,
  },
  {
    title: '序号',
    field: 'meterBookId',
    width: 180,
    align: 'center',
  },
  {
    title: '表册名称',
    field: 'meterBookName',
    align: 'center',
  },
  // {
  //   title: '抄表员',
  //   field: 'meterReader',
  //   align: 'center',
  // },
  {
    title: '抄表员',
    field: 'meterReaderName',
    align: 'center',
  },
  {
    title: '账单月份',
    field: 'billMonth',
    align: 'center',
  },
  {
    title: '水表用户',
    field: 'totalBills',
    align: 'center',
  },
  {
    title: '总计用户',
    field: 'totalUsers',
    align: 'center',
  },
  {
    title: '已付用户',
    field: 'paidBills',
    align: 'center',
  },
  {
    title: '未付用户',
    field: 'unpaidBills',
    align: 'center',
  },
  {
    title: '发行状态',
    field: 'executeStatus',
    align: 'center',
    slots: {
      default: ({ row }) => {
        if (row.executeStatus === '已发行') {
          return h(Tag, { color: 'success' }, () => '已发行');
        } else {
          return h(Tag, { color: 'default' }, () => '未发行');
        }
      }
    }
  },
  {
    title: '发行人',
    field: 'executor',
    align: 'center',
  },
  {
    title: '发行时间',
    field: 'executeTime',
    align: 'center',
  },
  {
    title: '操作',
    field: 'action',
    width: 220,
    fixed: 'right',
    slots: {
      default: 'action',
    },
  },
];

// 账单详情表格列定义
export const detailColumns: VxeGridProps['columns'] = [
  {
    type: 'checkbox',
    width: 40,
  },
  {
    title: '序号',
    field: 'billId',
    width: 180,
    align: 'center',
  },
  {
    title: '用户编号',
    field: 'userNo',
    align: 'center',
  },
  {
    title: '用户名称',
    field: 'userName',
    align: 'left',
  },
  {
    title: '账单编号',
    field: 'billNumber',
    align: 'center',
  },
  {
    title: '账单月份',
    field: 'billMonth',
    align: 'center',
  },
  {
    title: '本期水费金额',
    field: 'totalAmount',
    align: 'center',
    formatter: ({ row }) => {
      return row.totalAmount ? `${row.totalAmount}` : '0';
    },
  },
  {
    title: '用水量',
    field: 'consumptionVolume',
    align: 'center',
    formatter: ({ row }) => {
      return row.consumptionVolume ? `${row.consumptionVolume}` : '0';
    },
  },
  {
    title: '已付金额',
    field: 'amountPaid',
    align: 'center',
    formatter: ({ row }) => {
      return row.amountPaid ? `${row.amountPaid}` : '0';
    },
  },
  {
    title: '账单状态',
    field: 'billStatus',
    align: 'center',
    slots: {
      default: ({ row }) => {
        if (row.billStatus === 'DRAFT') {
          return h(Tag, { color: 'blue' }, () => '草稿');
        } else if (row.billStatus === 'ISSUED') {
          return h(Tag, { color: 'green' }, () => '已发行');
        } else if (row.billStatus === 'PAID') {
          return h(Tag, { color: 'success' }, () => '已支付');
        } else if (row.billStatus === 'OVERDUE') {
          return h(Tag, { color: 'red' }, () => '已逾期');
        } else {
          return h(Tag, { color: 'default' }, () => '未知');
        }
      }
    }
  },
  {
    title: '发行人',
    field: 'executor',
    align: 'center',
  },
  {
    title: '发行时间',
    field: 'executeTime',
    align: 'center',
  },
  {
    title: '操作',
    field: 'action',
    width: 220,
    fixed: 'right',
    slots: {
      default: 'action',
    },
  },
];

