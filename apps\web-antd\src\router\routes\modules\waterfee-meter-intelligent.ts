import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    path: '/waterfee/meter/intelligent',
    name: 'WaterfeeIntelligentMeter',
    component: () => import('#/views/waterfee/meter/intelligent/index.vue'),
    meta: {
      title: '智能水表管理',
    },
  },
  {
    path: '/waterfee/meter/intelligent/detail',
    name: 'WaterfeeIntelligentMeterDetail',
    component: () => import('#/views/waterfee/meter/intelligent/detail/index.vue'),
    meta: {
      title: '智能水表详情',
      hideMenu: true,
    },
  },
];

export default routes;
