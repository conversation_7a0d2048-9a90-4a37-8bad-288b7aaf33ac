<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { Button, Space, message } from 'ant-design-vue';
import { Page } from '@vben/common-ui';
import { EyeOutlined, DownloadOutlined, SyncOutlined } from '@ant-design/icons-vue';
import { getReadingTaskList, exportReadingTask, updateReadingTaskCount, updateReadingTaskCountBatch } from '#/api/waterfee/meterReading';
import { columns, querySchema } from './task.data';
import { preserveBigInt } from '#/utils/json-bigint';
import { commonDownloadExcel } from '#/utils/file/download';
import { useDictStore } from '#/store/dict';
import { getDictOptions } from '#/utils/dict';
import { initAllOptions } from './utils/options';

// 引入组件
import ReadingTaskDrawer from '../components/ReadingTaskDrawer.vue';

const dictStore = useDictStore();

// 表单配置
const formOptions = {
  commonConfig: {
    labelWidth: 80,
    componentProps: {
      allowClear: true,
    },
  },
  schema: querySchema(),
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
};

// 表格配置
const gridOptions = {
  columns,
  height: 'auto',
  width: '100%',
  fit: true,
  autoResize: true,
  tableLayout: 'auto',
  scrollX: {
    enabled: true,
    gt: 10,
  },
  resizable: true,
  resizeConfig: {
    minWidth: 80,
  },
  tooltipConfig: {
    showAll: true,
  },
  rowConfig: {
    isHover: true,
    // 设置行索引起始值为0，这样序号列显示的值就是从1开始
    // 因为我们在序号列中使用了 rowIndex + 1
    indexMethod: (row) => row._XID,
  },
  checkboxConfig: {
    // 高亮
    highlight: true,
    // 翻页时保留选中状态
    reserve: true,
  },
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues = {}) => {
        // 只查询任务状态正常的任务
        const resp = await getReadingTaskList({
          pageNum: page?.currentPage,
          pageSize: page?.pageSize,
          taskStatus: '1', // 任务状态正常
          ...formValues,
        });
        return resp;
      },
    },
  },
  id: 'waterfee-meter-reading-task-index',
};

// 初始化表格
const [BasicTable, tableApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

// 抽屉状态
const drawerOpen = ref(false);
const drawerProps = ref({
  id: '',
  readonly: true, // 默认只读模式
});

// 查看抄表任务详情
function handleDetail(row) {
  try {
    // 确保ID不会丢失精度
    const safeRecord = preserveBigInt(row);
    const taskId = String(safeRecord.taskId);

    console.log('查看抄表任务详情, ID:', taskId);

    drawerProps.value = {
      id: taskId,
      readonly: true,
    };
    drawerOpen.value = true;
  } catch (error) {
    console.error('查看抄表任务详情失败:', error);
    message.error(`查看详情失败：${error.message || '未知错误'}`);
  }
}

// 导出数据
async function handleExport() {
  try {
    const formValues = await tableApi.getFormValues();
    // 只导出任务状态正常的任务
    formValues.taskStatus = '1';

    const blob = await exportReadingTask(formValues);
    commonDownloadExcel(blob, '抄表任务列表');
    message.success('导出成功');
  } catch (error) {
    console.error('导出失败:', error);
    message.error(`导出失败：${error.message || '未知错误'}`);
  }
}

// 更新表册关联用户数和水表数
async function handleUpdateCount(row) {
  try {
    // 确保ID不会丢失精度
    const safeRecord = preserveBigInt(row);
    const taskId = String(safeRecord.taskId);

    console.log('更新表册关联用户数和水表数, ID:', taskId);

    // 显示加载中提示
    const hide = message.loading('正在更新表册关联用户数和水表数...', 0);

    try {
      await updateReadingTaskCount(taskId);
      message.success('更新成功');
      // 刷新表格
      tableApi.query();
    } finally {
      // 关闭加载中提示
      hide();
    }
  } catch (error) {
    console.error('更新表册关联用户数和水表数失败:', error);
    message.error(`更新失败：${error.message || '未知错误'}`);
  }
}

// 批量更新表册关联用户数和水表数
async function handleBatchUpdateCount() {
  try {
    // 获取选中的行
    const selectedRows = tableApi.grid.getCheckboxRecords();

    if (!selectedRows || selectedRows.length === 0) {
      message.warning('请选择要更新的抄表任务');
      return;
    }

    // 确保ID不会丢失精度
    const taskIds = selectedRows.map(row => {
      const safeRecord = preserveBigInt(row);
      return String(safeRecord.taskId);
    });

    console.log('批量更新表册关联用户数和水表数, IDs:', taskIds);

    // 显示加载中提示
    const hide = message.loading(`正在批量更新${taskIds.length}个抄表任务的表册关联用户数和水表数...`, 0);

    try {
      await updateReadingTaskCountBatch(taskIds);
      message.success('批量更新成功');
      // 刷新表格
      tableApi.query();
    } finally {
      // 关闭加载中提示
      hide();
    }
  } catch (error) {
    console.error('批量更新表册关联用户数和水表数失败:', error);
    message.error(`批量更新失败：${error.message || '未知错误'}`);
  }
}

// 操作成功后刷新表格
function handleSuccess() {
  tableApi.query();
}

// 组件挂载时初始化所有选项
onMounted(async () => {
  try {
    await initAllOptions();
    console.log('抄表任务页面选项初始化完成');
  } catch (error) {
    console.error('初始化选项失败:', error);
    message.error('加载选项数据失败，请刷新页面重试');
  }
});
</script>

<template>
  <Page :auto-content-height="true">
    <BasicTable @reload="tableApi.query()" table-title="抄表任务列表">
      <template #toolbar-tools>
        <Space>
          <Button type="primary" @click="handleBatchUpdateCount">
            <template #icon><SyncOutlined /></template>
            更新用户数
          </Button>
          <!-- <Button type="primary" @click="handleExport">
            <template #icon><DownloadOutlined /></template>
            导出
          </Button> -->
        </Space>
      </template>
    </BasicTable>

    <!-- 抄表任务抽屉 -->
    <ReadingTaskDrawer
      :open="drawerOpen"
      @update:open="drawerOpen = $event"
      :id="drawerProps.id"
      :readonly="drawerProps.readonly"
      @reload="handleSuccess"
    />
  </Page>
</template>

<style scoped>
/* 自定义样式 */
</style>
