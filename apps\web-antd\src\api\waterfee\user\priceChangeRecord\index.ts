import type { PageQuery } from '#/api/common';
import type {
  UserPriceChangeRecordForm,
  UserPriceChangeRecordQuery,
  UserPriceChangeRecordVO,
} from '#/api/waterfee/user/priceChangeRecord/model.d';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

enum Api {
  list = '/waterfee/userPriceChangeRecord/list',
  root = '/waterfee/userPriceChangeRecord',
}

/**
 * 用水用户用水价格变更记录导出
 * @param data data
 * @returns void
 */
export function UserPriceChangeRecordExport(
  data: Partial<UserPriceChangeRecordForm>,
) {
  return commonExport(`${Api.root}/export`, data);
}

/**
 * 查询用水用户用水价格变更记录列表
 * @param params 查询参数
 * @returns 用水用户用水价格变更记录列表
 */
export function listUserPriceChangeRecord(
  params?: PageQuery & UserPriceChangeRecordQuery,
) {
  return requestClient.get<UserPriceChangeRecordVO>(Api.list, { params });
}

/**
 * 查询用水用户用水价格变更记录详细
 * @param priceChangeId 用水用户用水价格变更记录ID
 * @returns 用水用户用水价格变更记录信息
 */
export function getUserPriceChangeRecord(priceChangeId: number | string) {
  return requestClient.get<UserPriceChangeRecordForm>(
    `${Api.root}/${priceChangeId}`,
  );
}

/**
 * 新增用水用户用水价格变更记录
 * @param data 新增数据
 * @returns void
 */
export function addUserPriceChangeRecord(data: UserPriceChangeRecordForm) {
  return requestClient.postWithMsg<void>(Api.root, data);
}

/**
 * 修改用水用户用水价格变更记录
 * @param data 修改数据
 * @returns void
 */
export function updateUserPriceChangeRecord(data: UserPriceChangeRecordForm) {
  return requestClient.putWithMsg<void>(Api.root, data);
}

/**
 * 删除用水用户用水价格变更记录
 * @param priceChangeId 用水用户用水价格变更记录ID或ID数组
 * @returns void
 */
export function delUserPriceChangeRecord(
  priceChangeId: Array<number | string> | number | string,
) {
  return requestClient.deleteWithMsg<void>(`${Api.root}/${priceChangeId}`);
}
