import type { BasicPageParams, BasicFetchResult } from '#/api/types/common';
import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';
import { preserveBigInt } from '#/utils/json-bigint';

// 水表报警事件API
enum Api {
  list = '/waterfee/meterAlert/list',
  detail = '/waterfee/meterAlert',
  add = '/waterfee/meterAlert',
  update = '/waterfee/meterAlert',
  delete = '/waterfee/meterAlert',
  export = '/waterfee/meterAlert/export',
  process = '/waterfee/meterAlert/process',
}

// 水表报警事件模型
export interface MeterAlertModel {
  alertId: string;
  meterId: string;
  meterNo: string;
  userId?: string;
  businessAreaId?: string;
  alertType: string;
  alertContent: string;
  alertLevel: string;
  alertStatus: string;
  alertTime: string;
  processTime?: string;
  processUser?: string;
  processResult?: string;
  remark?: string;
  createTime: string;
  userName?: string;
  userNo?: string;
  businessAreaName?: string;
  waterAddress?: string;
  caliber?: string;
  installEnvironment?: string;
  manufacturer?: string;
  communicationMode?: string;
  [key: string]: any;
}

// 水表报警事件查询参数
export interface MeterAlertParams extends BasicPageParams {
  meterNo?: string;
  alertType?: string;
  alertLevel?: string;
  alertStatus?: string;
  alertTimeRange?: [string, string];
  userNo?: string;
  userName?: string;
  businessAreaId?: string;
  [key: string]: any;
}

// 获取水表报警事件列表
export function getMeterAlertList(params?: MeterAlertParams) {
  // 处理查询参数
  const queryParams = params ? { ...params } : {};

  // 处理日期范围
  if (
    queryParams.alertTimeRange &&
    Array.isArray(queryParams.alertTimeRange)
  ) {
    queryParams.alertTimeStart = queryParams.alertTimeRange[0];
    queryParams.alertTimeEnd = queryParams.alertTimeRange[1];
    delete queryParams.alertTimeRange;
  }

  // 保留大整数精度
  const safeParams = preserveBigInt(queryParams);

  return requestClient.get<BasicFetchResult<MeterAlertModel[]>>(
    Api.list,
    {
      params: safeParams,
    },
  );
}

// 获取水表报警事件详情
export function getMeterAlertInfo(alertId: string) {
  return requestClient.get<MeterAlertModel>(
    `${Api.detail}/${alertId}`,
  );
}

// 添加水表报警事件
export function addMeterAlert(data: MeterAlertModel) {
  return requestClient.post(Api.add, preserveBigInt(data));
}

// 更新水表报警事件
export function updateMeterAlert(data: MeterAlertModel) {
  return requestClient.put(Api.update, preserveBigInt(data));
}

// 删除水表报警事件
export function deleteMeterAlert(alertIds: string | string[]) {
  return requestClient.delete(`${Api.delete}/${alertIds}`);
}

// 处理水表报警事件
export function processMeterAlert(alertId: string, processResult: string) {
  return requestClient.put(`${Api.process}/${alertId}?processResult=${encodeURIComponent(processResult)}`);
}

// 导出水表报警事件
export function exportMeterAlert(params?: MeterAlertParams) {
  // 确保params是一个对象，防止undefined导致错误
  const safeParams = params ? preserveBigInt(params) : {};
  return commonExport(Api.export, safeParams);
}
