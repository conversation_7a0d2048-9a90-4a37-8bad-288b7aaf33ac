import { requestClient } from '#/api/request';
import type { BasicFetchResult, BasicPageParams } from '#/api/types/common';
import { preserveBigInt } from '#/utils/json-bigint';

// 册本调整API
enum Api {
  list = '/waterfee/meter/adjustment/list',
  updateBook = '/waterfee/meter/adjustment/book',
}

// 册本调整查询参数
export interface MeterAdjustmentParams extends BasicPageParams {
  bookId?: string;
  meterNo?: string;
  userNo?: string;
  userName?: string;
  [key: string]: any;
}

// 册本调整信息模型
export interface MeterAdjustmentModel {
  meterId?: string;
  meterNo?: string;
  userId?: string;
  userNo?: string;
  userName?: string;
  businessAreaId?: string;
  businessAreaName?: string;
  meterBookId?: string;
  meterBookName?: string;
  installAddress?: string;
  [key: string]: any;
}

// 册本调整更新模型
export interface MeterAdjustmentUpdateModel {
  meterIds: string[];
  businessAreaId: string;
  meterBookId: string;
}

// 获取册本调整列表
export function getMeterAdjustmentList(bookId: string, params?: MeterAdjustmentParams) {
  // 处理查询参数
  const queryParams = params ? { ...params } : {};

  // 保留大整数精度
  const safeParams = preserveBigInt(queryParams);

  console.log(`调用API: ${Api.list}/${bookId}，参数:`, safeParams);

  // 使用 any 类型接收响应，因为后端可能返回不同格式的数据
  return requestClient.get<any>(`${Api.list}/${bookId}`, {
    params: safeParams,
  }).then(response => {
    console.log('API响应数据:', response);

    // 检查响应格式
    if (response && response.code === 200) {
      console.log('响应包含 code 字段，可能是标准响应格式');
      return response;
    } else if (Array.isArray(response)) {
      console.log('响应是数组格式');
      return response;
    } else if (response && response.rows) {
      console.log('响应包含 rows 字段，是分页格式');
      return response;
    }

    // 如果无法识别格式，返回原始响应
    return response;
  }).catch(error => {
    console.error('API调用失败:', error);
    throw error;
  });
}

// 更新水表所属册本和区域
export function updateMeterBook(data: MeterAdjustmentUpdateModel) {
  console.log('调用更新API:', Api.updateBook, '数据:', data);

  return requestClient.post(Api.updateBook, data).then(response => {
    console.log('更新API响应:', response);
    return response;
  }).catch(error => {
    console.error('更新API调用失败:', error);
    throw error;
  });
}
