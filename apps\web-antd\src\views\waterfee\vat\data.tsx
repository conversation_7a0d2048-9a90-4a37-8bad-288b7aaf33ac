import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'billNumber',
    label: '关联账单编号',
  },
  {
    component: 'Input',
    fieldName: 'invoiceCode',
    label: '发票代码',
  },
  {
    component: 'Input',
    fieldName: 'invoiceNumber',
    label: '发票号码',
  },
  {
    component: 'Input',
    fieldName: 'totalAmount',
    label: '价税合计金额',
  },
  {
    component: 'Input',
    fieldName: 'taxExclusiveAmount',
    label: '不含税金额',
  },
  {
    component: 'Input',
    fieldName: 'taxRate',
    label: '税率',
  },
  {
    component: 'Input',
    fieldName: 'taxAmount',
    label: '税额',
  },
  {
    component: 'Input',
    fieldName: 'isTaxIncluded',
    label: '是否含税',
  },
  {
    component: 'Input',
    fieldName: 'buyerName',
    label: '购方名称',
  },
  {
    component: 'Input',
    fieldName: 'buyerTaxId',
    label: '购方纳税人识别号',
  },
  {
    component: 'Input',
    fieldName: 'buyerAddressTel',
    label: '购方地址电话',
  },
  {
    component: 'Input',
    fieldName: 'buyerBankAccount',
    label: '购方开户行及账号',
  },
  {
    component: 'Input',
    fieldName: 'sellerName',
    label: '销方名称',
  },
  {
    component: 'Input',
    fieldName: 'sellerTaxId',
    label: '销方纳税人识别号',
  },
  {
    component: 'Input',
    fieldName: 'sellerAddressTel',
    label: '销方地址电话',
  },
  {
    component: 'Input',
    fieldName: 'sellerBankAccount',
    label: '销方开户行及账号',
  },
  {
    component: 'DatePicker',
    fieldName: 'issueTime',
    label: '开票时间',
  },
  {
    component: 'DatePicker',
    fieldName: 'redFlushTime',
    label: '红冲时间',
  },
  {
    component: 'DatePicker',
    fieldName: 'cancelTime',
    label: '作废时间',
  },
  {
    component: 'Input',
    fieldName: 'operatorId',
    label: '操作人ID',
  },
  {
    component: 'Input',
    fieldName: 'redFlushReason',
    label: '红冲原因',
  },
  {
    component: 'Input',
    fieldName: 'cancelReason',
    label: '作废原因',
  },
];

export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
  {
    title: '主键ID',
    field: 'id',
  },
  {
    title: '关联账单编号',
    field: 'billNumber',
  },
  {
    title: '发票类型',
    field: 'invoiceType',
  },
  {
    title: '发票代码',
    field: 'invoiceCode',
  },
  {
    title: '发票号码',
    field: 'invoiceNumber',
  },
  {
    title: '发票状态',
    field: 'invoiceStatus',
  },
  {
    title: '价税合计金额',
    field: 'totalAmount',
  },
  {
    title: '不含税金额',
    field: 'taxExclusiveAmount',
  },
  {
    title: '税率',
    field: 'taxRate',
  },
  {
    title: '税额',
    field: 'taxAmount',
  },
  {
    title: '是否含税',
    field: 'isTaxIncluded',
  },
  {
    title: '购方名称',
    field: 'buyerName',
  },
  {
    title: '购方纳税人识别号',
    field: 'buyerTaxId',
  },
  {
    title: '购方地址电话',
    field: 'buyerAddressTel',
  },
  {
    title: '购方开户行及账号',
    field: 'buyerBankAccount',
  },
  {
    title: '销方名称',
    field: 'sellerName',
  },
  {
    title: '销方纳税人识别号',
    field: 'sellerTaxId',
  },
  {
    title: '销方地址电话',
    field: 'sellerAddressTel',
  },
  {
    title: '销方开户行及账号',
    field: 'sellerBankAccount',
  },
  {
    title: '开票时间',
    field: 'issueTime',
  },
  {
    title: '红冲时间',
    field: 'redFlushTime',
  },
  {
    title: '作废时间',
    field: 'cancelTime',
  },
  {
    title: '操作人ID',
    field: 'operatorId',
  },
  {
    title: '红冲原因',
    field: 'redFlushReason',
  },
  {
    title: '作废原因',
    field: 'cancelReason',
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 180,
  },
];

export const drawerSchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
    fieldName: 'id',
    label: '主键',
  },
  {
    component: 'Input',
    fieldName: 'billNumber',
    label: '关联账单编号',
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'invoiceCode',
    label: '发票代码',
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'invoiceNumber',
    label: '发票号码',
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'totalAmount',
    label: '价税合计金额',
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'taxExclusiveAmount',
    label: '不含税金额',
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'taxRate',
    label: '税率',
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'taxAmount',
    label: '税额',
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'isTaxIncluded',
    label: '是否含税',
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'buyerName',
    label: '购方名称',
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'buyerTaxId',
    label: '购方纳税人识别号',
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'buyerAddressTel',
    label: '购方地址电话',
  },
  {
    component: 'Input',
    fieldName: 'buyerBankAccount',
    label: '购方开户行及账号',
  },
  {
    component: 'Input',
    fieldName: 'sellerName',
    label: '销方名称',
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'sellerTaxId',
    label: '销方纳税人识别号',
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'sellerAddressTel',
    label: '销方地址电话',
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'sellerBankAccount',
    label: '销方开户行及账号',
    rules: 'required',
  },
  {
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
    fieldName: 'issueTime',
    label: '开票时间',
    rules: 'required',
  },
  {
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
    fieldName: 'redFlushTime',
    label: '红冲时间',
  },
  {
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
    fieldName: 'cancelTime',
    label: '作废时间',
  },
  {
    component: 'Input',
    fieldName: 'operatorId',
    label: '操作人ID',
  },
  {
    component: 'Input',
    fieldName: 'redFlushReason',
    label: '红冲原因',
  },
  {
    component: 'Input',
    fieldName: 'cancelReason',
    label: '作废原因',
  },
];
