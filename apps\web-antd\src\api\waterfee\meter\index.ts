/**
 * 水表ID处理工具函数
 * 专门处理水表ID，确保前端不会丢失精度
 */

import { ContentTypeEnum } from '../../../api/helper';
import { requestClient } from '../../../api/request';
import { preserveBigInt } from '../../../utils/json-bigint';

/**
 * 确保ID以字符串形式处理，避免精度丢失
 * @param id ID值
 * @returns 字符串形式的ID
 */
export function ensureMeterIdString(id: any): string {
  if (id === null || id === undefined) {
    return '';
  }
  // 处理大整数的情况
  if (typeof id === 'number' && id.toString().length > 15) {
    const jsonStr = JSON.stringify({ temp: id });
    const parsed = JSON.parse(jsonStr);
    return String(parsed.temp);
  }
  return String(id);
}

/**
 * 通过JSON序列化和反序列化保留ID精度
 * @param obj 包含ID的对象
 * @returns 处理后的对象
 */
export function preserveIdPrecision<T>(obj: T): T {
  return preserveBigInt(obj);
}

/**
 * 导入水表数据
 * @param formData 包含上传文件和水表类型的表单数据
 * @returns 导入结果
 */
export function meterImportData(formData: FormData) {
  return requestClient.post<{ code: number; data: any; msg: string }>(
    `/waterfee/meter/importData`,
    formData,
    {
      headers: {
        'Content-Type': ContentTypeEnum.FORM_DATA,
      },
      isTransformResponse: false,
    },
  );
}

/**
 * 下载水表导入模板
 * @param meterType 水表类型 1-机械表 2-智能表
 * @returns 模板文件流
 */
export function meterImportTemplate(meterType: number) {
  return requestClient.get<Blob>(`/waterfee/meter/importTemplate`, {
    params: { meterType },
    responseType: 'blob',
    isTransformResponse: false,
  });
}
