export interface UserBasicInfoChangeRecordVO {
  /**
   * 主键
   */
  basicInfoChangeId: number | string;

  /**
   * 用户ID
   */
  userId: number | string;

  /**
   * 原客户性质（字典waterfee_user_customer_nature）
   */
  beforeCustomerNature: string;

  /**
   * 原用水性质（字典waterfee_user_use_water_nature）
   */
  beforeUseWaterNature: string;

  /**
   * 原用水人数
   */
  beforeUseWaterNumber: number;

  /**
   * 原手机号码
   */
  beforePhoneNumber: string;

  /**
   * 原用水地址
   */
  beforeAddress: string;

  /**
   * 原电子邮箱
   */
  beforeEmail: string;

  /**
   * 原纳税人识别号
   */
  beforeTaxpayerIdentificationNumber: number | string;

  /**
   * 原开票名称
   */
  beforeInvoiceName: string;

  /**
   * 原发票类型（字典waterfee_user_invoice_type）
   */
  beforeInvoiceType: string;

  /**
   * 新客户性质（字典waterfee_user_customer_nature）
   */
  afterCustomerNature: string;

  /**
   * 新用水性质（字典waterfee_user_use_water_nature）
   */
  afterUseWaterNature: string;

  /**
   * 新用水人数
   */
  afterUseWaterNumber: number;

  /**
   * 新手机号码
   */
  afterPhoneNumber: string;

  /**
   * 新用水地址
   */
  afterAddress: string;

  /**
   * 新电子邮箱
   */
  afterEmail: string;

  /**
   * 新纳税人识别号
   */
  afterTaxpayerIdentificationNumber: number | string;

  /**
   * 新开票名称
   */
  afterInvoiceName: string;

  /**
   * 新发票类型（字典waterfee_user_invoice_type）
   */
  afterInvoiceType: string;

  /**
   * 变更内容
   */
  changeContent: string;
}

export interface UserBasicInfoChangeRecordForm extends BaseEntity {
  /**
   * 主键
   */
  basicInfoChangeId?: number | string;

  /**
   * 用户ID
   */
  userId?: number | string;

  /**
   * 原客户性质（字典waterfee_user_customer_nature）
   */
  beforeCustomerNature?: string;

  /**
   * 原用水性质（字典waterfee_user_use_water_nature）
   */
  beforeUseWaterNature?: string;

  /**
   * 原用水人数
   */
  beforeUseWaterNumber?: number;

  /**
   * 原手机号码
   */
  beforePhoneNumber?: string;

  /**
   * 原用水地址
   */
  beforeAddress?: string;

  /**
   * 原电子邮箱
   */
  beforeEmail?: string;

  /**
   * 原纳税人识别号
   */
  beforeTaxpayerIdentificationNumber?: number | string;

  /**
   * 原开票名称
   */
  beforeInvoiceName?: string;

  /**
   * 原发票类型（字典waterfee_user_invoice_type）
   */
  beforeInvoiceType?: string;

  /**
   * 新客户性质（字典waterfee_user_customer_nature）
   */
  afterCustomerNature?: string;

  /**
   * 新用水性质（字典waterfee_user_use_water_nature）
   */
  afterUseWaterNature?: string;

  /**
   * 新用水人数
   */
  afterUseWaterNumber?: number;

  /**
   * 新手机号码
   */
  afterPhoneNumber?: string;

  /**
   * 新用水地址
   */
  afterAddress?: string;

  /**
   * 新电子邮箱
   */
  afterEmail?: string;

  /**
   * 新纳税人识别号
   */
  afterTaxpayerIdentificationNumber?: number | string;

  /**
   * 新开票名称
   */
  afterInvoiceName?: string;

  /**
   * 新发票类型（字典waterfee_user_invoice_type）
   */
  afterInvoiceType?: string;

  /**
   * 变更内容
   */
  changeContent?: string;
}

export interface UserBasicInfoChangeRecordQuery extends PageQuery {
  /**
   * 用户编号或用户名
   */
  userNoOrUserName?: string;

  /**
   * 查询开始时间
   */
  startTime?: string;

  /**
   * 查询结束时间
   */
  endTime?: string;
}
