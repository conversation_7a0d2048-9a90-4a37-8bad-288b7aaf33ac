<template>
  <Page :auto-content-height="true">
    <BasicTable table-title="账单列表">
      <template #toolbar-tools>
        <Space>
          <Button @click="handleRefresh">
            <template #icon><ReloadOutlined /></template>
            刷新
          </Button>
          <Button type="primary" ghost @click="exportExcel">
            <template #icon><DownloadOutlined /></template>
            导出
          </Button>
        </Space>
      </template>
      <template #action="{ row }">
        <Space>
          <Button type="link" @click="handleView(row)">
            查看
          </Button>
          <Button type="link" @click="handlePay(row)" v-if="row.billStatus === 'ISSUED'">
            支付
          </Button>
          <Button type="link" @click="handleAdjust(row)" v-if="row.billStatus === 'DRAFT'">
            调整
          </Button>
        </Space>
      </template>
    </BasicTable>

    <!-- 调整金额Modal -->
    <Modal
      v-model:open="adjustModalVisible"
      title="调整金额"
      width="500px"
      @ok="confirmAdjust"
      @cancel="cancelAdjust"
    >
      <Form layout="vertical" style="margin-top: 20px;">
        <FormItem label="账单信息" v-if="currentRecord">
          <div style="background: #f5f5f5; padding: 12px; border-radius: 4px;">
            <div>账单编号: {{ currentRecord.billNumber }}</div>
            <div>当前总金额: {{ currentRecord.totalAmount }} 元</div>
            <div>当前调整金额: {{ currentRecord.adjustmentsAmount || 0 }} 元</div>
          </div>
        </FormItem>

        <FormItem label="调整金额" required>
          <InputNumber
            v-model:value="adjustForm.adjustmentsAmount"
            :precision="2"
            :step="0.01"
            :min="-999999.99"
            :max="999999.99"
            style="width: 100%;"
            placeholder="请输入调整金额（正数为增加，负数为减少）"
            addon-after="元"
            @change="handleAmountChange"
          />
          <div style="color: #666; font-size: 12px; margin-top: 4px;">
            支持正负数，最多保留两位小数，范围：-999,999.99 到 999,999.99
          </div>
        </FormItem>

        <FormItem label="调整原因" required>
          <Input.TextArea
            v-model:value="adjustForm.adjustmentReason"
            :rows="3"
            placeholder="请输入调整原因"
            :maxlength="200"
            show-count
          />
        </FormItem>
      </Form>
    </Modal>
  </Page>
</template>

<script lang="ts" setup>
  import { Button, Space, message, Modal, Form, FormItem, InputNumber, Input } from 'ant-design-vue';
  import { ReloadOutlined, DownloadOutlined } from '@ant-design/icons-vue';
  import { useVbenVxeGrid } from '#/adapter/vxe-table';
  import { Page } from '@vben/common-ui';
  import { billList, billPay, billAdjustConsumption } from '#/api/waterfee/bill';
  import { columns, querySchema } from './data';
  // import type { BillModel } from '#/api/waterfee/model/billModel';
  import { ref, onMounted } from 'vue';
  import { useRouter, useRoute } from 'vue-router';

  const router = useRouter();
  const route = useRoute();
  const meterBookId = ref(null);

  // 调整金额Modal显示状态
  const adjustModalVisible = ref(false);
  const currentRecord = ref(null);

  // 支付表单
  const payForm = ref({
    amount: 0,
  });

  // 调整金额表单
  const adjustForm = ref({
    adjustmentsAmount: 0,
    adjustmentReason: '',
  });

  // 表单配置
  const formOptions = {
    commonConfig: {
      componentProps: {
        allowClear: true,
      },
    },
    schema: querySchema(),
    wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
  };

  // 表格配置
  const gridOptions = {
    columns,
    height: 'auto',
    proxyConfig: {
      ajax: {
        query: async ({ page }, formValues = {}) => {
          // 获取路由参数中的表册ID
          if (route.query.meterBookId) {
            meterBookId.value = Number(route.query.meterBookId);
          }

          // 构建查询参数
          const params = {
            pageNum: page?.currentPage,
            pageSize: page?.pageSize,
            ...formValues,
          };

          // 如果有表册ID，添加到查询参数中
          if (meterBookId.value) {
            params.meterBookId = meterBookId.value;
          }

          const resp = await billList(params)
          return resp;
        },
      },
    },
    id: 'waterfee-bill-list-index',
  };

  const [BasicTable, tableApi] = useVbenVxeGrid({
    formOptions,
    gridOptions,
  });

  // 组件挂载时初始化
  onMounted(() => {
    // 初始化表格数据
    tableApi.query();
  });

  // 查看账单详情
  function handleView(record) {
    console.log("账单id：",record.billId);
    if (record.billId) {
      router.push(`/charges/detail?id=${record.billId}`);
    }
  }

  // 处理支付账单
  function handlePay(record) {
    if (!record.billId) return;

    payForm.value.amount = record.balanceDue || 0;

    Modal.confirm({
      title: '支付账单',
      content: `账单编号: ${record.billNumber}\n账单总额: ${record.totalAmount} 元\n已支付金额: ${record.amountPaid} 元\n应付余额: ${record.balanceDue} 元`,
      onOk: async () => {
        try {
          await billPay(record.billId, payForm.value.amount);
          message.success('支付成功');
          tableApi.query();
        } catch (error) {
          console.error('支付失败:', error);
          message.error('支付失败，请稍后重试');
        }
      },
    });
  }

  // 处理调整金额
  function handleAdjust(record) {
    console.log("账单id：",record.billId);
    if (!record.billId) return;

    currentRecord.value = record;
    adjustForm.value.adjustmentsAmount = 0;
    adjustForm.value.adjustmentReason = '';
    adjustModalVisible.value = true;
  }

  // 处理金额输入变化
  function handleAmountChange(value) {
    // InputNumber组件已经处理了精度限制，这里只需要确保值在合理范围内
    if (value !== null && value !== undefined) {
      // 确保值在指定范围内
      if (value < -999999.99) {
        adjustForm.value.adjustmentsAmount = -999999.99;
        message.warning('调整金额不能小于-999,999.99元');
      } else if (value > 999999.99) {
        adjustForm.value.adjustmentsAmount = 999999.99;
        message.warning('调整金额不能大于999,999.99元');
      }
    }
  }

  // 验证调整金额格式
  function validateAdjustmentAmount(value) {
    if (value === null || value === undefined) {
      return false;
    }

    // 检查是否为数字
    if (isNaN(value)) {
      return false;
    }

    // 检查范围
    if (value < -999999.99 || value > 999999.99) {
      return false;
    }

    // 检查小数位数是否超过2位
    const decimalPart = value.toString().split('.')[1];
    if (decimalPart && decimalPart.length > 2) {
      return false;
    }

    return true;
  }

  // 确认调整金额
  async function confirmAdjust() {
    if (!currentRecord.value || !currentRecord.value.billId) return;

    // 验证输入
    if (adjustForm.value.adjustmentsAmount === 0 || adjustForm.value.adjustmentsAmount === null || adjustForm.value.adjustmentsAmount === undefined) {
      message.warning('请输入调整金额');
      return;
    }

    if (!validateAdjustmentAmount(adjustForm.value.adjustmentsAmount)) {
      message.warning('调整金额格式不正确，最多保留两位小数');
      return;
    }

    if (!adjustForm.value.adjustmentReason.trim()) {
      message.warning('请输入调整原因');
      return;
    }

    try {
      await billAdjustConsumption({
        billId: currentRecord.value.billId,
        adjustmentsAmount: Number(adjustForm.value.adjustmentsAmount),
        adjustmentReason: adjustForm.value.adjustmentReason,
      });
      message.success('调整金额成功');
      adjustModalVisible.value = false;
      tableApi.query();
    } catch (error) {
      console.error('调整金额失败:', error);
      message.error('调整金额失败，请稍后重试');
    }
  }

  // 取消调整
  function cancelAdjust() {
    adjustModalVisible.value = false;
    adjustForm.value.adjustmentsAmount = 0;
    adjustForm.value.adjustmentReason = '';
    currentRecord.value = null;
  }

  // 刷新表格数据
  function handleRefresh() {
    tableApi.query();
  }

  // 导出Excel
  function exportExcel() {
    Modal.confirm({
      title: '确认导出',
      content: '确定要导出当前筛选条件下的账单数据吗？',
      onOk: async () => {
        try {
          // 这里可以调用后端导出接口，或者使用前端导出库
          message.success('导出功能开发中，敬请期待');
        } catch (error) {
          console.error('导出失败:', error);
          message.error('导出失败，请稍后重试');
        }
      },
    });
  }


</script>

<style scoped>
/* 自定义样式 */
:deep(.vxe-table--render-default .vxe-body--row:hover) {
  background-color: #f5f5f5;
}
</style>
