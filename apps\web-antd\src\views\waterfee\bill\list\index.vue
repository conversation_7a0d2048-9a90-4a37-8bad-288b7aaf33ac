<template>
  <Page :auto-content-height="true">
    <BasicTable table-title="账单列表">
      <template #toolbar-tools>
        <Space>
          <Button @click="handleRefresh">
            <template #icon><ReloadOutlined /></template>
            刷新
          </Button>
          <Button type="primary" ghost @click="exportExcel">
            <template #icon><DownloadOutlined /></template>
            导出
          </Button>
        </Space>
      </template>
      <template #action="{ row }">
        <Space>
          <Button type="link" @click="handleView(row)">
            查看
          </Button>
          <Button type="link" @click="handlePay(row)" v-if="row.billStatus === 'ISSUED'">
            支付
          </Button>
          <Button type="link" @click="handleAdjust(row)" v-if="row.billStatus === 'DRAFT'">
            调整
          </Button>
        </Space>
      </template>
    </BasicTable>
  </Page>
</template>

<script lang="ts" setup>
  import { Button, Space, message, Modal } from 'ant-design-vue';
  import { ReloadOutlined, DownloadOutlined } from '@ant-design/icons-vue';
  import { useVbenVxeGrid } from '#/adapter/vxe-table';
  import { Page } from '@vben/common-ui';
  import { billList, billPay, billAdjustConsumption } from '#/api/waterfee/bill';
  import { columns, querySchema } from './data';
  import type { BillModel } from '#/api/waterfee/model/billModel';
  import { ref, onMounted } from 'vue';
  import { useRouter, useRoute } from 'vue-router';

  const router = useRouter();
  const route = useRoute();
  const meterBookId = ref<number | null>(null);

  // 支付表单
  const payForm = ref({
    amount: 0,
  });

  // 调整用量表单
  const adjustForm = ref({
    adjustmentVolume: 0,
    adjustmentReason: '',
  });

  // 表单配置
  const formOptions = {
    commonConfig: {
      componentProps: {
        allowClear: true,
      },
    },
    schema: querySchema(),
    wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
  };

  // 表格配置
  const gridOptions = {
    columns,
    height: 'auto',
    proxyConfig: {
      ajax: {
        query: async ({ page }: { page?: { currentPage: number; pageSize: number } }, formValues: Record<string, any> = {}) => {
          // 获取路由参数中的表册ID
          if (route.query.meterBookId) {
            meterBookId.value = Number(route.query.meterBookId);
          }

          // 构建查询参数
          const params: any = {
            pageNum: page?.currentPage,
            pageSize: page?.pageSize,
            ...formValues,
          };

          // 如果有表册ID，添加到查询参数中
          if (meterBookId.value) {
            params.meterBookId = meterBookId.value;
          }

          const resp = await billList(params)
          return resp;
        },
      },
    },
    id: 'waterfee-bill-list-index',
  };

  const [BasicTable, tableApi] = useVbenVxeGrid({
    formOptions,
    gridOptions,
  });

  // 组件挂载时初始化
  onMounted(() => {
    // 初始化表格数据
    tableApi.query();
  });

  // 查看账单详情
  function handleView(record: BillModel) {
    console.log("账单id：",record.billId);
    if (record.billId) {
      router.push(`/charges/detail?id=${record.billId}`);
    }
  }

  // 处理支付账单
  function handlePay(record: BillModel) {
    if (!record.billId) return;

    payForm.value.amount = record.balanceDue || 0;

    Modal.confirm({
      title: '支付账单',
      content: `账单编号: ${record.billNumber}\n账单总额: ${record.totalAmount} 元\n已支付金额: ${record.amountPaid} 元\n应付余额: ${record.balanceDue} 元`,
      onOk: async () => {
        try {
          await billPay(record.billId as number, payForm.value.amount);
          message.success('支付成功');
          tableApi.query();
        } catch (error) {
          console.error('支付失败:', error);
          message.error('支付失败，请稍后重试');
        }
      },
    });
  }

  // 处理调整用量
  function handleAdjust(record: BillModel) {
    console.log("账单id：",record.billId);
    if (!record.billId) return;

    adjustForm.value.adjustmentVolume = 0;
    adjustForm.value.adjustmentReason = '';

    Modal.confirm({
      title: '调整用量',
      width: 500,
      content: `账单编号: ${record.billNumber}\n当前用量: ${record.consumptionVolume} ${record.consumptionUnit}`,
      onOk: async () => {
        try {
          await billAdjustConsumption({
            billId: record.billId,
            adjustmentVolume: adjustForm.value.adjustmentVolume,
            adjustmentReason: adjustForm.value.adjustmentReason,
          });
          message.success('调整用量成功');
          tableApi.query();
        } catch (error) {
          console.error('调整用量失败:', error);
          message.error('调整用量失败，请稍后重试');
        }
      },
    });
  }

  // 刷新表格数据
  function handleRefresh() {
    tableApi.query();
  }

  // 导出Excel
  function exportExcel() {
    Modal.confirm({
      title: '确认导出',
      content: '确定要导出当前筛选条件下的账单数据吗？',
      onOk: async () => {
        try {
          // 这里可以调用后端导出接口，或者使用前端导出库
          message.success('导出功能开发中，敬请期待');
        } catch (error) {
          console.error('导出失败:', error);
          message.error('导出失败，请稍后重试');
        }
      },
    });
  }


</script>

<style scoped>
/* 自定义样式 */
:deep(.vxe-table--render-default .vxe-body--row:hover) {
  background-color: #f5f5f5;
}
</style>
