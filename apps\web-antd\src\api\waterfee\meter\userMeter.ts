import type { BasicFetchResult } from '#/api/types/common';
import { requestClient } from '#/api/request';
import { preserveBigInt } from '#/utils/json-bigint';
import type { MeterModel } from '#/api/waterfee/model/meter/meterModel';

// 用户水表API
enum Api {
  byUserNo = '/waterfee/meter/user', // 根据用户编号查询水表 
}

// 根据用户编号查询水表
export function getMetersByUserNo(userNo: string) {
  return requestClient.get<BasicFetchResult<MeterModel[]>>(`${Api.byUserNo}/${userNo}`);
} 
