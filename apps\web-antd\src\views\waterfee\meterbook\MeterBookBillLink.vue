<template>
  <Button type="link" @click="viewBills">
    查看账单
  </Button>
</template>

<script lang="ts" setup>
import { Button } from 'ant-design-vue';
import { useRouter } from 'vue-router';

const props = defineProps<{
  meterBookId: number | string;
}>();

const router = useRouter();

// 查看表册下的账单
function viewBills() {
  router.push(`/waterfee/bill/list?meterBookId=${props.meterBookId}`);
}
</script>