import type { VxeColumnProps } from 'vxe-table';
import type { FormSchemaGetter } from '@vben/vxe-grid';
import { h } from 'vue';
import { renderDict } from '#/utils/render';
import { getDictOptions } from '#/utils/dict';
import { businessAreaOptions, meterBookOptions, readerOptions } from './utils/options';

// 查询表单架构
export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入任务名称',
    },
    fieldName: 'taskName',
    label: '任务名称',
  },
  {
    component: 'TreeSelect',
    componentProps: {
      placeholder: '请选择营业区域',
      treeData: businessAreaOptions,
      allowClear: true,
      treeDefaultExpandAll: true,
      showSearch: true,
      dropdownStyle: { maxHeight: '400px', overflow: 'auto' },
      filterTreeNode: (input: string, treeNode: any) => {
        return treeNode.title.toLowerCase().indexOf(input.toLowerCase()) >= 0;
      },
    },
    fieldName: 'businessAreaId',
    label: '营业区域',
  },
  {
    component: 'Select',
    componentProps: {
      placeholder: '请选择抄表手册',
      options: meterBookOptions,
      allowClear: true,
    },
    fieldName: 'meterBookId',
    label: '抄表手册',
  },
  {
    component: 'Select',
    componentProps: {
      placeholder: '请选择抄表员',
      options: readerOptions,
      allowClear: true,
    },
    fieldName: 'readerId',
    label: '抄表员',
  },
  {
    component: 'Select',
    componentProps: {
      placeholder: '请选择抄表方式',
      options: getDictOptions('waterfee_reading_method'),
    },
    fieldName: 'readingMethod',
    label: '抄表方式',
  },
  {
    component: 'Select',
    componentProps: {
      placeholder: '请选择抄表周期',
      options: getDictOptions('waterfee_reading_cycle'),
    },
    fieldName: 'readingCycle',
    label: '抄表周期',
  },
];

// 表格列配置
export const columns: VxeColumnProps[] = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center',
  },
  {
    type: 'seq',
    title: '序号',
    width: 60,
    fixed: 'left',
    align: 'center',
    // 使用自定义渲染函数来显示序号
    slots: {
      default: ({ rowIndex }) => {
        return h('span', {}, rowIndex + 1);
      },
    },
  },
  {
    field: 'businessAreaName',
    title: '营业区域',
    minWidth: 120,
    align: 'center',
  },
  {
    field: 'meterBookName',
    title: '抄表手册',
    minWidth: 120,
    align: 'center',
  },
  {
    field: 'readerName',
    title: '抄表员',
    minWidth: 100,
    align: 'center',
  },
  {
    field: 'readingMethod',
    title: '抄表方式',
    minWidth: 100,
    align: 'center',
    slots: {
      default: ({ row }) => {
        return h('div', {}, renderDict(row.readingMethod, 'waterfee_reading_method'));
      },
    },
  },
  {
    field: 'readingCycle',
    title: '抄表周期',
    minWidth: 100,
    align: 'center',
    slots: {
      default: ({ row }) => {
        return h('div', {}, renderDict(row.readingCycle, 'waterfee_reading_cycle'));
      },
    },
  },
  {
    field: 'bookUserNum',
    title: '手册用户数',
    minWidth: 100,
    align: 'center',
  },
  {
    field: 'planReadingNum',
    title: '计划抄表数',
    minWidth: 100,
    align: 'center',
  },
  {
    field: 'readingDay',
    title: '抄表例日',
    minWidth: 100,
    align: 'center',
  },
  {
    field: 'baseDay',
    title: '抄表基准日',
    minWidth: 100,
    align: 'center',
  },
  {
    field: 'startDate',
    title: '任务下发时间',
    minWidth: 150,
    align: 'center',
  },
  {
    field: 'actualReadingNum',
    title: '实际抄表数',
    minWidth: 100,
    align: 'center',
  },
  // {
  //   field: 'readingRate',
  //   title: '抄见率',
  //   minWidth: 100,
  //   align: 'center',
  //   slots: {
  //     default: ({ row }) => {
  //       const rate = row.readingRate || 0;
  //       const color = rate < 80 ? '#ff4d4f' : (rate < 90 ? '#faad14' : '#52c41a');

  //       return h('div', {
  //         style: {
  //           color: color,
  //           fontWeight: 'bold',
  //         }
  //       }, `${rate}%`);
  //     },
  //   },
  // },
];
