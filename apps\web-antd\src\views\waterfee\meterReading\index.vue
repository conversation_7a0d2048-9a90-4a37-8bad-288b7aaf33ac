<script setup lang="ts">
import { ref, onMounted, h } from 'vue';
import { Page } from '@vben/common-ui';
import { Button, Space, message, Modal, Popconfirm } from 'ant-design-vue';
import { DownloadOutlined, ExclamationCircleOutlined, PlusOutlined } from '@ant-design/icons-vue';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { useVbenDrawer } from '@vben/common-ui';

import {
  getReadingTaskList,
  deleteReadingTask,
  exportReadingTask,
  pauseReadingTask,
  enableReadingTask,
  dispatchReadingTask,
} from '#/api/waterfee/meterReading';
import { columns, querySchema } from './reading.data';
import { preserveBigInt } from '#/utils/json-bigint';
import { commonDownloadExcel } from '#/utils/file/download';
import { useDictStore } from '#/store/dict';
import { getDictOptions } from '#/utils/dict';

// 引入组件
import ReadingTaskDrawer from './components/ReadingTaskDrawer.vue';

const dictStore = useDictStore();

// 表单配置
const formOptions = {
  commonConfig: {
    labelWidth: 80,
    componentProps: {
      allowClear: true,
    },
  },
  schema: querySchema(),
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
};

// 表格配置
const gridOptions = {
  columns,
  height: 'auto',
  width: '100%',
  fit: true,
  autoResize: true,
  tableLayout: 'auto',
  scrollX: {
    enabled: true,
    gt: 10,
  },
  resizable: true,
  resizeConfig: {
    minWidth: 80,
  },
  tooltipConfig: {
    showAll: true,
  },
  proxyConfig: {
    ajax: {
      query: async ({ page }: { page?: { currentPage: number; pageSize: number } }, formValues: Record<string, any> = {}) => {
        const resp = await getReadingTaskList({
          pageNum: page?.currentPage,
          pageSize: page?.pageSize,
          ...formValues,
        });
        return resp;
      },
    },
  },
  id: 'waterfee-meter-reading-index',
};

// 初始化表格
const [BasicTable, tableApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

// 抽屉状态
const drawerOpen = ref(false);
const drawerProps = ref({
  id: '',
  readonly: false,
});

// 添加抄表任务
function handleAdd() {
  drawerProps.value = {
    id: '',
    readonly: false,
  };
  drawerOpen.value = true;
}

// 编辑抄表任务
function handleEdit(row: Record<string, any>) {
  try {
    // 确保ID不会丢失精度
    const safeRecord = preserveBigInt(row);
    const taskId = String(safeRecord.taskId);

    console.log('编辑抄表任务, ID:', taskId);

    drawerProps.value = {
      id: taskId,
      readonly: false,
    };
    drawerOpen.value = true;
  } catch (error) {
    console.error('编辑抄表任务失败:', error);
    message.error(`编辑失败：${error.message || '未知错误'}`);
  }
}

// 删除抄表任务
function handleDelete(row: Record<string, any>) {
  Modal.confirm({
    title: '确认删除',
    icon: h(ExclamationCircleOutlined),
    content: `确定要删除该抄表任务吗？`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      try {
        await deleteReadingTask(row.taskId);
        message.success('删除成功');
        tableApi.query();
      } catch (error) {
        console.error('删除抄表任务失败:', error);
        message.error(`删除失败：${error.message || '未知错误'}`);
      }
    },
  });
}

// 批量删除
async function handleBatchDelete() {
  const selections = await tableApi.getSelections();
  if (selections.length === 0) {
    message.warning('请至少选择一条记录');
    return;
  }

  const taskIds = selections.map((item) => item.taskId);

  Modal.confirm({
    title: '确认批量删除',
    icon: h(ExclamationCircleOutlined),
    content: `确定要删除选中的 ${taskIds.length} 条抄表任务吗？`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      try {
        await deleteReadingTask(taskIds);
        message.success('批量删除成功');
        tableApi.query();
      } catch (error) {
        console.error('批量删除抄表任务失败:', error);
        message.error(`批量删除失败：${error.message || '未知错误'}`);
      }
    },
  });
}

// 暂停任务
async function handlePause(row: Record<string, any>) {
  try {
    await pauseReadingTask(row.taskId);
    message.success('暂停成功');
    tableApi.query();
  } catch (error) {
    console.error('暂停抄表任务失败:', error);
    message.error(`暂停失败：${error.message || '未知错误'}`);
  }
}

// 启用任务
async function handleEnable(row: Record<string, any>) {
  try {
    await enableReadingTask(row.taskId);
    message.success('启用成功');
    tableApi.query();
  } catch (error) {
    console.error('启用抄表任务失败:', error);
    message.error(`启用失败：${error.message || '未知错误'}`);
  }
}

// 下发任务
async function handleDispatch(row: Record<string, any>) {
  try {
    await dispatchReadingTask(row.taskId);
    message.success('下发成功');
    tableApi.query();
  } catch (error) {
    console.error('下发抄表任务失败:', error);
    message.error(`下发失败：${error.message || '未知错误'}`);
  }
}

// 导出数据
async function handleExport() {
  try {
    const formValues = await tableApi.getFormValues();

    // 创建额外的表头映射
    const dictHeaders = [
      { label: '抄表方式翻译', prop: 'readingMethodName' },
      { label: '抄表周期翻译', prop: 'readingCycleName' },
      { label: '任务状态翻译', prop: 'taskStatusName' },
    ];

    await commonDownloadExcel(exportReadingTask, '抄表任务数据', {
      ...formValues,
      headers: dictHeaders,
    });

    message.success('导出成功');
  } catch (error) {
    console.error('导出抄表任务数据失败:', error);
    message.error(`导出失败：${error.message || '未知错误'}`);
  }
}

// 查看抄表任务详情
function handleDetail(row: Record<string, any>) {
  try {
    // 确保ID不会丢失精度
    const safeRecord = preserveBigInt(row);
    const taskId = String(safeRecord.taskId);

    console.log('查看抄表任务详情, ID:', taskId);
    console.log('原始行数据:', row);
    console.log('处理后的行数据:', safeRecord);

    drawerProps.value = {
      id: taskId,
      readonly: true,
    };
    console.log('设置 drawerProps:', drawerProps.value);
    drawerOpen.value = true;
    console.log('设置 drawerOpen:', drawerOpen.value);
  } catch (error) {
    console.error('查看抄表任务详情失败:', error);
    message.error(`查看详情失败：${error.message || '未知错误'}`);
  }
}

// 处理成功回调
function handleSuccess() {
  tableApi.query();
}

const schemas = querySchema();

// 获取字典数据
const initDicts = async () => {
  try {
    const [readingMethodDict, readingCycleDict, taskStatusDict] =
      await Promise.all([
        getDictOptions('waterfee_reading_method'),
        getDictOptions('waterfee_reading_cycle'),
        getDictOptions('waterfee_task_status'),
      ]);

    for (const schema of schemas) {
      switch (schema.fieldName) {
        case 'readingMethod': {
          schema.componentProps.options = readingMethodDict;
          break;
        }
        case 'readingCycle': {
          schema.componentProps.options = readingCycleDict;
          break;
        }
        case 'taskStatus': {
          schema.componentProps.options = taskStatusDict;
          break;
        }
      }
    }
  } catch (error) {
    console.error('获取字典数据失败:', error);
  }
};

onMounted(() => {
  initDicts();
  tableApi.query();
});
</script>

<template>
  <Page :auto-content-height="true">
    <BasicTable @reload="tableApi.query()" table-title="抄表任务管理">
      <template #toolbar-tools>
        <Space>
          <Button type="primary" @click="handleAdd">
            <template #icon><PlusOutlined /></template>
            新建任务
          </Button>
          <!-- <Button type="primary" danger @click="handleBatchDelete">
            批量删除
          </Button>
          <Button type="primary" @click="handleExport">
            <template #icon><DownloadOutlined /></template>
            导出
          </Button> -->
        </Space>
      </template>
      <template #action="{ row }">
        <Space size="small">
          <Button
            type="link"
            size="small"
            style="padding: 0 4px; height: 22px;"
            @click="handleEdit(row)"
          >编辑</Button>
          <Button
            type="link"
            size="small"
            style="padding: 0 4px; height: 22px;"
            @click="handleDetail(row)"
          >详情</Button>
          <Button
            type="link"
            size="small"
            style="padding: 0 4px; height: 22px;"
            @click="handleDispatch(row)"
          >下发</Button>
          <Button
            v-if="row.taskStatus === '1'"
            type="link"
            size="small"
            style="padding: 0 4px; height: 22px;"
            @click="handlePause(row)"
          >暂停</Button>
          <Button
            v-if="row.taskStatus === '0'"
            type="link"
            size="small"
            style="padding: 0 4px; height: 22px;"
            @click="handleEnable(row)"
          >启用</Button>
          <Popconfirm
            placement="left"
            title="确认删除?"
            @confirm="handleDelete(row)"
          >
            <Button
              type="link"
              danger
              size="small"
              style="padding: 0 4px; height: 22px;"
              @click.stop=""
            >删除</Button>
          </Popconfirm>
        </Space>
      </template>
    </BasicTable>

    <!-- 抄表任务抽屉 -->
    <ReadingTaskDrawer
      v-if="drawerOpen"
      v-model:open="drawerOpen"
      :id="drawerProps.id"
      :readonly="drawerProps.readonly"
      @reload="handleSuccess"
    />
  </Page>
</template>

<style scoped>
/* 可以添加自定义样式 */
:deep(.vben-vxe-grid-toolbar) {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

:deep(.vben-vxe-grid-toolbar-left) {
  flex: 1;
}

/* 确保表格容器能够撑满可用宽度 */
:deep(.vxe-table--main-wrapper),
:deep(.vxe-grid--main-wrapper) {
  width: 100% !important;
}

/* 表格内容区自适应 */
:deep(.vxe-table--body-wrapper) {
  width: 100% !important;
}

/* 表格头部自适应 */
:deep(.vxe-table--header-wrapper) {
  width: 100% !important;
}

/* 确保表格行内容能够正确显示 */
:deep(.vxe-body--row) {
  width: 100%;
}

/* 文本溢出处理 */
:deep(.vxe-cell--label),
:deep(.vxe-cell--title) {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 允许单元格内容溢出显示工具提示 */
:deep(.vxe-cell) {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 响应式处理 */
@media (max-width: 1200px) {
  :deep(.vxe-table--fixed-left-wrapper),
  :deep(.vxe-table--fixed-right-wrapper) {
    /* 确保固定列在小屏上正确显示 */
    z-index: 10;
  }
}
</style>
