import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { getPopupContainer } from '@vben/utils';
import { h } from 'vue';
import { Tag } from 'ant-design-vue';
import { getDictOptions } from '#/utils/dict';
import { renderDict } from '#/utils/render';

// 查询表单模式
export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'userNo',
    label: '用户编号',
  },
  {
    component: 'Input',
    fieldName: 'userName',
    label: '用户名称',
  },
  {
    component: 'Input',
    fieldName: 'address',
    label: '用水地址',
  },
  {
    component: 'DatePicker',
    componentProps: {
      getPopupContainer,
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      format: 'YYYY-MM-DD HH:mm:ss',
      showTime: true,
    },
    fieldName: 'billingPeriodStart',
    label: '本期水费时间',
  },
];

// 表格列定义
export const columns: VxeGridProps['columns'] = [
  {
    type: 'checkbox',
    width: 60,
  },
  {
    title: '账单ID',
    field: 'billId',
    width: 120,
    align: 'center',
  },
  {
    title: '用户编号',
    field: 'userNo',
    align: 'center',
  },
  {
    title: '用户名称',
    field: 'userName',
    align: 'center',
  },
  {
    title: '用水地址',
    field: 'address',
    align: 'center',
  },
  {
    title: '本期水费时间',
    field: 'billingPeriodStart',
    align: 'center',
  },
  {
    title: '本期水费金额',
    field: 'totalAmount',
    align: 'center',
    formatter: ({ row }) => {
      return row.totalAmount ? `${row.totalAmount}` : '0';
    },
  },
  {
    title: '用水量',
    field: 'consumptionVolume',
    align: 'center',
    formatter: ({ row }) => {
      return row.consumptionVolume ? `${row.consumptionVolume}` : '0';
    },
  },
  {
    title: '服务金额',
    field: 'additionalChargeAmount',
    align: 'center',
    formatter: ({ row }) => {
      return row.additionalChargeAmount ? `${row.additionalChargeAmount}` : '0';
    },
  },
  {
    title: '操作',
    field: 'action',
    width: 150,
    fixed: 'right',
    slots: {
      default: 'action',
    },
  },
];
