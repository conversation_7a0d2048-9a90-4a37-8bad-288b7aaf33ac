<script setup lang="ts">
import type { LeaveVO } from './api/model';

import type { DescItem } from '#/components/description';

import { computed } from 'vue';

import { Description, useDescription } from '#/components/description';
import { renderDict } from '#/utils/render';

import { flowSchema } from './data';

// import dayjs from 'dayjs';

defineOptions({
  name: 'LeaveDescription',
  inheritAttrs: false,
});

const props = defineProps<{ data: LeaveVO }>();

// 获取字典名称
const getDictName = (fieldName: string) => {
  const dictMap: Record<string, string> = {
    customerNature: 'waterfee_user_customer_nature',
    useWaterNature: 'waterfee_user_use_water_nature',
    userStatus: 'waterfee_user_user_status',
    auditStatus: 'audit_status',
    certificateType: 'waterfee_user_certificate_type',
    invoiceType: 'waterfee_user_invoice_type',
    billingMethod: 'waterfee_user_billing_method',
    ifPenalty: 'yes_no',
    ifExtraCharge: 'yes_no',
    penaltyType: 'waterfee_user_penalty_type',
    priceUseWaterNature: 'waterfee_user_use_water_nature',
  };
  return dictMap[fieldName] || '';
};

// 构建描述项
const descSchema = computed<DescItem[]>(() => {
  return flowSchema()
    .filter((item) => item.fieldName !== 'userId') // 过滤掉不需要显示的字段
    .map((item) => {
      return {
        field: item.fieldName,
        label: item.label as string,
        span: 1,
        render: (val: any) => {
          const dictName = getDictName(item.fieldName);
          if (dictName) {
            return renderDict(val, dictName);
          }
          if (item.fieldName === 'supplyDate' && val) {
            return val;
          }
          return val || '-';
        },
      } satisfies DescItem;
    });
});

// 使用Description组件
const [registerDescription] = useDescription({
  column: 2,
  bordered: true,
  labelStyle: {
    fontWeight: 'bold',
    minWidth: '120px',
  },
  schema: descSchema.value,
  data: props.data,
});

// function formatDate(date: string) {
//   return dayjs(date).format('YYYY-MM-DD');
// }
</script>

<template>
  <Description @register="registerDescription" />
</template>
