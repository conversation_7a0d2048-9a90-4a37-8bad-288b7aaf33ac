import type { PageQuery } from '#/api/common';
import type {
  SurchargeConfigsForm,
  SurchargeConfigsQuery,
  SurchargeConfigsVO,
} from '#/api/waterfee/price/surchargeConfigs/model.d';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

enum Api {
  list = '/waterfee/surchargeConfigs/list',
  root = '/waterfee/surchargeConfigs',
}

/**
 * 附加费配置 (Surcharge Configurations)导出
 * @param data data
 * @returns void
 */
export function SurchargeConfigsExport(data: Partial<SurchargeConfigsForm>) {
  return commonExport(`${Api.root}/export`, data);
}

/**
 * 查询附加费配置 (Surcharge Configurations)列表
 * @param params 查询参数
 * @returns 附加费配置 (Surcharge Configurations)列表
 */
export function listSurchargeConfigs(
  params?: PageQuery & SurchargeConfigsQuery,
) {
  return requestClient.get<{ rows: SurchargeConfigsVO[]; total: number }>(
    Api.list,
    { params },
  );
}

/**
 * 查询附加费配置 (Surcharge Configurations)详细
 * @param id 附加费配置 (Surcharge Configurations)ID
 * @returns 附加费配置 (Surcharge Configurations)信息
 */
export function getSurchargeConfigs(id: number | string) {
  return requestClient.get<SurchargeConfigsForm>(`${Api.root}/${id}`);
}

/**
 * 新增附加费配置 (Surcharge Configurations)
 * @param data 新增数据
 * @returns void
 */
export function addSurchargeConfigs(data: SurchargeConfigsForm) {
  return requestClient.postWithMsg<void>(Api.root, data);
}

/**
 * 修改附加费配置 (Surcharge Configurations)
 * @param data 修改数据
 * @returns void
 */
export function updateSurchargeConfigs(data: SurchargeConfigsForm) {
  return requestClient.putWithMsg<void>(Api.root, data);
}

/**
 * 删除附加费配置 (Surcharge Configurations)
 * @param id 附加费配置 (Surcharge Configurations)ID或ID数组
 * @returns void
 */
export function delSurchargeConfigs(
  id: Array<number | string> | number | string,
) {
  return requestClient.deleteWithMsg<void>(`${Api.root}/${id}`);
}
