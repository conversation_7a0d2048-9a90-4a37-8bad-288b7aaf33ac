import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    path: '/waterfee/meter/mechanical',
    name: 'WaterfeeMechanicalMeter',
    component: () => import('#/views/waterfee/meter/mechanical/index.vue'),
    meta: {
      title: '机械水表管理',
    },
  },
  {
    path: '/waterfee/meter/mechanical/detail',
    name: 'WaterfeeMechanicalMeterDetail',
    component: () =>
      import('#/views/waterfee/meter/mechanical/detail/index.vue'),
    meta: {
      title: '机械水表详情',
      hideMenu: true,
    },
  },
];

export default routes;
