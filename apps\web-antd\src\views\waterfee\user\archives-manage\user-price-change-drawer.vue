<script setup lang="ts">
import type { UserPriceChangeRecordForm } from '#/api/waterfee/user/priceChangeRecord/model.d';

import { ref, watch } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';
import { cloneDeep } from '@vben/utils';

import { useVbenForm } from '#/adapter/form';
import { changePriceUser, getUser } from '#/api/waterfee/user/archivesManage';
import { getDictOptions } from '#/utils/dict';

const emit = defineEmits<{ reload: [] }>();

// 控制违约金和附加费选项的显示
const showPenaltyOptions = ref(false);
const showExtraChargeOptions = ref(false);

const [BasicForm, formApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'col-span-1',
  },
  layout: 'vertical',
  schema: [
    {
      component: 'Input',
      dependencies: {
        show: () => false,
        triggerFields: [''],
      },
      fieldName: 'userId',
      label: '用户ID',
    },
    {
      component: 'Select',
      fieldName: 'beforePriceUseWaterNature',
      label: '原价格-用水性质',
      componentProps: {
        disabled: true,
        options: getDictOptions('waterfee_user_use_water_nature'),
      },
    },
    {
      component: 'Select',
      fieldName: 'beforeBillingMethod',
      label: '原计费方式',
      componentProps: {
        disabled: true,
        options: getDictOptions('waterfee_user_billing_method'),
      },
    },
    {
      component: 'RadioGroup',
      fieldName: 'beforeIfPenalty',
      label: '原是否有违约金',
      componentProps: {
        disabled: true,
        buttonStyle: 'solid',
        options: getDictOptions('yes_no'),
        optionType: 'button',
      },
    },
    {
      component: 'Select',
      fieldName: 'beforePenaltyType',
      label: '原违约金类型',
      componentProps: {
        disabled: true,
        options: getDictOptions('waterfee_user_penalty_type'),
      },
      dependencies: {
        show: ({ values }) => values.beforeIfPenalty === 'Y',
        triggerFields: ['beforeIfPenalty'],
      },
    },
    {
      component: 'RadioGroup',
      fieldName: 'beforeIfExtraCharge',
      label: '原是否有附加费',
      componentProps: {
        disabled: true,
        buttonStyle: 'solid',
        options: getDictOptions('yes_no'),
        optionType: 'button',
      },
    },
    {
      component: 'Select',
      fieldName: 'beforeExtraChargeType',
      label: '原附加费内容',
      componentProps: {
        disabled: true,
        mode: 'multiple',
        options: getDictOptions('waterfee_user_extra_charge_type'),
      },
      dependencies: {
        show: ({ values }) => values.beforeIfExtraCharge === 'Y',
        triggerFields: ['beforeIfExtraCharge'],
      },
    },
    {
      component: 'Select',
      fieldName: 'afterPriceUseWaterNature',
      label: '新价格-用水性质',
      componentProps: {
        options: getDictOptions('waterfee_user_use_water_nature'),
      },
      rules: 'required',
    },
    {
      component: 'Select',
      fieldName: 'afterBillingMethod',
      label: '新计费方式',
      componentProps: {
        options: getDictOptions('waterfee_user_billing_method'),
      },
      rules: 'required',
    },
    {
      component: 'RadioGroup',
      fieldName: 'afterIfPenalty',
      label: '新是否有违约金',
      componentProps: {
        buttonStyle: 'solid',
        options: getDictOptions('yes_no'),
        optionType: 'button',
      },
      rules: 'required',
    },
    {
      component: 'Select',
      fieldName: 'afterPenaltyType',
      label: '新违约金类型',
      componentProps: {
        options: getDictOptions('waterfee_user_penalty_type'),
      },
      dependencies: {
        show: ({ values }) => values.afterIfPenalty === 'Y',
        triggerFields: ['afterIfPenalty'],
      },
      rules: 'required',
    },
    {
      component: 'RadioGroup',
      fieldName: 'afterIfExtraCharge',
      label: '新是否有附加费',
      componentProps: {
        buttonStyle: 'solid',
        options: getDictOptions('yes_no'),
        optionType: 'button',
      },
      rules: 'required',
    },
    {
      component: 'Select',
      fieldName: 'afterExtraChargeType',
      label: '新附加费内容',
      componentProps: {
        mode: 'multiple',
        options: getDictOptions('waterfee_user_extra_charge_type'),
      },
      dependencies: {
        show: ({ values }) => values.afterIfExtraCharge === 'Y',
        triggerFields: ['afterIfExtraCharge'],
      },
      rules: 'required',
    },
  ],
  showDefaultActions: false,
  wrapperClass: 'grid-cols-2 gap-x-4',
});

// 监听表单值变化
watch(
  () => formApi.form.values,
  (values) => {
    showPenaltyOptions.value = values.afterIfPenalty === 'Y';
    showExtraChargeOptions.value = values.afterIfExtraCharge === 'Y';
  },
  { deep: true },
);

const [BasicDrawer, drawerApi] = useVbenDrawer({
  title: '用水价格变更',
  onClosed: handleCancel,
  onConfirm: handleConfirm,
  onOpenChange: async (isOpen) => {
    if (!isOpen) {
      return null;
    }
    drawerApi.drawerLoading(true);
    const { row } = drawerApi.getData() as { row?: any };
    if (row && row.userId) {
      const record = await getUser(row.userId);
      if (!record) {
        drawerApi.drawerLoading(false);
        return;
      }
      // 设置原价格信息
      await formApi.setValues({
        userId: row.userId,
        beforePriceUseWaterNature: record.priceUseWaterNature,
        beforeBillingMethod: record.billingMethod,
        beforeIfPenalty: record.ifPenalty,
        beforePenaltyType: record.penaltyType,
        beforeIfExtraCharge: record.ifExtraCharge,
        beforeExtraChargeType: record.extraChargeType,
        // 默认新价格与原价格相同
        afterPriceUseWaterNature: record.priceUseWaterNature,
        afterBillingMethod: record.billingMethod,
        afterIfPenalty: record.ifPenalty,
        afterPenaltyType: record.penaltyType,
        afterIfExtraCharge: record.ifExtraCharge,
        afterExtraChargeType: record.extraChargeType,
      });

      // 初始化显示状态
      showPenaltyOptions.value = record.ifPenalty === 'Y';
      showExtraChargeOptions.value = record.ifExtraCharge === 'Y';
    }
    drawerApi.drawerLoading(false);
  },
});

async function handleConfirm() {
  try {
    drawerApi.drawerLoading(true);
    const { valid } = await formApi.validate();
    if (!valid) {
      return;
    }
    // 可能会做数据处理 使用cloneDeep深拷贝
    const data = cloneDeep(
      await formApi.getValues(),
    ) as UserPriceChangeRecordForm;
    await changePriceUser(data);
    emit('reload');
    await handleCancel();
  } catch (error) {
    console.error(error);
  } finally {
    drawerApi.drawerLoading(false);
  }
}

async function handleCancel() {
  drawerApi.close();
  await formApi.resetForm();
}
</script>

<template>
  <BasicDrawer :close-on-click-modal="false" class="w-[800px]">
    <BasicForm />
  </BasicDrawer>
</template>
