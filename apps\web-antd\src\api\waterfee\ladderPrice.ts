import type {
  LadderPriceListGetResultModel,
  LadderPriceModel,
  LadderPriceParams,
} from './model/ladderPriceModel';

import type { ID } from '#/api/common';

import { requestClient } from '#/api/request';

enum Api {
  ladderPriceList = '/waterfee/ladderPrice/list',
  root = '/waterfee/ladderPrice',
}

/**
 * 阶梯价格列表
 * @param params 查询参数
 * @returns 阶梯价格列表
 */
export function ladderPriceList(params?: LadderPriceParams) {
  return requestClient.get<LadderPriceListGetResultModel>(Api.ladderPriceList, {
    params,
  });
}

/**
 * 阶梯价格详情
 * @param id 阶梯价格ID
 * @returns 阶梯价格详情
 */
export function ladderPriceInfo(id: ID) {
  return requestClient.get<LadderPriceModel>(`${Api.root}/${id}`);
}

/**
 * 阶梯价格新增
 * @param data 参数
 */
export function ladderPriceAdd(data: Partial<LadderPriceModel>) {
  return requestClient.postWithMsg<void>(Api.root, data);
}

/**
 * 阶梯价格更新
 * @param data 参数
 */
export function ladderPriceUpdate(data: Partial<LadderPriceModel>) {
  return requestClient.putWithMsg<void>(Api.root, data);
}

/**
 * 阶梯价格删除
 * @param id 阶梯价格ID
 * @returns void
 */
export function ladderPriceRemove(id: ID) {
  return requestClient.deleteWithMsg<void>(`${Api.root}/${id}`);
}
