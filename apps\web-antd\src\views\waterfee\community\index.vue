<template>
  <Page :auto-content-height="true">
    <BasicTable table-title="社区管理">
      <template #toolbar-tools>
        <Space>
          <Button type="primary" @click="handleAdd">
            新增
          </Button>
        </Space>
      </template>
      <template #action="{ row }">
        <Space>
          <ghost-button @click="handleEdit(row)">
            编辑
          </ghost-button>
          <Popconfirm
            placement="left"
            title="确认删除?"
            @confirm="handleDelete(row)"
          >
            <ghost-button danger @click.stop="">
              删除
            </ghost-button>
          </Popconfirm>
        </Space>
      </template>
    </BasicTable>
    <CommunityDrawer @reload="tableApi.query()" />
  </Page>
</template>

<script lang="ts" setup>
  import { Button, Space, Popconfirm } from 'ant-design-vue';
  import { useVbenVxeGrid } from '#/adapter/vxe-table';
  import { useVbenDrawer } from '@vben/common-ui';
  import { Page } from '@vben/common-ui';
  import { communityList, communityRemove } from '#/api/waterfee/community';
  import { columns, querySchema } from './community.data';
  import type { CommunityModel } from '#/api/waterfee/model/communityModel';
  import type { ID } from '#/api/common';
  import communityDrawer from './community-drawer.vue';

  const formOptions = {
    commonConfig: {
      componentProps: {
        allowClear: true,
      },
    },
    schema: querySchema(),
    wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
  };

  const gridOptions = {
    columns,
    height: 'auto',
    proxyConfig: {
      ajax: {
        query: async ({ page }: { page?: { currentPage: number; pageSize: number } }, formValues: Record<string, any> = {}) => {
          // 只传递必要的搜索参数，避免请求过长
          const resp = await communityList({
            pageNum: page?.currentPage,
            pageSize: page?.pageSize,
            ...formValues,
          });
          
          return resp;
        },
      },
    },
    id: 'waterfee-community-index',
  };

  const [BasicTable, tableApi] = useVbenVxeGrid({
    formOptions,
    gridOptions,
  });
  
  const [CommunityDrawer, drawerApi] = useVbenDrawer({
    connectedComponent: communityDrawer,
  });

  function handleAdd() {
    drawerApi.setData({});
    drawerApi.open();
  }

  function handleEdit(record: CommunityModel) {
    drawerApi.setData({ id: record.id });
    drawerApi.open();
  }

  async function handleDelete(record: CommunityModel) {
    if (record.id) {
      await communityRemove(record.id as ID);
      await tableApi.query();
    }
  }
</script>
